@echo off
chcp 65001 >nul
title 🏪 موقع حراجنا - خادم محلي

color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🏪 موقع حراجنا                           ║
echo ║                  نظام الإعلانات المبوبة                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM التحقق من وجود PHP
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: PHP غير مثبت أو غير موجود في PATH
    echo.
    echo 💡 يرجى تثبيت PHP من: https://www.php.net/downloads
    echo    أو إضافة مجلد PHP إلى متغير البيئة PATH
    echo.
    pause
    exit /b 1
)

echo ✅ PHP متوفر - الإصدار:
php --version | findstr "PHP"
echo.

REM إنشاء المجلدات المطلوبة
echo 📁 إنشاء المجلدات المطلوبة...
if not exist "uploads" mkdir uploads
if not exist "uploads\ads" mkdir uploads\ads
if not exist "uploads\users" mkdir uploads\users
if not exist "logs" mkdir logs
if not exist "config" mkdir config
echo ✅ تم إنشاء المجلدات بنجاح
echo.

REM التحقق من المنفذ
echo 🔍 التحقق من توفر المنفذ 8081...
netstat -an | findstr ":8081" >nul
if %errorlevel% equ 0 (
    echo ⚠️  تحذير: المنفذ 8081 قد يكون مستخدماً
    echo.
)

echo 🚀 بدء تشغيل خادم حراجنا...
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      🌐 الروابط المهمة                      ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║ 🏠 الصفحة الرئيسية:                                        ║
echo ║    http://localhost:8081/pages/home.php                      ║
echo ║                                                              ║
echo ║ 🔧 تثبيت الموقع (أول مرة):                                 ║
echo ║    http://localhost:8081/install/install.php                 ║
echo ║                                                              ║
echo ║ 👨‍💼 لوحة تحكم الإدارة:                                      ║
echo ║    http://localhost:8081/admin/index.php                     ║
echo ║                                                              ║
echo ║ 📝 تسجيل الدخول:                                            ║
echo ║    http://localhost:8081/pages/login.php                     ║
echo ║                                                              ║
echo ║ ➕ إضافة إعلان جديد:                                        ║
echo ║    http://localhost:8081/pages/add-ad.php                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 بيانات الاختبار:
echo    👤 المدير - اسم المستخدم: admin / كلمة المرور: password
echo.
echo 💡 نصائح:
echo    • استخدم Ctrl+C لإيقاف الخادم
echo    • تأكد من تشغيل MySQL قبل الاستخدام
echo    • ابدأ بزيارة صفحة التثبيت أولاً
echo.
echo ════════════════════════════════════════════════════════════════
echo 🔄 الخادم يعمل الآن... (اضغط Ctrl+C للإيقاف)
echo ════════════════════════════════════════════════════════════════
echo.

REM تشغيل الخادم
php -S localhost:8081

echo.
echo 🛑 تم إيقاف خادم حراجنا
echo 👋 شكراً لاستخدام موقع حراجنا!
pause
