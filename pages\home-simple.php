<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

try {
    require_once $root_path . '/config/database.php';
    require_once $root_path . '/config/config.php';
    require_once $root_path . '/includes/functions.php';
} catch (Exception $e) {
    die("خطأ في تحميل الملفات: " . $e->getMessage());
}

$page_title = 'الصفحة الرئيسية - حراجنا';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="home.php">
                <i class="fas fa-store"></i> حراجنا
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="home.php">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="categories.php">الفئات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">اتصل بنا</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> <?= $_SESSION['full_name'] ?? 'المستخدم' ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="my-ads.php">إعلاناتي</a></li>
                                <li><a class="dropdown-item" href="favorites.php">المفضلة</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-success text-white ms-2" href="add-ad.php">
                                <i class="fas fa-plus"></i> أضف إعلان
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">تسجيل الدخول</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-success text-white ms-2" href="register.php">
                                إنشاء حساب
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container mt-4">
        <!-- Hero Section -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center py-5">
                        <h1 class="display-4 mb-3">مرحباً بك في حراجنا</h1>
                        <p class="lead">منصة الإعلانات المبوبة الأولى في المملكة العربية السعودية</p>
                        <div class="mt-4">
                            <a href="categories.php" class="btn btn-light btn-lg me-3">
                                <i class="fas fa-search"></i> تصفح الإعلانات
                            </a>
                            <?php if (!isset($_SESSION['user_id'])): ?>
                                <a href="register.php" class="btn btn-outline-light btn-lg">
                                    <i class="fas fa-user-plus"></i> إنشاء حساب
                                </a>
                            <?php else: ?>
                                <a href="add-ad.php" class="btn btn-outline-light btn-lg">
                                    <i class="fas fa-plus"></i> أضف إعلان
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الفئات الرئيسية -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-center mb-4">الفئات الرئيسية</h2>
                <div class="row">
                    <?php
                    $main_categories = [
                        ['name' => 'سيارات', 'icon' => 'fas fa-car', 'color' => 'primary'],
                        ['name' => 'عقارات', 'icon' => 'fas fa-home', 'color' => 'success'],
                        ['name' => 'أجهزة إلكترونية', 'icon' => 'fas fa-laptop', 'color' => 'info'],
                        ['name' => 'أثاث ومنزل', 'icon' => 'fas fa-couch', 'color' => 'warning'],
                        ['name' => 'أزياء وموضة', 'icon' => 'fas fa-tshirt', 'color' => 'danger'],
                        ['name' => 'وظائف', 'icon' => 'fas fa-briefcase', 'color' => 'dark'],
                    ];
                    
                    foreach ($main_categories as $category):
                    ?>
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="categories.php" class="text-decoration-none">
                                <div class="card text-center h-100 border-<?= $category['color'] ?>">
                                    <div class="card-body">
                                        <i class="<?= $category['icon'] ?> fa-2x text-<?= $category['color'] ?> mb-2"></i>
                                        <h6 class="card-title"><?= $category['name'] ?></h6>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-center mb-4">إحصائيات الموقع</h2>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3>1000+</h3>
                                <p>مستخدم مسجل</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-success text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-bullhorn fa-2x mb-2"></i>
                                <h3>500+</h3>
                                <p>إعلان نشط</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-info text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-eye fa-2x mb-2"></i>
                                <h3>10K+</h3>
                                <p>مشاهدة يومية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-warning text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-handshake fa-2x mb-2"></i>
                                <h3>200+</h3>
                                <p>صفقة ناجحة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- دعوة للعمل -->
        <div class="row">
            <div class="col-12">
                <div class="card bg-light">
                    <div class="card-body text-center py-5">
                        <h3>ابدأ رحلتك في البيع والشراء اليوم</h3>
                        <p class="lead text-muted">انضم إلى آلاف المستخدمين الذين يثقون بحراجنا</p>
                        <div class="mt-4">
                            <?php if (!isset($_SESSION['user_id'])): ?>
                                <a href="register.php" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-user-plus"></i> إنشاء حساب مجاني
                                </a>
                                <a href="login.php" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                                </a>
                            <?php else: ?>
                                <a href="add-ad.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-plus"></i> أضف إعلانك الأول
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-store"></i> حراجنا</h5>
                    <p>منصة الإعلانات المبوبة الأولى في المملكة العربية السعودية</p>
                </div>
                <div class="col-md-6">
                    <h5>روابط مهمة</h5>
                    <ul class="list-unstyled">
                        <li><a href="about.php" class="text-white-50">من نحن</a></li>
                        <li><a href="contact.php" class="text-white-50">اتصل بنا</a></li>
                        <li><a href="categories.php" class="text-white-50">الفئات</a></li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; 2024 حراجنا. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
