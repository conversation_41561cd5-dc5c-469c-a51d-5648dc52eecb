<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// تسجيل دخول تلقائي للاختبار
if (!isLoggedIn()) {
    $user = $db->fetch("SELECT * FROM users LIMIT 1");
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['logged_in'] = true;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل - حراجنا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .log-area { background: #f8f9fa; border: 1px solid #dee2e6; height: 200px; overflow-y: auto; padding: 10px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="text-center mb-4">
            <h1><i class="fas fa-vial text-primary"></i> اختبار شامل للمشاكل</h1>
            <p class="lead">اختبار متعدد للتأكد من حل جميع المشاكل</p>
        </div>

        <?php if (isLoggedIn()): ?>
            <div class="alert alert-info">
                <i class="fas fa-user"></i> مسجل الدخول كـ: <strong><?= $_SESSION['username'] ?></strong> (ID: <?= $_SESSION['user_id'] ?>)
            </div>

            <!-- اختبار 1: فحص هيكل قاعدة البيانات -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-database"></i> اختبار 1: فحص هيكل قاعدة البيانات</h5>
                </div>
                <div class="card-body">
                    <?php
                    $db_test_result = "success";
                    $db_message = "";
                    
                    try {
                        // فحص جدول reports
                        $reports_structure = $db->fetchAll("PRAGMA table_info(reports)");
                        $has_reported_ad_id = false;
                        $has_ad_id = false;
                        
                        foreach ($reports_structure as $col) {
                            if ($col['name'] === 'reported_ad_id') $has_reported_ad_id = true;
                            if ($col['name'] === 'ad_id') $has_ad_id = true;
                        }
                        
                        if ($has_reported_ad_id && !$has_ad_id) {
                            $db_message = "✅ جدول reports يحتوي على العمود الصحيح (reported_ad_id)";
                        } elseif ($has_ad_id && !$has_reported_ad_id) {
                            $db_test_result = "error";
                            $db_message = "❌ جدول reports يحتوي على العمود الخطأ (ad_id) بدلاً من (reported_ad_id)";
                        } elseif ($has_ad_id && $has_reported_ad_id) {
                            $db_test_result = "warning";
                            $db_message = "⚠️ جدول reports يحتوي على العمودين (يجب حذف ad_id)";
                        } else {
                            $db_test_result = "error";
                            $db_message = "❌ جدول reports لا يحتوي على أي من العمودين المطلوبين";
                        }
                        
                    } catch (Exception $e) {
                        $db_test_result = "error";
                        $db_message = "❌ خطأ في فحص قاعدة البيانات: " . $e->getMessage();
                    }
                    ?>
                    
                    <div class="alert test-<?= $db_test_result ?>">
                        <?= $db_message ?>
                    </div>
                    
                    <?php if ($db_test_result !== "success"): ?>
                        <button class="btn btn-danger" onclick="fixDatabase()">
                            <i class="fas fa-wrench"></i> إصلاح قاعدة البيانات
                        </button>
                        <div id="fix-result" class="mt-3"></div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- اختبار 2: اختبار إدراج البلاغات -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5><i class="fas fa-flag"></i> اختبار 2: إدراج البلاغات</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-warning" onclick="testReports()">
                        <i class="fas fa-play"></i> اختبار إدراج البلاغ
                    </button>
                    <div id="reports-result" class="mt-3"></div>
                </div>
            </div>

            <!-- اختبار 3: اختبار المفضلة -->
            <div class="card mb-4">
                <div class="card-header bg-danger text-white">
                    <h5><i class="fas fa-heart"></i> اختبار 3: المفضلة</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-danger" onclick="testFavorites()">
                        <i class="fas fa-play"></i> اختبار المفضلة
                    </button>
                    <div id="favorites-result" class="mt-3"></div>
                </div>
            </div>

            <!-- اختبار 4: اختبار متعدد للبلاغات -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-sync"></i> اختبار 4: اختبار متعدد للبلاغات</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-success" onclick="multipleReportsTest()">
                        <i class="fas fa-play"></i> اختبار 10 بلاغات متتالية
                    </button>
                    <div id="multiple-reports-result" class="mt-3"></div>
                    <div id="reports-log" class="log-area mt-3" style="display: none;"></div>
                </div>
            </div>

            <!-- اختبار 5: اختبار الصفحات الحقيقية -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-external-link-alt"></i> اختبار 5: الصفحات الحقيقية</h5>
                </div>
                <div class="card-body">
                    <p>اختبر البلاغات في الصفحات الحقيقية:</p>
                    <div class="row">
                        <?php
                        $test_ads = $db->fetchAll("SELECT id, title FROM ads LIMIT 4");
                        foreach ($test_ads as $ad):
                        ?>
                        <div class="col-md-3 mb-2">
                            <a href="pages/ad-details.php?id=<?= $ad['id'] ?>" target="_blank" class="btn btn-outline-info w-100">
                                <i class="fas fa-external-link-alt"></i><br>
                                إعلان <?= $ad['id'] ?>
                            </a>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

        <?php else: ?>
            <div class="alert alert-danger text-center">
                <h4><i class="fas fa-exclamation-triangle"></i> خطأ في تسجيل الدخول</h4>
                <p>لا يمكن إجراء الاختبارات بدون تسجيل الدخول</p>
                <a href="pages/login.php" class="btn btn-primary">تسجيل الدخول</a>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function fixDatabase() {
            document.getElementById('fix-result').innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> جاري الإصلاح...</div>';
            
            fetch('fix-reports-final.php')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('fix-result').innerHTML = '<div class="alert alert-success">' + data + '</div>';
                    setTimeout(() => location.reload(), 2000);
                })
                .catch(error => {
                    document.getElementById('fix-result').innerHTML = '<div class="alert alert-danger">خطأ: ' + error + '</div>';
                });
        }

        function testReports() {
            document.getElementById('reports-result').innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> جاري الاختبار...</div>';
            
            fetch('test-single-report.php')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('reports-result').innerHTML = data;
                })
                .catch(error => {
                    document.getElementById('reports-result').innerHTML = '<div class="alert alert-danger">خطأ: ' + error + '</div>';
                });
        }

        function testFavorites() {
            document.getElementById('favorites-result').innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> جاري الاختبار...</div>';
            
            fetch('pages/ajax/toggle-favorite.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ad_id: 1})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('favorites-result').innerHTML = '<div class="alert alert-success">✅ اختبار المفضلة نجح: ' + data.message + '</div>';
                } else {
                    document.getElementById('favorites-result').innerHTML = '<div class="alert alert-danger">❌ اختبار المفضلة فشل: ' + data.message + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('favorites-result').innerHTML = '<div class="alert alert-danger">❌ خطأ في الشبكة: ' + error + '</div>';
            });
        }

        function multipleReportsTest() {
            const resultDiv = document.getElementById('multiple-reports-result');
            const logDiv = document.getElementById('reports-log');
            
            resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> جاري اختبار 10 بلاغات...</div>';
            logDiv.style.display = 'block';
            logDiv.innerHTML = '';
            
            let successCount = 0;
            let errorCount = 0;
            
            for (let i = 1; i <= 10; i++) {
                fetch('test-single-report.php?test_id=' + i)
                    .then(response => response.text())
                    .then(data => {
                        if (data.includes('✅') || data.includes('نجح')) {
                            successCount++;
                            logDiv.innerHTML += `<div style="color: green;">اختبار ${i}: ✅ نجح</div>`;
                        } else {
                            errorCount++;
                            logDiv.innerHTML += `<div style="color: red;">اختبار ${i}: ❌ فشل - ${data}</div>`;
                        }
                        
                        if (successCount + errorCount === 10) {
                            if (errorCount === 0) {
                                resultDiv.innerHTML = '<div class="alert alert-success">🎉 جميع الاختبارات نجحت! (10/10)</div>';
                            } else {
                                resultDiv.innerHTML = `<div class="alert alert-warning">⚠️ نجح ${successCount} من 10 اختبارات</div>`;
                            }
                        }
                        
                        logDiv.scrollTop = logDiv.scrollHeight;
                    })
                    .catch(error => {
                        errorCount++;
                        logDiv.innerHTML += `<div style="color: red;">اختبار ${i}: ❌ خطأ في الشبكة</div>`;
                        logDiv.scrollTop = logDiv.scrollHeight;
                    });
            }
        }
    </script>
</body>
</html>
