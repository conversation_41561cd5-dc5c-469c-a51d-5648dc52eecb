<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h2>🔍 فحص دقيق لهيكل قاعدة البيانات</h2>";

try {
    echo "<h3>1. فحص جدول reports:</h3>";
    
    // فحص وجود الجدول
    $table_exists = $db->fetch("SELECT name FROM sqlite_master WHERE type='table' AND name='reports'");
    
    if ($table_exists) {
        echo "✅ جدول reports موجود<br>";
        
        // فحص هيكل الجدول
        $structure = $db->fetchAll("PRAGMA table_info(reports)");
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'><th>اسم العمود</th><th>النوع</th><th>NOT NULL</th><th>القيمة الافتراضية</th><th>Primary Key</th></tr>";
        
        $columns = [];
        foreach ($structure as $col) {
            $columns[] = $col['name'];
            $highlight = '';
            if ($col['name'] === 'ad_id') $highlight = 'background: #f8d7da;'; // أحمر للعمود الخطأ
            if ($col['name'] === 'reported_ad_id') $highlight = 'background: #d4edda;'; // أخضر للعمود الصحيح
            
            echo "<tr style='$highlight'>";
            echo "<td><strong>{$col['name']}</strong></td>";
            echo "<td>{$col['type']}</td>";
            echo "<td>" . ($col['notnull'] ? 'نعم' : 'لا') . "</td>";
            echo "<td>" . ($col['dflt_value'] ?: 'لا يوجد') . "</td>";
            echo "<td>" . ($col['pk'] ? 'نعم' : 'لا') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h4>تحليل الأعمدة:</h4>";
        echo "<ul>";
        if (in_array('ad_id', $columns)) {
            echo "<li style='color: red;'>❌ العمود <code>ad_id</code> موجود (هذا خطأ)</li>";
        }
        if (in_array('reported_ad_id', $columns)) {
            echo "<li style='color: green;'>✅ العمود <code>reported_ad_id</code> موجود (هذا صحيح)</li>";
        }
        if (!in_array('reported_ad_id', $columns) && in_array('ad_id', $columns)) {
            echo "<li style='color: orange;'>⚠️ يجب تغيير <code>ad_id</code> إلى <code>reported_ad_id</code></li>";
        }
        echo "</ul>";
        
        // عرض SQL لإنشاء الجدول
        $create_sql = $db->fetch("SELECT sql FROM sqlite_master WHERE type='table' AND name='reports'");
        if ($create_sql) {
            echo "<h4>SQL إنشاء الجدول:</h4>";
            echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
            echo htmlspecialchars($create_sql['sql']);
            echo "</pre>";
        }
        
    } else {
        echo "❌ جدول reports غير موجود<br>";
    }
    
    echo "<h3>2. اختبار الإدراج المباشر:</h3>";
    
    // جلب بيانات للاختبار
    $test_user = $db->fetch("SELECT id FROM users LIMIT 1");
    $test_ad = $db->fetch("SELECT id FROM ads LIMIT 1");
    
    if ($test_user && $test_ad) {
        echo "<p>بيانات الاختبار:</p>";
        echo "<ul>";
        echo "<li>معرف المستخدم: {$test_user['id']}</li>";
        echo "<li>معرف الإعلان: {$test_ad['id']}</li>";
        echo "</ul>";
        
        // اختبار 1: محاولة الإدراج بالطريقة الحالية
        echo "<h4>اختبار 1: الإدراج بـ reported_ad_id</h4>";
        try {
            $db->query("INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))",
                      [$test_user['id'], $test_user['id'], $test_ad['id'], 'test', 'اختبار 1']);
            echo "✅ نجح الإدراج بـ reported_ad_id<br>";
            
            // حذف البيانات التجريبية
            $db->query("DELETE FROM reports WHERE reason = 'اختبار 1'");
            
        } catch (Exception $e) {
            echo "❌ فشل الإدراج بـ reported_ad_id: " . $e->getMessage() . "<br>";
            
            // اختبار 2: محاولة الإدراج بـ ad_id
            echo "<h4>اختبار 2: الإدراج بـ ad_id</h4>";
            try {
                $db->query("INSERT INTO reports (user_id, reporter_id, ad_id, report_type, reason, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))",
                          [$test_user['id'], $test_user['id'], $test_ad['id'], 'test', 'اختبار 2']);
                echo "✅ نجح الإدراج بـ ad_id<br>";
                echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
                echo "<strong>⚠️ المشكلة محددة:</strong> الجدول يستخدم <code>ad_id</code> لكن الكود يحاول الإدراج في <code>reported_ad_id</code>";
                echo "</div>";
                
                // حذف البيانات التجريبية
                $db->query("DELETE FROM reports WHERE reason = 'اختبار 2'");
                
            } catch (Exception $e) {
                echo "❌ فشل الإدراج بـ ad_id أيضاً: " . $e->getMessage() . "<br>";
            }
        }
        
    } else {
        echo "❌ لا توجد بيانات مستخدمين أو إعلانات للاختبار<br>";
    }
    
    echo "<h3>3. الحل المطلوب:</h3>";
    
    if ($table_exists && in_array('ad_id', $columns) && !in_array('reported_ad_id', $columns)) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>🚨 المشكلة مؤكدة:</h4>";
        echo "<p>الجدول يحتوي على عمود <code>ad_id</code> لكن الكود يحاول الإدراج في <code>reported_ad_id</code></p>";
        echo "<p><strong>الحلول الممكنة:</strong></p>";
        echo "<ol>";
        echo "<li>تغيير الكود ليستخدم <code>ad_id</code> بدلاً من <code>reported_ad_id</code></li>";
        echo "<li>تغيير الجدول ليحتوي على <code>reported_ad_id</code> بدلاً من <code>ad_id</code></li>";
        echo "</ol>";
        echo "</div>";
        
        echo "<h4>تطبيق الحل الثاني (تغيير الجدول):</h4>";
        echo "<button onclick='fixDatabase()' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>إصلاح قاعدة البيانات</button>";
        echo "<div id='fix-result'></div>";
        
    } elseif ($table_exists && in_array('reported_ad_id', $columns)) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ الجدول صحيح:</h4>";
        echo "<p>الجدول يحتوي على العمود الصحيح <code>reported_ad_id</code></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ خطأ في الفحص:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>🔗 روابط للاختبار:</h3>";
echo "<a href='pages/ad-details.php?id=1' target='_blank' style='margin: 5px; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px;'>اختبار البلاغ - إعلان 1</a>";
echo "<a href='pages/ad-details.php?id=2' target='_blank' style='margin: 5px; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px;'>اختبار البلاغ - إعلان 2</a>";
echo "<a href='pages/ad-details.php?id=3' target='_blank' style='margin: 5px; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px;'>اختبار البلاغ - إعلان 3</a>";
echo "<a href='pages/ad-details.php?id=4' target='_blank' style='margin: 5px; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px;'>اختبار البلاغ - إعلان 4</a>";
?>

<script>
function fixDatabase() {
    document.getElementById('fix-result').innerHTML = '<p>جاري الإصلاح...</p>';
    
    fetch('fix-reports-final.php')
        .then(response => response.text())
        .then(data => {
            document.getElementById('fix-result').innerHTML = data;
        })
        .catch(error => {
            document.getElementById('fix-result').innerHTML = '<p style="color: red;">خطأ: ' + error + '</p>';
        });
}
</script>
