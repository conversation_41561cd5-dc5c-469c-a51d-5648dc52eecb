<?php
class Database {
    private $dbfile = 'database/harajuna.db';
    private $pdo;

    public function __construct() {
        $this->connect();
    }

    private function connect() {
        try {
            // إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
            $dbDir = dirname($this->dbfile);
            if (!is_dir($dbDir)) {
                mkdir($dbDir, 0755, true);
            }

            $dsn = "sqlite:" . $this->dbfile;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];

            $this->pdo = new PDO($dsn, null, null, $options);

            // إنشاء الجداول إذا لم تكن موجودة
            $this->createTables();

        } catch (PDOException $e) {
            die('فشل في الاتصال بقاعدة البيانات: ' . $e->getMessage());
        }
    }

    private function createTables() {
        // إنشاء جدول المستخدمين
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                city VARCHAR(50),
                user_type VARCHAR(10) DEFAULT 'user',
                is_active INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ");

        // إنشاء جدول الفئات
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                parent_id INTEGER DEFAULT NULL,
                icon VARCHAR(50),
                is_active INTEGER DEFAULT 1,
                sort_order INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ");

        // إنشاء جدول الإعلانات
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS ads (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                category_id INTEGER NOT NULL,
                title VARCHAR(200) NOT NULL,
                description TEXT NOT NULL,
                price DECIMAL(10,2),
                currency VARCHAR(3) DEFAULT 'SAR',
                city VARCHAR(50) NOT NULL,
                phone VARCHAR(20) NOT NULL,
                images TEXT,
                is_featured INTEGER DEFAULT 0,
                is_sold INTEGER DEFAULT 0,
                is_active INTEGER DEFAULT 1,
                views_count INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ");

        // إضافة مستخدم إدمن افتراضي
        $adminExists = $this->pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'admin'")->fetchColumn();
        if ($adminExists == 0) {
            $hashedPassword = password_hash('password', PASSWORD_DEFAULT);
            $stmt = $this->pdo->prepare("
                INSERT INTO users (username, email, password, full_name, user_type)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute(['admin', '<EMAIL>', $hashedPassword, 'مدير الموقع', 'admin']);
        }

        // إضافة بعض الفئات الأساسية
        $categoriesCount = $this->pdo->query("SELECT COUNT(*) FROM categories")->fetchColumn();
        if ($categoriesCount == 0) {
            $basicCategories = [
                ['سيارات', '🚗'],
                ['عقارات', '🏠'],
                ['أجهزة إلكترونية', '📱'],
                ['أثاث ومنزل', '🛋️'],
                ['أزياء وموضة', '👕'],
                ['وظائف', '💼'],
                ['خدمات', '🔧'],
                ['حيوانات وطيور', '🐱'],
                ['رياضة وترفيه', '⚽'],
                ['أخرى', '📦']
            ];

            $stmt = $this->pdo->prepare("INSERT INTO categories (name, icon) VALUES (?, ?)");
            foreach ($basicCategories as $category) {
                $stmt->execute($category);
            }
        }
    }
    
    public function getConnection() {
        return $this->pdo;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            throw new Exception('خطأ في قاعدة البيانات: ' . $e->getMessage());
        }
    }
    
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
}

// إنشاء اتصال عام
$db = new Database();
?>
