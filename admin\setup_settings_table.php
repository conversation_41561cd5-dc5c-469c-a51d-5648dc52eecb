<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدمن
requireAdmin();

try {
    // إنشاء جدول الإعدادات
    $db->query("
        CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE,
            value TEXT,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // التحقق من وجود إعدادات
    $existing_settings = $db->fetch("SELECT COUNT(*) as count FROM settings")['count'];
    
    if ($existing_settings == 0) {
        // إدراج الإعدادات الافتراضية
        $default_settings = [
            ['maintenance_mode', '0', 'وضع الصيانة (0 = مغلق، 1 = مفعل)'],
            ['maintenance_message', 'الموقع تحت الصيانة، سنعود قريباً', 'رسالة الصيانة'],
            ['site_name', 'حراجنا', 'اسم الموقع'],
            ['site_description', 'منصة الإعلانات المبوبة الأولى في المملكة العربية السعودية', 'وصف الموقع'],
            ['contact_email', '<EMAIL>', 'بريد التواصل'],
            ['contact_phone', '+966 50 000 0000', 'هاتف التواصل'],
            ['max_images_per_ad', '10', 'الحد الأقصى للصور في الإعلان'],
            ['ad_expiry_days', '30', 'مدة انتهاء الإعلان بالأيام'],
            ['auto_approve_ads', '1', 'الموافقة التلقائية على الإعلانات (0 = لا، 1 = نعم)'],
            ['featured_ad_price', '50', 'سعر الإعلان المميز'],
        ];
        
        foreach ($default_settings as $setting) {
            $db->query(
                "INSERT INTO settings (name, value, description) VALUES (?, ?, ?)",
                $setting
            );
        }
        
        echo "تم إنشاء جدول الإعدادات وإدراج " . count($default_settings) . " إعداد افتراضي بنجاح!";
    } else {
        echo "جدول الإعدادات موجود مسبقاً ويحتوي على {$existing_settings} إعداد.";
    }
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
