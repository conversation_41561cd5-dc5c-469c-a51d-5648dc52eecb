<?php
echo "✅ PHP يعمل بشكل صحيح!<br>";
echo "📅 التاريخ والوقت: " . date('Y-m-d H:i:s') . "<br>";
echo "🐘 إصدار PHP: " . PHP_VERSION . "<br>";

// اختبار تحميل الملفات
echo "<hr>";
echo "<h3>🔍 اختبار تحميل الملفات:</h3>";

try {
    require_once '../config/config.php';
    echo "✅ config.php تم تحميله بنجاح<br>";
} catch (Exception $e) {
    echo "❌ خطأ في config.php: " . $e->getMessage() . "<br>";
}

try {
    require_once '../config/database.php';
    echo "✅ database.php تم تحميله بنجاح<br>";
} catch (Exception $e) {
    echo "❌ خطأ في database.php: " . $e->getMessage() . "<br>";
}

try {
    require_once '../includes/functions.php';
    echo "✅ functions.php تم تحميله بنجاح<br>";
} catch (Exception $e) {
    echo "❌ خطأ في functions.php: " . $e->getMessage() . "<br>";
}

try {
    require_once '../includes/auth.php';
    echo "✅ auth.php تم تحميله بنجاح<br>";
} catch (Exception $e) {
    echo "❌ خطأ في auth.php: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h3>🔗 الروابط:</h3>";
echo '<a href="/">الصفحة الرئيسية</a><br>';
echo '<a href="/install/install.php">صفحة التثبيت</a><br>';
echo '<a href="/pages/home.php">الصفحة الرئيسية للموقع</a><br>';
echo '<a href="/admin/index.php">لوحة الإدارة</a><br>';
?>
