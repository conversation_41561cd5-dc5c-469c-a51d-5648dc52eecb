/* ملف CSS بسيط لموقع حراجنا */

/* متغيرات CSS */
:root {
    --primary-color: #667eea;
    --primary-dark: #5a6fd8;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #2c3e50;
    --navy-color: #2c3e50;
    --border-radius: 10px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

/* الخط الأساسي */
body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fc;
    line-height: 1.6;
    direction: rtl;
}

/* شريط التنقل */
.navbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
}

.navbar-nav .nav-link {
    color: rgba(255,255,255,0.9) !important;
    font-weight: 500;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    background-color: rgba(255,255,255,0.1);
    color: white !important;
}

/* البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
    background: white;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.card-header {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 20px;
    font-weight: 600;
}

.card-body {
    padding: 25px;
}

/* بطاقات الإعلانات */
.ad-card {
    position: relative;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
}

.ad-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.ad-card .ad-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.ad-card .ad-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.ad-card:hover .ad-image img {
    transform: scale(1.05);
}

.ad-card .no-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #f8f9fa;
    color: #6c757d;
}

.ad-card .no-image i {
    font-size: 3rem;
    margin-bottom: 0.5rem;
}

.ad-card .ad-content {
    padding: 1rem;
}

.ad-card .ad-title {
    margin-bottom: 0.5rem;
}

.ad-card .ad-title a {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 600;
}

.ad-card .ad-title a:hover {
    color: var(--primary-color);
}

.ad-card .ad-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--success-color);
    margin-bottom: 0.5rem;
}

.ad-card .ad-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.ad-card .ad-category {
    font-size: 0.8rem;
    color: var(--primary-color);
}

/* بطاقات الفئات */
.category-card {
    display: block;
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    text-align: center;
    text-decoration: none;
    color: inherit;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    color: inherit;
    text-decoration: none;
}

.category-card .category-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.category-card .category-name {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.category-card .category-count {
    font-size: 0.9rem;
    color: #6c757d;
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-color) 100%);
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* النماذج */
.form-control,
.form-select {
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    padding: 0.75rem;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 20px;
    font-weight: 500;
    box-shadow: var(--box-shadow);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* الفوتر */
.footer {
    background: var(--navy-color);
    color: white;
    padding: 3rem 0 1rem 0;
    margin-top: 4rem;
}

.footer h5 {
    color: white;
    margin-bottom: 1rem;
}

.footer a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer a:hover {
    color: white;
}

/* استجابة للجوال */
@media (max-width: 768px) {
    .ad-card .ad-image {
        height: 180px;
    }
    
    .category-card {
        padding: 20px;
    }
}

/* تحسينات إضافية */
.text-primary { color: var(--primary-color) !important; }
.bg-primary { background-color: var(--primary-color) !important; }
.border-primary { border-color: var(--primary-color) !important; }
