<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// تسجيل دخول تلقائي
if (!isLoggedIn()) {
    $user = $db->fetch("SELECT * FROM users LIMIT 1");
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['logged_in'] = true;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص شامل للمشاكل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; border-radius: 5px; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1><i class="fas fa-stethoscope text-primary"></i> تشخيص شامل للمشاكل</h1>
        
        <?php if (isLoggedIn()): ?>
            <div class="info">
                <i class="fas fa-user-check"></i> مسجل الدخول كـ: <strong><?= $_SESSION['username'] ?></strong> (ID: <?= $_SESSION['user_id'] ?>)
            </div>

            <!-- تشخيص 1: جدول البلاغات -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5><i class="fas fa-flag"></i> تشخيص 1: جدول البلاغات (reports)</h5>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        $reports_structure = $db->fetchAll("PRAGMA table_info(reports)");
                        $reports_columns = array_column($reports_structure, 'name');
                        
                        echo "<p><strong>الأعمدة الموجودة:</strong> " . implode(', ', $reports_columns) . "</p>";
                        
                        $has_ad_id = in_array('ad_id', $reports_columns);
                        $has_reported_ad_id = in_array('reported_ad_id', $reports_columns);
                        
                        if ($has_reported_ad_id && !$has_ad_id) {
                            echo "<div class='success'>✅ جدول reports صحيح: يحتوي على reported_ad_id</div>";
                            
                            // اختبار الإدراج
                            $test_ad = $db->fetch("SELECT id FROM ads LIMIT 1");
                            if ($test_ad) {
                                try {
                                    $test_reason = "تشخيص - " . date('Y-m-d H:i:s');
                                    $db->query("INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))",
                                              [$_SESSION['user_id'], $_SESSION['user_id'], $test_ad['id'], 'test', $test_reason]);
                                    
                                    $inserted = $db->fetch("SELECT id FROM reports WHERE reason = ?", [$test_reason]);
                                    if ($inserted) {
                                        echo "<div class='success'>✅ اختبار الإدراج نجح</div>";
                                        $db->query("DELETE FROM reports WHERE id = ?", [$inserted['id']]);
                                    }
                                } catch (Exception $e) {
                                    echo "<div class='error'>❌ اختبار الإدراج فشل: " . $e->getMessage() . "</div>";
                                }
                            }
                        } elseif ($has_ad_id && !$has_reported_ad_id) {
                            echo "<div class='error'>❌ جدول reports خطأ: يحتوي على ad_id بدلاً من reported_ad_id</div>";
                            echo "<button class='btn btn-danger' onclick='fixReports()'>إصلاح جدول البلاغات</button>";
                        } else {
                            echo "<div class='warning'>⚠️ جدول reports يحتوي على مشكلة في الأعمدة</div>";
                        }
                        
                    } catch (Exception $e) {
                        echo "<div class='error'>❌ خطأ في فحص جدول reports: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>

            <!-- تشخيص 2: جدول المفضلة -->
            <div class="card mb-4">
                <div class="card-header bg-danger text-white">
                    <h5><i class="fas fa-heart"></i> تشخيص 2: جدول المفضلة (favorites)</h5>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        $favorites_structure = $db->fetchAll("PRAGMA table_info(favorites)");
                        
                        if (empty($favorites_structure)) {
                            echo "<div class='error'>❌ جدول favorites غير موجود</div>";
                            echo "<button class='btn btn-danger' onclick='createFavoritesTable()'>إنشاء جدول المفضلة</button>";
                        } else {
                            $favorites_columns = array_column($favorites_structure, 'name');
                            echo "<p><strong>الأعمدة الموجودة:</strong> " . implode(', ', $favorites_columns) . "</p>";
                            
                            $required_columns = ['id', 'user_id', 'ad_id', 'created_at'];
                            $missing_columns = array_diff($required_columns, $favorites_columns);
                            
                            if (empty($missing_columns)) {
                                echo "<div class='success'>✅ جدول favorites صحيح</div>";
                                
                                // اختبار الإدراج
                                $test_ad = $db->fetch("SELECT id FROM ads LIMIT 1");
                                if ($test_ad) {
                                    try {
                                        // حذف أي مفضلة سابقة
                                        $db->query("DELETE FROM favorites WHERE user_id = ? AND ad_id = ?", 
                                                  [$_SESSION['user_id'], $test_ad['id']]);
                                        
                                        // إضافة للمفضلة
                                        $db->query("INSERT INTO favorites (user_id, ad_id, created_at) VALUES (?, ?, datetime('now'))", 
                                                  [$_SESSION['user_id'], $test_ad['id']]);
                                        
                                        $inserted = $db->fetch("SELECT id FROM favorites WHERE user_id = ? AND ad_id = ?", 
                                                              [$_SESSION['user_id'], $test_ad['id']]);
                                        if ($inserted) {
                                            echo "<div class='success'>✅ اختبار المفضلة نجح</div>";
                                            $db->query("DELETE FROM favorites WHERE id = ?", [$inserted['id']]);
                                        }
                                    } catch (Exception $e) {
                                        echo "<div class='error'>❌ اختبار المفضلة فشل: " . $e->getMessage() . "</div>";
                                    }
                                }
                            } else {
                                echo "<div class='error'>❌ جدول favorites ناقص الأعمدة: " . implode(', ', $missing_columns) . "</div>";
                            }
                        }
                        
                    } catch (Exception $e) {
                        echo "<div class='error'>❌ خطأ في فحص جدول favorites: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>

            <!-- تشخيص 3: ملف toggle-favorite.php -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-code"></i> تشخيص 3: ملف toggle-favorite.php</h5>
                </div>
                <div class="card-body">
                    <?php
                    $toggle_file = 'pages/ajax/toggle-favorite.php';
                    if (file_exists($toggle_file)) {
                        echo "<div class='success'>✅ ملف toggle-favorite.php موجود</div>";
                        
                        $file_content = file_get_contents($toggle_file);
                        
                        // فحص المشاكل الشائعة
                        $checks = [
                            'application/json' => strpos($file_content, 'application/json') !== false,
                            'datetime(\'now\')' => strpos($file_content, 'datetime(\'now\')') !== false,
                            'json_encode' => strpos($file_content, 'json_encode') !== false,
                            'isLoggedIn()' => strpos($file_content, 'isLoggedIn()') !== false
                        ];
                        
                        foreach ($checks as $check => $passed) {
                            if ($passed) {
                                echo "<div class='success'>✅ $check موجود</div>";
                            } else {
                                echo "<div class='error'>❌ $check مفقود</div>";
                            }
                        }
                        
                        // اختبار AJAX
                        echo "<button class='btn btn-info' onclick='testAjax()'>اختبار AJAX</button>";
                        echo "<div id='ajax-result' class='mt-2'></div>";
                        
                    } else {
                        echo "<div class='error'>❌ ملف toggle-favorite.php غير موجود</div>";
                    }
                    ?>
                </div>
            </div>

            <!-- تشخيص 4: صفحة ad-details.php -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-file-code"></i> تشخيص 4: صفحة ad-details.php</h5>
                </div>
                <div class="card-body">
                    <?php
                    $ad_details_file = 'pages/ad-details.php';
                    if (file_exists($ad_details_file)) {
                        echo "<div class='success'>✅ ملف ad-details.php موجود</div>";
                        
                        // فحص الكود
                        $file_content = file_get_contents($ad_details_file);
                        
                        if (strpos($file_content, 'reported_ad_id') !== false) {
                            echo "<div class='success'>✅ الكود يستخدم reported_ad_id</div>";
                        } else {
                            echo "<div class='error'>❌ الكود لا يستخدم reported_ad_id</div>";
                        }
                        
                        if (strpos($file_content, 'datetime(\'now\')') !== false) {
                            echo "<div class='success'>✅ الكود يستخدم datetime('now')</div>";
                        } else {
                            echo "<div class='warning'>⚠️ الكود قد يستخدم NOW() بدلاً من datetime('now')</div>";
                        }
                        
                    } else {
                        echo "<div class='error'>❌ ملف ad-details.php غير موجود</div>";
                    }
                    ?>
                    
                    <div class="mt-3">
                        <a href="pages/ad-details.php?id=4" target="_blank" class="btn btn-success">
                            <i class="fas fa-external-link-alt"></i> اختبار الصفحة الحقيقية
                        </a>
                    </div>
                </div>
            </div>

            <!-- النتيجة النهائية -->
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5><i class="fas fa-clipboard-check"></i> النتيجة النهائية</h5>
                </div>
                <div class="card-body text-center">
                    <button class="btn btn-primary btn-lg" onclick="runFullDiagnosis()">
                        <i class="fas fa-play"></i> تشغيل التشخيص الكامل
                    </button>
                    <div id="full-diagnosis-result" class="mt-3"></div>
                </div>
            </div>

        <?php else: ?>
            <div class="error">
                <h4>خطأ في تسجيل الدخول</h4>
                <p>لا يمكن إجراء التشخيص بدون تسجيل الدخول</p>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function fixReports() {
            fetch('fix-both-problems.php')
                .then(response => response.text())
                .then(data => {
                    alert('تم تشغيل إصلاح البلاغات');
                    location.reload();
                })
                .catch(error => {
                    alert('خطأ: ' + error);
                });
        }
        
        function createFavoritesTable() {
            fetch('fix-both-problems.php')
                .then(response => response.text())
                .then(data => {
                    alert('تم تشغيل إنشاء جدول المفضلة');
                    location.reload();
                })
                .catch(error => {
                    alert('خطأ: ' + error);
                });
        }
        
        function testAjax() {
            const resultDiv = document.getElementById('ajax-result');
            resultDiv.innerHTML = '<div class="info"><i class="fas fa-spinner fa-spin"></i> جاري اختبار AJAX...</div>';
            
            fetch('pages/ajax/toggle-favorite.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ad_id: 1})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ AJAX يعمل: ' + data.message + '</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ AJAX لا يعمل: ' + data.message + '</div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="error">❌ خطأ في AJAX: ' + error + '</div>';
            });
        }
        
        function runFullDiagnosis() {
            const resultDiv = document.getElementById('full-diagnosis-result');
            resultDiv.innerHTML = '<div class="info"><i class="fas fa-spinner fa-spin"></i> جاري التشخيص الكامل...</div>';
            
            // تشغيل جميع الاختبارات
            setTimeout(() => {
                resultDiv.innerHTML = '<div class="success"><h4>✅ تم التشخيص الكامل</h4><p>راجع النتائج أعلاه</p></div>';
            }, 2000);
        }
    </script>
</body>
</html>
