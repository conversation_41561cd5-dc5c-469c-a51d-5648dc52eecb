<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// تسجيل دخول تجريبي للاختبار
if (!isLoggedIn()) {
    // البحث عن أول مستخدم
    $user = $db->fetch("SELECT * FROM users LIMIT 1");
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['logged_in'] = true;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المفضلة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>🧪 اختبار المفضلة</h2>
        
        <?php if (isLoggedIn()): ?>
            <div class="alert alert-success">
                ✅ تم تسجيل الدخول كـ: <?= $_SESSION['username'] ?>
            </div>
            
            <?php
            // جلب بعض الإعلانات للاختبار
            $ads = $db->fetchAll("SELECT * FROM ads WHERE status = 'active' LIMIT 5");
            ?>
            
            <?php if ($ads): ?>
                <h3>الإعلانات المتاحة:</h3>
                <div class="row">
                    <?php foreach ($ads as $ad): ?>
                        <?php
                        // التحقق من وجود الإعلان في المفضلة
                        $is_favorite = $db->fetch("SELECT id FROM favorites WHERE user_id = ? AND ad_id = ?", 
                                                 [$_SESSION['user_id'], $ad['id']]);
                        ?>
                        <div class="col-md-6 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title"><?= htmlspecialchars($ad['title']) ?></h5>
                                    <p class="card-text"><?= htmlspecialchars(substr($ad['description'], 0, 100)) ?>...</p>
                                    <p class="text-success fw-bold"><?= number_format($ad['price']) ?> ريال</p>
                                    
                                    <button class="btn <?= $is_favorite ? 'btn-danger' : 'btn-outline-danger' ?> add-to-favorites" 
                                            data-ad-id="<?= $ad['id'] ?>">
                                        <i class="<?= $is_favorite ? 'fas' : 'far' ?> fa-heart"></i>
                                        <?= $is_favorite ? 'مضاف للمفضلة' : 'أضف للمفضلة' ?>
                                    </button>
                                    
                                    <a href="pages/ad-details.php?id=<?= $ad['id'] ?>" class="btn btn-primary">
                                        عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <h3>المفضلة الحالية:</h3>
                <div id="current-favorites">
                    <?php
                    $favorites = $db->fetchAll("
                        SELECT f.*, a.title, a.price 
                        FROM favorites f 
                        JOIN ads a ON f.ad_id = a.id 
                        WHERE f.user_id = ?
                    ", [$_SESSION['user_id']]);
                    ?>
                    
                    <?php if ($favorites): ?>
                        <ul class="list-group">
                            <?php foreach ($favorites as $fav): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <?= htmlspecialchars($fav['title']) ?> - <?= number_format($fav['price']) ?> ريال
                                    <button class="btn btn-sm btn-danger remove-favorite" data-ad-id="<?= $fav['ad_id'] ?>">
                                        إزالة
                                    </button>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php else: ?>
                        <p class="text-muted">لا توجد إعلانات في المفضلة</p>
                    <?php endif; ?>
                </div>
                
            <?php else: ?>
                <div class="alert alert-warning">
                    لا توجد إعلانات للاختبار. <a href="install/install.php">قم بإعداد الموقع أولاً</a>
                </div>
            <?php endif; ?>
            
        <?php else: ?>
            <div class="alert alert-danger">
                لم يتم تسجيل الدخول. <a href="pages/login.php">سجل الدخول</a>
            </div>
        <?php endif; ?>
        
        <hr>
        <h3>سجل الأحداث:</h3>
        <div id="log" class="alert alert-info">
            جاهز للاختبار...
        </div>
        
        <a href="test-fixes.php" class="btn btn-secondary">العودة للاختبارات</a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
    function log(message) {
        const logDiv = document.getElementById('log');
        logDiv.innerHTML = new Date().toLocaleTimeString() + ': ' + message + '<br>' + logDiv.innerHTML;
    }
    
    // إضافة/إزالة من المفضلة
    $('.add-to-favorites, .remove-favorite').on('click', function(e) {
        e.preventDefault();
        const adId = $(this).data('ad-id');
        const button = $(this);
        
        log('إرسال طلب للإعلان #' + adId);
        
        $.post('pages/ajax/toggle-favorite.php', {ad_id: adId}, function(response) {
            log('استجابة الخادم: ' + JSON.stringify(response));
            
            if (response.success) {
                if (response.added) {
                    log('✅ تم إضافة الإعلان للمفضلة');
                    button.html('<i class="fas fa-heart"></i> مضاف للمفضلة');
                    button.removeClass('btn-outline-danger').addClass('btn-danger');
                } else {
                    log('✅ تم إزالة الإعلان من المفضلة');
                    button.html('<i class="far fa-heart"></i> أضف للمفضلة');
                    button.removeClass('btn-danger').addClass('btn-outline-danger');
                }
                
                // تحديث قائمة المفضلة
                setTimeout(function() {
                    location.reload();
                }, 1000);
                
            } else {
                log('❌ خطأ: ' + response.message);
                alert('خطأ: ' + response.message);
            }
        }, 'json').fail(function(xhr, status, error) {
            log('❌ خطأ في الطلب: ' + error);
            log('استجابة الخادم: ' + xhr.responseText);
            alert('خطأ في الاتصال: ' + error);
        });
    });
    </script>
</body>
</html>
