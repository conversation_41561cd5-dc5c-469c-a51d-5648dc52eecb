<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدمن
requireAdmin();

$error = '';
$success = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'];
        
        switch ($action) {
            case 'add':
                $name = sanitize($_POST['name']);
                $arabic_name = sanitize($_POST['arabic_name']);
                $is_popular = isset($_POST['is_popular']) ? 1 : 0;
                $sort_order = (int)$_POST['sort_order'];
                
                $db->query(
                    "INSERT INTO car_brands (name, arabic_name, is_popular, sort_order) VALUES (?, ?, ?, ?)",
                    [$name, $arabic_name, $is_popular, $sort_order]
                );
                $success = 'تم إضافة الماركة بنجاح';
                break;
                
            case 'edit':
                $id = (int)$_POST['id'];
                $name = sanitize($_POST['name']);
                $arabic_name = sanitize($_POST['arabic_name']);
                $is_popular = isset($_POST['is_popular']) ? 1 : 0;
                $is_active = isset($_POST['is_active']) ? 1 : 0;
                $sort_order = (int)$_POST['sort_order'];
                
                $db->query(
                    "UPDATE car_brands SET name = ?, arabic_name = ?, is_popular = ?, is_active = ?, sort_order = ? WHERE id = ?",
                    [$name, $arabic_name, $is_popular, $is_active, $sort_order, $id]
                );
                $success = 'تم تحديث الماركة بنجاح';
                break;
                
            case 'delete':
                $id = (int)$_POST['id'];
                $db->query("DELETE FROM car_brands WHERE id = ?", [$id]);
                $success = 'تم حذف الماركة بنجاح';
                break;
                
            case 'toggle_status':
                $id = (int)$_POST['id'];
                $db->query("UPDATE car_brands SET is_active = NOT is_active WHERE id = ?", [$id]);
                $success = 'تم تغيير حالة الماركة بنجاح';
                break;
        }
        
        // إعادة توجيه لتجنب إعادة الإرسال
        header('Location: car-brands.php');
        exit();
        
    } catch (Exception $e) {
        $error = 'حدث خطأ: ' . $e->getMessage();
    }
}

// جلب الماركات
try {
    $search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
    $status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : 'all';
    
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(name LIKE ? OR arabic_name LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if ($status_filter !== 'all') {
        if ($status_filter === 'active') {
            $where_conditions[] = "is_active = 1";
        } elseif ($status_filter === 'inactive') {
            $where_conditions[] = "is_active = 0";
        } elseif ($status_filter === 'popular') {
            $where_conditions[] = "is_popular = 1";
        }
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    $brands = $db->fetchAll("SELECT * FROM car_brands $where_clause ORDER BY sort_order ASC, name ASC", $params);
    
    // إحصائيات
    $stats = [
        'total' => $db->fetch("SELECT COUNT(*) as count FROM car_brands")['count'],
        'active' => $db->fetch("SELECT COUNT(*) as count FROM car_brands WHERE is_active = 1")['count'],
        'popular' => $db->fetch("SELECT COUNT(*) as count FROM car_brands WHERE is_popular = 1")['count'],
    ];
    
} catch (Exception $e) {
    $error = 'خطأ في جلب البيانات: ' . $e->getMessage();
    $brands = [];
    $stats = ['total' => 0, 'active' => 0, 'popular' => 0];
}

$page_title = 'إدارة ماركات السيارات';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة ماركات السيارات</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBrandModal">
                        <i class="fas fa-plus"></i> إضافة ماركة جديدة
                    </button>
                </div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?= $error ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?= $success ?>
                </div>
            <?php endif; ?>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-4 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-car fa-2x mb-2"></i>
                            <h4><?= number_format($stats['total']) ?></h4>
                            <p class="mb-0">إجمالي الماركات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                            <h4><?= number_format($stats['active']) ?></h4>
                            <p class="mb-0">نشطة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-star fa-2x mb-2"></i>
                            <h4><?= number_format($stats['popular']) ?></h4>
                            <p class="mb-0">شائعة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?= htmlspecialchars($search) ?>" 
                                   placeholder="ابحث في اسم الماركة">
                        </div>
                        <div class="col-md-4">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="all" <?= $status_filter === 'all' ? 'selected' : '' ?>>جميع الحالات</option>
                                <option value="active" <?= $status_filter === 'active' ? 'selected' : '' ?>>نشطة</option>
                                <option value="inactive" <?= $status_filter === 'inactive' ? 'selected' : '' ?>>غير نشطة</option>
                                <option value="popular" <?= $status_filter === 'popular' ? 'selected' : '' ?>>شائعة</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block w-100">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول الماركات -->
            <div class="card">
                <div class="card-body">
                    <?php if (empty($brands)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-car fa-3x text-muted mb-3"></i>
                            <h5>لا توجد ماركات</h5>
                            <p class="text-muted">لم يتم العثور على أي ماركات</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الترتيب</th>
                                        <th>الشعار</th>
                                        <th>الاسم</th>
                                        <th>الاسم العربي</th>
                                        <th>شائعة</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($brands as $brand): ?>
                                        <tr>
                                            <td><?= $brand['sort_order'] ?></td>
                                            <td>
                                                <?php if ($brand['logo_path']): ?>
                                                    <img src="../assets/images/car-brands/<?= $brand['logo_path'] ?>" 
                                                         alt="<?= htmlspecialchars($brand['name']) ?>" 
                                                         width="40" height="40" class="rounded">
                                                <?php else: ?>
                                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                                         style="width: 40px; height: 40px;">
                                                        <i class="fas fa-car text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= htmlspecialchars($brand['name']) ?></td>
                                            <td><?= htmlspecialchars($brand['arabic_name']) ?></td>
                                            <td>
                                                <?php if ($brand['is_popular']): ?>
                                                    <span class="badge bg-warning">شائعة</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">عادية</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($brand['is_active']): ?>
                                                    <span class="badge bg-success">نشطة</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">غير نشطة</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            onclick="editBrand(<?= htmlspecialchars(json_encode($brand)) ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    
                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="action" value="toggle_status">
                                                        <input type="hidden" name="id" value="<?= $brand['id'] ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-warning" 
                                                                title="تغيير الحالة">
                                                            <i class="fas fa-toggle-<?= $brand['is_active'] ? 'on' : 'off' ?>"></i>
                                                        </button>
                                                    </form>
                                                    
                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="action" value="delete">
                                                        <input type="hidden" name="id" value="<?= $brand['id'] ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                                onclick="return confirm('هل أنت متأكد من حذف هذه الماركة؟')" 
                                                                title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- نموذج إضافة ماركة جديدة -->
<div class="modal fade" id="addBrandModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة ماركة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">

                    <div class="mb-3">
                        <label for="add_name" class="form-label">اسم الماركة (بالإنجليزية)</label>
                        <input type="text" class="form-control" id="add_name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="add_arabic_name" class="form-label">اسم الماركة (بالعربية)</label>
                        <input type="text" class="form-control" id="add_arabic_name" name="arabic_name" required>
                    </div>

                    <div class="mb-3">
                        <label for="add_sort_order" class="form-label">ترتيب العرض</label>
                        <input type="number" class="form-control" id="add_sort_order" name="sort_order" value="0">
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="add_is_popular" name="is_popular">
                        <label class="form-check-label" for="add_is_popular">
                            ماركة شائعة (تظهر في الصفحة الرئيسية)
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة الماركة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج تعديل ماركة -->
<div class="modal fade" id="editBrandModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الماركة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="id" id="edit_id">

                    <div class="mb-3">
                        <label for="edit_name" class="form-label">اسم الماركة (بالإنجليزية)</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_arabic_name" class="form-label">اسم الماركة (بالعربية)</label>
                        <input type="text" class="form-control" id="edit_arabic_name" name="arabic_name" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_sort_order" class="form-label">ترتيب العرض</label>
                        <input type="number" class="form-control" id="edit_sort_order" name="sort_order">
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="edit_is_popular" name="is_popular">
                        <label class="form-check-label" for="edit_is_popular">
                            ماركة شائعة (تظهر في الصفحة الرئيسية)
                        </label>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="edit_is_active" name="is_active">
                        <label class="form-check-label" for="edit_is_active">
                            ماركة نشطة
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editBrand(brand) {
    document.getElementById('edit_id').value = brand.id;
    document.getElementById('edit_name').value = brand.name;
    document.getElementById('edit_arabic_name').value = brand.arabic_name;
    document.getElementById('edit_sort_order').value = brand.sort_order;
    document.getElementById('edit_is_popular').checked = brand.is_popular == 1;
    document.getElementById('edit_is_active').checked = brand.is_active == 1;

    new bootstrap.Modal(document.getElementById('editBrandModal')).show();
}
</script>

<?php include 'includes/footer.php'; ?>
