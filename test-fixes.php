<?php
// اختبار الإصلاحات
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

echo "<h2>🧪 اختبار الإصلاحات</h2>";

try {
    // التحقق من وجود المستخدمين والإعلانات
    $users = $db->fetchAll("SELECT id, username FROM users LIMIT 3");
    $ads = $db->fetchAll("SELECT id, title FROM ads WHERE status = 'active' LIMIT 3");
    
    echo "<h3>1. البيانات المتاحة للاختبار</h3>";
    echo "<h4>المستخدمون:</h4>";
    if ($users) {
        echo "<ul>";
        foreach ($users as $user) {
            echo "<li>ID: {$user['id']} - {$user['username']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>❌ لا توجد مستخدمين</p>";
    }
    
    echo "<h4>الإعلانات:</h4>";
    if ($ads) {
        echo "<ul>";
        foreach ($ads as $ad) {
            echo "<li>ID: {$ad['id']} - {$ad['title']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>❌ لا توجد إعلانات</p>";
    }
    
    if ($users && $ads) {
        $test_user = $users[0];
        $test_ad = $ads[0];
        
        echo "<h3>2. اختبار البلاغات</h3>";
        
        // محاولة إدراج بلاغ
        try {
            $db->query("
                INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason) 
                VALUES (?, ?, ?, ?, ?)
            ", [
                $test_user['id'],
                $test_user['id'],
                $test_ad['id'],
                'spam',
                'اختبار البلاغ - سيتم حذفه'
            ]);
            
            echo "✅ تم إدراج البلاغ بنجاح<br>";
            
            // حذف البلاغ التجريبي
            $db->query("DELETE FROM reports WHERE reason = 'اختبار البلاغ - سيتم حذفه'");
            echo "✅ تم حذف البلاغ التجريبي<br>";
            
        } catch (Exception $e) {
            echo "❌ خطأ في البلاغات: " . $e->getMessage() . "<br>";
        }
        
        echo "<h3>3. اختبار المفضلة</h3>";
        
        // محاولة إضافة للمفضلة
        try {
            $db->query("
                INSERT OR IGNORE INTO favorites (user_id, ad_id) 
                VALUES (?, ?)
            ", [$test_user['id'], $test_ad['id']]);
            
            echo "✅ تم إضافة الإعلان للمفضلة<br>";
            
            // التحقق من وجود المفضلة
            $favorite = $db->fetch("SELECT id FROM favorites WHERE user_id = ? AND ad_id = ?", 
                                  [$test_user['id'], $test_ad['id']]);
            
            if ($favorite) {
                echo "✅ تم العثور على المفضلة في قاعدة البيانات<br>";
                
                // حذف المفضلة
                $db->query("DELETE FROM favorites WHERE user_id = ? AND ad_id = ?", 
                          [$test_user['id'], $test_ad['id']]);
                echo "✅ تم حذف المفضلة التجريبية<br>";
            } else {
                echo "❌ لم يتم العثور على المفضلة<br>";
            }
            
        } catch (Exception $e) {
            echo "❌ خطأ في المفضلة: " . $e->getMessage() . "<br>";
        }
        
        echo "<h3>4. اختبار صفحة تفاصيل الإعلان</h3>";
        echo "<p>اختبر الصفحة التالية:</p>";
        echo "<a href='pages/ad-details.php?id={$test_ad['id']}' target='_blank' class='btn btn-primary'>
                اختبار صفحة الإعلان #{$test_ad['id']}
              </a>";
        
        echo "<h3>5. تسجيل دخول تجريبي</h3>";
        echo "<p>لاختبار المفضلة والبلاغات، تحتاج لتسجيل الدخول:</p>";
        echo "<a href='pages/login.php' target='_blank' class='btn btn-success'>تسجيل الدخول</a>";
        echo "<p><small>جرب: admin / password</small></p>";
        
    } else {
        echo "<h3>⚠️ تحذير</h3>";
        echo "<p>لا توجد بيانات كافية للاختبار. تحتاج لإنشاء مستخدمين وإعلانات أولاً.</p>";
        echo "<a href='install/install.php' target='_blank' class='btn btn-warning'>إعداد الموقع</a>";
    }
    
    echo "<h3>6. ملخص الحالة</h3>";
    
    // فحص الجداول
    $tables_status = [];
    
    $required_tables = ['users', 'ads', 'reports', 'favorites', 'messages'];
    foreach ($required_tables as $table) {
        try {
            $count = $db->fetch("SELECT COUNT(*) as count FROM $table")['count'];
            $tables_status[$table] = "✅ موجود ($count سجل)";
        } catch (Exception $e) {
            $tables_status[$table] = "❌ غير موجود أو خطأ";
        }
    }
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>الجدول</th><th>الحالة</th></tr>";
    foreach ($tables_status as $table => $status) {
        echo "<tr><td>$table</td><td>$status</td></tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ خطأ في الاختبار:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<ul>";
echo "<li><a href='fix-database-issues.php' target='_blank'>إصلاح قاعدة البيانات</a></li>";
echo "<li><a href='debug.php' target='_blank'>تشخيص المشاكل</a></li>";
echo "<li><a href='pages/home.php' target='_blank'>الصفحة الرئيسية</a></li>";
echo "<li><a href='admin/index.php' target='_blank'>لوحة الإدارة</a></li>";
echo "</ul>";

echo "<style>
.btn {
    display: inline-block;
    padding: 8px 16px;
    margin: 5px;
    text-decoration: none;
    border-radius: 4px;
    color: white;
}
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn-warning { background: #ffc107; color: black; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
</style>";
?>
