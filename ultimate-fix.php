<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h2>🔧 الحل النهائي والقاطع</h2>";

try {
    echo "<h3>الخطوة 1: فحص الوضع الحالي</h3>";
    
    // فحص الجدول الحالي
    $table_info = $db->fetchAll("PRAGMA table_info(reports)");
    $columns = array_column($table_info, 'name');
    
    echo "<p><strong>الأعمدة الموجودة:</strong> " . implode(', ', $columns) . "</p>";
    
    $has_ad_id = in_array('ad_id', $columns);
    $has_reported_ad_id = in_array('reported_ad_id', $columns);
    
    echo "<ul>";
    echo "<li>ad_id موجود: " . ($has_ad_id ? "✅ نعم" : "❌ لا") . "</li>";
    echo "<li>reported_ad_id موجود: " . ($has_reported_ad_id ? "✅ نعم" : "❌ لا") . "</li>";
    echo "</ul>";
    
    if ($has_ad_id && !$has_reported_ad_id) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>🚨 المشكلة مؤكدة!</h4>";
        echo "<p>الجدول يحتوي على <code>ad_id</code> لكن الكود يحاول الإدراج في <code>reported_ad_id</code></p>";
        echo "</div>";
        
        echo "<h3>الخطوة 2: حفظ البيانات الموجودة</h3>";
        $existing_reports = $db->fetchAll("SELECT * FROM reports");
        echo "<p>✅ تم حفظ " . count($existing_reports) . " بلاغ موجود</p>";
        
        echo "<h3>الخطوة 3: إعادة إنشاء الجدول</h3>";
        
        // حذف الجدول القديم
        $db->query("DROP TABLE reports");
        echo "<p>✅ تم حذف الجدول القديم</p>";
        
        // إنشاء الجدول الجديد بالهيكل الصحيح
        $db->query("
            CREATE TABLE reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                reporter_id INTEGER NOT NULL,
                reported_ad_id INTEGER NOT NULL,
                report_type TEXT NOT NULL,
                reason TEXT,
                status TEXT DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (reporter_id) REFERENCES users(id),
                FOREIGN KEY (reported_ad_id) REFERENCES ads(id)
            )
        ");
        echo "<p>✅ تم إنشاء الجدول الجديد بالهيكل الصحيح</p>";
        
        echo "<h3>الخطوة 4: استعادة البيانات</h3>";
        $restored = 0;
        foreach ($existing_reports as $report) {
            try {
                $ad_id = $report['ad_id'] ?? null;
                if ($ad_id) {
                    $db->query("
                        INSERT INTO reports (id, user_id, reporter_id, reported_ad_id, report_type, reason, status, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ", [
                        $report['id'],
                        $report['user_id'],
                        $report['reporter_id'] ?? $report['user_id'],
                        $ad_id, // استخدام ad_id القديم كـ reported_ad_id الجديد
                        $report['report_type'] ?? 'other',
                        $report['reason'] ?? '',
                        $report['status'] ?? 'pending',
                        $report['created_at'] ?? date('Y-m-d H:i:s')
                    ]);
                    $restored++;
                }
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ تعذر استعادة بلاغ: " . $e->getMessage() . "</p>";
            }
        }
        echo "<p>✅ تم استعادة $restored بلاغ</p>";
        
    } elseif ($has_reported_ad_id && !$has_ad_id) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ الجدول صحيح!</h4>";
        echo "<p>الجدول يحتوي على العمود الصحيح <code>reported_ad_id</code></p>";
        echo "</div>";
        
    } elseif ($has_ad_id && $has_reported_ad_id) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>⚠️ الجدول يحتوي على العمودين!</h4>";
        echo "<p>سيتم حذف العمود الخطأ <code>ad_id</code></p>";
        echo "</div>";
        
        // إعادة إنشاء الجدول بدون ad_id
        $existing_reports = $db->fetchAll("SELECT * FROM reports");
        
        $db->query("DROP TABLE reports");
        $db->query("
            CREATE TABLE reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                reporter_id INTEGER NOT NULL,
                reported_ad_id INTEGER NOT NULL,
                report_type TEXT NOT NULL,
                reason TEXT,
                status TEXT DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (reporter_id) REFERENCES users(id),
                FOREIGN KEY (reported_ad_id) REFERENCES ads(id)
            )
        ");
        
        foreach ($existing_reports as $report) {
            $ad_id = $report['reported_ad_id'] ?? $report['ad_id'] ?? null;
            if ($ad_id) {
                $db->query("
                    INSERT INTO reports (id, user_id, reporter_id, reported_ad_id, report_type, reason, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ", [
                    $report['id'],
                    $report['user_id'],
                    $report['reporter_id'] ?? $report['user_id'],
                    $ad_id,
                    $report['report_type'] ?? 'other',
                    $report['reason'] ?? '',
                    $report['status'] ?? 'pending',
                    $report['created_at'] ?? date('Y-m-d H:i:s')
                ]);
            }
        }
        echo "<p>✅ تم تنظيف الجدول وإزالة العمود الخطأ</p>";
    }
    
    echo "<h3>الخطوة 5: فحص الجدول الجديد</h3>";
    $new_structure = $db->fetchAll("PRAGMA table_info(reports)");
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'><th>اسم العمود</th><th>النوع</th><th>NOT NULL</th></tr>";
    foreach ($new_structure as $col) {
        $highlight = ($col['name'] === 'reported_ad_id') ? 'background: #d4edda;' : '';
        echo "<tr style='$highlight'>";
        echo "<td><strong>{$col['name']}</strong></td>";
        echo "<td>{$col['type']}</td>";
        echo "<td>" . ($col['notnull'] ? 'نعم' : 'لا') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>الخطوة 6: اختبار نهائي</h3>";
    
    // جلب بيانات للاختبار
    $test_user = $db->fetch("SELECT id FROM users LIMIT 1");
    $test_ad = $db->fetch("SELECT id FROM ads LIMIT 1");
    
    if ($test_user && $test_ad) {
        try {
            // اختبار الإدراج بنفس الطريقة المستخدمة في الكود
            $db->query("
                INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, created_at) 
                VALUES (?, ?, ?, ?, ?, datetime('now'))
            ", [
                $test_user['id'], 
                $test_user['id'], 
                $test_ad['id'], 
                'test', 
                'اختبار نهائي - ' . date('Y-m-d H:i:s')
            ]);
            
            $inserted = $db->fetch("SELECT * FROM reports WHERE reason LIKE 'اختبار نهائي%' ORDER BY id DESC LIMIT 1");
            if ($inserted) {
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>🎉 نجح الاختبار النهائي!</h4>";
                echo "<p><strong>تفاصيل البلاغ المُدرج:</strong></p>";
                echo "<ul>";
                echo "<li>ID: {$inserted['id']}</li>";
                echo "<li>user_id: {$inserted['user_id']}</li>";
                echo "<li>reporter_id: {$inserted['reporter_id']}</li>";
                echo "<li>reported_ad_id: {$inserted['reported_ad_id']}</li>";
                echo "<li>report_type: {$inserted['report_type']}</li>";
                echo "<li>created_at: {$inserted['created_at']}</li>";
                echo "</ul>";
                echo "</div>";
                
                // حذف البيانات التجريبية
                $db->query("DELETE FROM reports WHERE id = ?", [$inserted['id']]);
                echo "<p>✅ تم حذف البيانات التجريبية</p>";
            }
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>❌ فشل الاختبار النهائي!</h4>";
            echo "<p>رسالة الخطأ: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;'>";
    echo "<h3>🎉 تم الإصلاح النهائي!</h3>";
    echo "<p>جدول البلاغات الآن يحتوي على الهيكل الصحيح</p>";
    echo "<p>يمكنك الآن اختبار البلاغات من أي صفحة إعلان</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ في الإصلاح النهائي:</h4>";
    echo "<p><strong>الرسالة:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>🧪 اختبار المشكلة الآن:</h3>";
echo "<div style='text-align: center;'>";
echo "<a href='pages/ad-details.php?id=1' target='_blank' style='margin: 5px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>اختبار البلاغ - إعلان 1</a>";
echo "<a href='pages/ad-details.php?id=2' target='_blank' style='margin: 5px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>اختبار البلاغ - إعلان 2</a>";
echo "<a href='pages/ad-details.php?id=3' target='_blank' style='margin: 5px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>اختبار البلاغ - إعلان 3</a>";
echo "<a href='pages/ad-details.php?id=4' target='_blank' style='margin: 5px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>اختبار البلاغ - إعلان 4</a>";
echo "</div>";

echo "<div style='text-align: center; margin-top: 20px;'>";
echo "<a href='comprehensive-test.php' style='margin: 5px; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>الاختبار الشامل</a>";
echo "<a href='direct-test.php' style='margin: 5px; padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>الاختبار المباشر</a>";
echo "</div>";
?>
