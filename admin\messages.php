<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدمن
requireAdmin();

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        $message_id = (int)$_POST['message_id'];
        
        switch ($action) {
            case 'mark_read':
                $db->query("UPDATE contact_messages SET status = 'read' WHERE id = ?", [$message_id]);
                $_SESSION['success'] = 'تم تحديد الرسالة كمقروءة';
                break;
                
            case 'reply':
                $reply = sanitize($_POST['reply']);
                if (!empty($reply)) {
                    $db->query("UPDATE contact_messages SET admin_reply = ?, status = 'replied', replied_by = ?, replied_at = NOW() WHERE id = ?", 
                              [$reply, $_SESSION['user_id'], $message_id]);
                    $_SESSION['success'] = 'تم إرسال الرد بنجاح';
                }
                break;
                
            case 'close':
                $db->query("UPDATE contact_messages SET status = 'closed' WHERE id = ?", [$message_id]);
                $_SESSION['success'] = 'تم إغلاق الرسالة';
                break;
                
            case 'delete':
                $db->query("DELETE FROM contact_messages WHERE id = ?", [$message_id]);
                $_SESSION['success'] = 'تم حذف الرسالة';
                break;
        }
        
        header('Location: messages.php');
        exit();
    }
}

// فلترة الرسائل
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : 'all';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$items_per_page = 20;
$offset = ($page - 1) * $items_per_page;

$where_conditions = [];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب الرسائل
$sql = "
    SELECT cm.*, u.full_name as replied_by_name
    FROM contact_messages cm
    LEFT JOIN users u ON cm.replied_by = u.id
    $where_clause
    ORDER BY cm.created_at DESC
    LIMIT $items_per_page OFFSET $offset
";

$messages = $db->fetchAll($sql, $params);

// عدد النتائج الإجمالي
$count_sql = "SELECT COUNT(*) as total FROM contact_messages cm $where_clause";
$total_messages = $db->fetch($count_sql, $params)['total'];
$total_pages = ceil($total_messages / $items_per_page);

// إحصائيات سريعة
$stats = [
    'total' => $db->fetch("SELECT COUNT(*) as count FROM contact_messages")['count'],
    'new' => $db->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'new'")['count'],
    'read' => $db->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'read'")['count'],
    'replied' => $db->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'replied'")['count'],
    'closed' => $db->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'closed'")['count'],
];

$page_title = 'إدارة الرسائل';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة الرسائل</h1>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-2 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4><?= number_format($stats['total']) ?></h4>
                            <p class="mb-0">إجمالي</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h4><?= number_format($stats['new']) ?></h4>
                            <p class="mb-0">جديدة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h4><?= number_format($stats['read']) ?></h4>
                            <p class="mb-0">مقروءة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4><?= number_format($stats['replied']) ?></h4>
                            <p class="mb-0">تم الرد</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h4><?= number_format($stats['closed']) ?></h4>
                            <p class="mb-0">مغلقة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="btn-group" role="group">
                        <a href="?status=all" class="btn <?= $status_filter === 'all' ? 'btn-primary' : 'btn-outline-primary' ?>">
                            الكل (<?= $stats['total'] ?>)
                        </a>
                        <a href="?status=new" class="btn <?= $status_filter === 'new' ? 'btn-danger' : 'btn-outline-danger' ?>">
                            جديدة (<?= $stats['new'] ?>)
                        </a>
                        <a href="?status=read" class="btn <?= $status_filter === 'read' ? 'btn-warning' : 'btn-outline-warning' ?>">
                            مقروءة (<?= $stats['read'] ?>)
                        </a>
                        <a href="?status=replied" class="btn <?= $status_filter === 'replied' ? 'btn-success' : 'btn-outline-success' ?>">
                            تم الرد (<?= $stats['replied'] ?>)
                        </a>
                        <a href="?status=closed" class="btn <?= $status_filter === 'closed' ? 'btn-secondary' : 'btn-outline-secondary' ?>">
                            مغلقة (<?= $stats['closed'] ?>)
                        </a>
                    </div>
                </div>
            </div>

            <!-- جدول الرسائل -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">قائمة الرسائل (<?= number_format($total_messages) ?> رسالة)</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($messages)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>المرسل</th>
                                        <th>الموضوع</th>
                                        <th>الرسالة</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($messages as $message): ?>
                                        <tr class="<?= $message['status'] === 'new' ? 'table-warning' : '' ?>">
                                            <td>
                                                <div class="fw-bold"><?= $message['name'] ?></div>
                                                <small class="text-muted"><?= $message['email'] ?></small>
                                                <?php if ($message['phone']): ?>
                                                    <br><small class="text-muted"><?= $message['phone'] ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= $message['subject'] ?></td>
                                            <td>
                                                <div style="max-width: 200px;">
                                                    <?= substr($message['message'], 0, 100) ?>...
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= 
                                                    $message['status'] === 'new' ? 'danger' : 
                                                    ($message['status'] === 'read' ? 'warning' : 
                                                    ($message['status'] === 'replied' ? 'success' : 'secondary')) 
                                                ?>">
                                                    <?php
                                                    $status_labels = [
                                                        'new' => 'جديدة',
                                                        'read' => 'مقروءة',
                                                        'replied' => 'تم الرد',
                                                        'closed' => 'مغلقة'
                                                    ];
                                                    echo $status_labels[$message['status']] ?? $message['status'];
                                                    ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div><?= date('Y-m-d', strtotime($message['created_at'])) ?></div>
                                                <small class="text-muted"><?= date('H:i', strtotime($message['created_at'])) ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary view-message-btn" 
                                                            data-message='<?= json_encode($message) ?>'>
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <?php if ($message['status'] !== 'replied'): ?>
                                                        <button class="btn btn-outline-success reply-message-btn" 
                                                                data-id="<?= $message['id'] ?>" 
                                                                data-email="<?= $message['email'] ?>">
                                                            <i class="fas fa-reply"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <button class="btn btn-outline-danger delete-message-btn" 
                                                            data-id="<?= $message['id'] ?>">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- الصفحات -->
                        <?php if ($total_pages > 1): ?>
                        <nav aria-label="صفحات الرسائل" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>">السابق</a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                        <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"><?= $i ?></a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>">التالي</a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>

                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                            <h5>لا توجد رسائل</h5>
                            <p class="text-muted">لا توجد رسائل تطابق معايير البحث</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Modal عرض الرسالة -->
<div class="modal fade" id="viewMessageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الرسالة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="messageContent">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // عرض الرسالة
    document.querySelectorAll('.view-message-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const message = JSON.parse(this.getAttribute('data-message'));
            
            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>الاسم:</strong> ${message.name}<br>
                        <strong>البريد الإلكتروني:</strong> ${message.email}<br>
                        ${message.phone ? '<strong>الهاتف:</strong> ' + message.phone + '<br>' : ''}
                        <strong>الموضوع:</strong> ${message.subject}<br>
                        <strong>التاريخ:</strong> ${message.created_at}
                    </div>
                    <div class="col-md-6">
                        <strong>الحالة:</strong> ${message.status}<br>
                        <strong>عنوان IP:</strong> ${message.ip_address || 'غير متاح'}
                    </div>
                </div>
                <hr>
                <div>
                    <strong>الرسالة:</strong><br>
                    <div class="border p-3 bg-light">${message.message.replace(/\n/g, '<br>')}</div>
                </div>
                ${message.admin_reply ? '<hr><div><strong>الرد:</strong><br><div class="border p-3 bg-success text-white">' + message.admin_reply.replace(/\n/g, '<br>') + '</div></div>' : ''}
            `;
            
            document.getElementById('messageContent').innerHTML = content;
            
            const modal = new bootstrap.Modal(document.getElementById('viewMessageModal'));
            modal.show();
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
