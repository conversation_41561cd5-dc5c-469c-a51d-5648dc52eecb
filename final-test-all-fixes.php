<?php
session_start();

echo "<h1>🔧 اختبار نهائي شامل - جميع الإصلاحات</h1>";

try {
    $pdo = new PDO('mysql:host=localhost;dbname=harajuna;charset=utf8mb4', 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<h2>✅ قاعدة البيانات متصلة</h2>";
    
    // اختبار الجداول المطلوبة
    $tables = [
        'users' => 'المستخدمين',
        'categories' => 'الفئات', 
        'ads' => 'الإعلانات',
        'messages' => 'الرسائل الداخلية',
        'reports' => 'التقارير',
        'contact_messages' => 'رسائل الاتصال',
        'favorites' => 'المفضلة',
        'announcements' => 'الإعلانات العامة',
        'settings' => 'الإعدادات'
    ];
    
    echo "<h3>📊 حالة الجداول:</h3>";
    echo "<div class='row'>";
    
    foreach ($tables as $table => $name) {
        try {
            $result = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $result->fetch()['count'];
            echo "<div class='col-md-4 mb-2'>";
            echo "<div class='alert alert-success'>✅ <strong>$name</strong>: $count سجل</div>";
            echo "</div>";
        } catch (Exception $e) {
            echo "<div class='col-md-4 mb-2'>";
            echo "<div class='alert alert-danger'>❌ <strong>$name</strong>: غير موجود</div>";
            echo "</div>";
        }
    }
    
    echo "</div>";
    
    // اختبار المشاكل المحلولة
    echo "<h3>🎯 المشاكل المحلولة:</h3>";
    echo "<div class='row'>";
    
    $fixes = [
        'المفضلة في تفاصيل الإعلان' => 'pages/ad-details.php?id=1',
        'الإبلاغ عن الإعلانات' => 'pages/ad-details.php?id=1',
        'الرسائل الداخلية' => 'pages/messages.php',
        'روابط الفئات' => 'pages/categories.php',
        'البحث بالمدينة' => 'pages/home.php',
        'صفحة اتصل بنا' => 'pages/contact.php',
        'إدارة المستخدمين' => 'admin/users.php',
        'إدارة التقارير' => 'admin/reports.php',
        'الإعلانات العامة' => 'admin/announcements.php',
        'إعدادات الموقع' => 'admin/settings.php'
    ];
    
    foreach ($fixes as $name => $url) {
        echo "<div class='col-md-6 mb-2'>";
        echo "<div class='alert alert-info'>";
        echo "<strong>✅ $name</strong><br>";
        echo "<a href='$url' target='_blank' class='btn btn-sm btn-primary'>اختبار الآن</a>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // اختبار سريع للبيانات
    echo "<h3>📈 إحصائيات سريعة:</h3>";
    echo "<div class='row'>";
    
    $stats = [
        'المستخدمين' => $pdo->query("SELECT COUNT(*) as count FROM users")->fetch()['count'],
        'الإعلانات النشطة' => $pdo->query("SELECT COUNT(*) as count FROM ads WHERE status = 'active'")->fetch()['count'],
        'التقارير المعلقة' => $pdo->query("SELECT COUNT(*) as count FROM reports WHERE status = 'pending'")->fetch()['count'],
        'الرسائل غير المقروءة' => $pdo->query("SELECT COUNT(*) as count FROM messages WHERE is_read = 0")->fetch()['count'],
        'الإعلانات العامة النشطة' => $pdo->query("SELECT COUNT(*) as count FROM announcements WHERE is_active = 1")->fetch()['count']
    ];
    
    foreach ($stats as $name => $count) {
        echo "<div class='col-md-2 mb-2'>";
        echo "<div class='card text-center'>";
        echo "<div class='card-body'>";
        echo "<h4 class='text-primary'>$count</h4>";
        echo "<p class='mb-0'>$name</p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في الاتصال: " . $e->getMessage() . "</h2>";
}

echo "<hr>";
echo "<h2>🧪 اختبارات تفاعلية:</h2>";

// اختبارات تفاعلية
$tests = [
    [
        'title' => '💖 اختبار المفضلة',
        'description' => 'اذهب لأي إعلان واضغط على زر القلب',
        'url' => 'pages/ad-details.php?id=1',
        'steps' => [
            'اذهب لصفحة تفاصيل الإعلان',
            'اضغط على زر القلب ❤️',
            'يجب أن يتغير لون الزر',
            'تظهر رسالة تأكيد'
        ]
    ],
    [
        'title' => '🚨 اختبار الإبلاغ',
        'description' => 'اضغط على زر إبلاغ في أي إعلان',
        'url' => 'pages/ad-details.php?id=1',
        'steps' => [
            'اذهب لصفحة تفاصيل الإعلان',
            'اضغط على زر "إبلاغ" 🚩',
            'املأ النموذج',
            'اضغط إرسال البلاغ'
        ]
    ],
    [
        'title' => '📧 اختبار البريد الإلكتروني',
        'description' => 'اضغط على بريد إلكتروني في أي إعلان',
        'url' => 'pages/ad-details.php?id=1',
        'steps' => [
            'اذهب لصفحة تفاصيل الإعلان',
            'اضغط على "بريد إلكتروني" 📧',
            'يجب أن يفتح تطبيق البريد',
            'الموضوع والنص محددان مسبقاً'
        ]
    ],
    [
        'title' => '📂 اختبار الفئات',
        'description' => 'اضغط على عرض الإعلانات في أي فئة',
        'url' => 'pages/categories.php',
        'steps' => [
            'اذهب لصفحة الفئات',
            'اضغط على "عرض الإعلانات"',
            'يجب أن تظهر إعلانات الفئة',
            'وليس الصفحة الرئيسية'
        ]
    ],
    [
        'title' => '🔍 اختبار البحث',
        'description' => 'استخدم البحث السريع في الصفحة الرئيسية',
        'url' => 'pages/home.php',
        'steps' => [
            'اذهب للصفحة الرئيسية',
            'اكتب مدينة في البحث',
            'اضغط "بحث"',
            'تظهر إعلانات المدينة'
        ]
    ],
    [
        'title' => '💬 اختبار الرسائل',
        'description' => 'أرسل رسالة لبائع',
        'url' => 'pages/ad-details.php?id=1',
        'steps' => [
            'اذهب لصفحة تفاصيل الإعلان',
            'اضغط على "راسل البائع" 💬',
            'اكتب رسالة',
            'اضغط إرسال'
        ]
    ]
];

echo "<div class='row'>";
foreach ($tests as $test) {
    echo "<div class='col-md-6 mb-4'>";
    echo "<div class='card h-100'>";
    echo "<div class='card-header'>";
    echo "<h5>{$test['title']}</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<p>{$test['description']}</p>";
    echo "<ol>";
    foreach ($test['steps'] as $step) {
        echo "<li>$step</li>";
    }
    echo "</ol>";
    echo "</div>";
    echo "<div class='card-footer'>";
    echo "<a href='{$test['url']}' target='_blank' class='btn btn-primary w-100'>اختبار الآن</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
}
echo "</div>";

echo "<hr>";
echo "<h2>📋 ملخص الإصلاحات النهائي:</h2>";

$final_summary = [
    '✅ إصلاح المفضلة والإبلاغ (خطأ 500)',
    '✅ إصلاح الرسائل الداخلية (خطأ 500)', 
    '✅ إصلاح روابط الفئات',
    '✅ إصلاح صفحة اتصل بنا',
    '✅ إضافة البحث بالمدينة',
    '✅ إنشاء إدارة المستخدمين',
    '✅ إنشاء إدارة التقارير',
    '✅ إنشاء نظام الإعلانات العامة',
    '✅ إنشاء صفحة الإعدادات',
    '✅ إصلاح جميع الجداول المطلوبة'
];

echo "<div class='alert alert-success'>";
echo "<h4>🎉 جميع المشاكل تم حلها بنجاح!</h4>";
echo "<ul class='mb-0'>";
foreach ($final_summary as $item) {
    echo "<li>$item</li>";
}
echo "</ul>";
echo "</div>";

echo "<div class='alert alert-info'>";
echo "<h5>📁 الملفات المضافة:</h5>";
echo "<ul class='mb-0'>";
echo "<li><strong>شرح الفئات.txt</strong> - دليل إدارة الفئات</li>";
echo "<li><strong>ملخص الإصلاحات النهائي.txt</strong> - ملخص شامل</li>";
echo "<li><strong>admin/users.php</strong> - إدارة المستخدمين</li>";
echo "<li><strong>admin/reports.php</strong> - إدارة التقارير</li>";
echo "<li><strong>admin/announcements.php</strong> - الإعلانات العامة</li>";
echo "<li><strong>admin/settings.php</strong> - إعدادات الموقع</li>";
echo "<li><strong>pages/category-ads.php</strong> - إعلانات الفئة</li>";
echo "</ul>";
echo "</div>";

echo "<div class='text-center mt-4'>";
echo "<h3 style='color: #28a745;'>🚀 الموقع جاهز للاستخدام الكامل!</h3>";
echo "<p class='lead'>جميع الميزات تعمل بشكل صحيح ويمكن اختبارها الآن</p>";
echo "</div>";

// إضافة CSS للتنسيق
echo "<style>
.alert { margin-bottom: 1rem; }
.card { margin-bottom: 1rem; }
.row { margin-bottom: 1rem; }
h1, h2, h3 { color: #333; margin-bottom: 1rem; }
.btn { margin: 0.25rem; }
</style>";

// إضافة Bootstrap للتنسيق
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>";
?>
