<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدمن
requireAdmin();

// الحصول على الفترة الزمنية
$period = isset($_GET['period']) ? $_GET['period'] : '30';
$start_date = date('Y-m-d', strtotime("-{$period} days"));
$end_date = date('Y-m-d');

// إحصائيات عامة
$total_users = $db->fetch("SELECT COUNT(*) as count FROM users")['count'];
$total_ads = $db->fetch("SELECT COUNT(*) as count FROM ads")['count'];
$active_ads = $db->fetch("SELECT COUNT(*) as count FROM ads WHERE status = 'active'")['count'];
$total_categories = $db->fetch("SELECT COUNT(*) as count FROM categories WHERE is_active = 1")['count'];

// إحصائيات الفترة المحددة
$new_users = $db->fetch("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) >= ?", [$start_date])['count'];
$new_ads = $db->fetch("SELECT COUNT(*) as count FROM ads WHERE DATE(created_at) >= ?", [$start_date])['count'];

// أكثر الفئات نشاطاً
$top_categories = $db->fetchAll("
    SELECT c.name, COUNT(a.id) as ads_count 
    FROM categories c 
    LEFT JOIN ads a ON c.id = a.category_id 
    WHERE a.created_at >= ? 
    GROUP BY c.id, c.name 
    ORDER BY ads_count DESC 
    LIMIT 10
", [$start_date]);

// أكثر المدن نشاطاً
$top_cities = $db->fetchAll("
    SELECT city, COUNT(*) as ads_count 
    FROM ads 
    WHERE created_at >= ? AND city IS NOT NULL AND city != ''
    GROUP BY city 
    ORDER BY ads_count DESC 
    LIMIT 10
", [$start_date]);

// إحصائيات يومية للإعلانات
$daily_ads = $db->fetchAll("
    SELECT DATE(created_at) as date, COUNT(*) as count 
    FROM ads 
    WHERE DATE(created_at) >= ? 
    GROUP BY DATE(created_at) 
    ORDER BY date ASC
", [$start_date]);

// إحصائيات يومية للمستخدمين
$daily_users = $db->fetchAll("
    SELECT DATE(created_at) as date, COUNT(*) as count 
    FROM users 
    WHERE DATE(created_at) >= ? 
    GROUP BY DATE(created_at) 
    ORDER BY date ASC
", [$start_date]);

// أكثر المستخدمين نشاطاً
$top_users = $db->fetchAll("
    SELECT u.username, u.full_name, COUNT(a.id) as ads_count 
    FROM users u 
    LEFT JOIN ads a ON u.id = a.user_id 
    WHERE a.created_at >= ? 
    GROUP BY u.id 
    ORDER BY ads_count DESC 
    LIMIT 10
", [$start_date]);

// إحصائيات حالة الإعلانات
$ads_status = $db->fetchAll("
    SELECT status, COUNT(*) as count 
    FROM ads 
    WHERE created_at >= ? 
    GROUP BY status
", [$start_date]);

$page_title = 'التحليلات والإحصائيات';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">التحليلات والإحصائيات</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="?period=7" class="btn btn-sm btn-outline-secondary <?= $period == '7' ? 'active' : '' ?>">7 أيام</a>
                        <a href="?period=30" class="btn btn-sm btn-outline-secondary <?= $period == '30' ? 'active' : '' ?>">30 يوم</a>
                        <a href="?period=90" class="btn btn-sm btn-outline-secondary <?= $period == '90' ? 'active' : '' ?>">90 يوم</a>
                        <a href="?period=365" class="btn btn-sm btn-outline-secondary <?= $period == '365' ? 'active' : '' ?>">سنة</a>
                    </div>
                </div>
            </div>

            <!-- إحصائيات عامة -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <h4><?= number_format($total_users) ?></h4>
                            <p class="mb-0">إجمالي المستخدمين</p>
                            <small>+<?= number_format($new_users) ?> جديد</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-bullhorn fa-2x mb-2"></i>
                            <h4><?= number_format($total_ads) ?></h4>
                            <p class="mb-0">إجمالي الإعلانات</p>
                            <small>+<?= number_format($new_ads) ?> جديد</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-eye fa-2x mb-2"></i>
                            <h4><?= number_format($active_ads) ?></h4>
                            <p class="mb-0">إعلانات نشطة</p>
                            <small><?= $total_ads > 0 ? round(($active_ads / $total_ads) * 100, 1) : 0 ?>% من الإجمالي</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-th-large fa-2x mb-2"></i>
                            <h4><?= number_format($total_categories) ?></h4>
                            <p class="mb-0">الفئات النشطة</p>
                            <small>متاحة للاستخدام</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- الرسم البياني للإعلانات اليومية -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">الإعلانات اليومية</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="dailyAdsChart" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- الرسم البياني للمستخدمين اليوميين -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">المستخدمون الجدد يومياً</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="dailyUsersChart" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- أكثر الفئات نشاطاً -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">أكثر الفئات نشاطاً</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($top_categories)): ?>
                                <p class="text-muted text-center">لا توجد بيانات</p>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>الفئة</th>
                                                <th>عدد الإعلانات</th>
                                                <th>النسبة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $total_category_ads = array_sum(array_column($top_categories, 'ads_count'));
                                            foreach ($top_categories as $category): 
                                                $percentage = $total_category_ads > 0 ? round(($category['ads_count'] / $total_category_ads) * 100, 1) : 0;
                                            ?>
                                                <tr>
                                                    <td><?= htmlspecialchars($category['name']) ?></td>
                                                    <td><?= number_format($category['ads_count']) ?></td>
                                                    <td>
                                                        <div class="progress" style="height: 20px;">
                                                            <div class="progress-bar" role="progressbar" 
                                                                 style="width: <?= $percentage ?>%">
                                                                <?= $percentage ?>%
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- أكثر المدن نشاطاً -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">أكثر المدن نشاطاً</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($top_cities)): ?>
                                <p class="text-muted text-center">لا توجد بيانات</p>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>المدينة</th>
                                                <th>عدد الإعلانات</th>
                                                <th>النسبة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $total_city_ads = array_sum(array_column($top_cities, 'ads_count'));
                                            foreach ($top_cities as $city): 
                                                $percentage = $total_city_ads > 0 ? round(($city['ads_count'] / $total_city_ads) * 100, 1) : 0;
                                            ?>
                                                <tr>
                                                    <td><?= htmlspecialchars($city['city']) ?></td>
                                                    <td><?= number_format($city['ads_count']) ?></td>
                                                    <td>
                                                        <div class="progress" style="height: 20px;">
                                                            <div class="progress-bar bg-info" role="progressbar" 
                                                                 style="width: <?= $percentage ?>%">
                                                                <?= $percentage ?>%
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- حالة الإعلانات -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">حالة الإعلانات</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="adsStatusChart" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- أكثر المستخدمين نشاطاً -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">أكثر المستخدمين نشاطاً</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($top_users)): ?>
                                <p class="text-muted text-center">لا توجد بيانات</p>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>المستخدم</th>
                                                <th>عدد الإعلانات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($top_users as $user): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?= htmlspecialchars($user['full_name']) ?></strong>
                                                        <br><small class="text-muted">@<?= htmlspecialchars($user['username']) ?></small>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-primary"><?= number_format($user['ads_count']) ?></span>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// إعداد البيانات للرسوم البيانية
const dailyAdsData = <?= json_encode($daily_ads) ?>;
const dailyUsersData = <?= json_encode($daily_users) ?>;
const adsStatusData = <?= json_encode($ads_status) ?>;

// رسم بياني للإعلانات اليومية
const dailyAdsCtx = document.getElementById('dailyAdsChart').getContext('2d');
new Chart(dailyAdsCtx, {
    type: 'line',
    data: {
        labels: dailyAdsData.map(item => item.date),
        datasets: [{
            label: 'عدد الإعلانات',
            data: dailyAdsData.map(item => item.count),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// رسم بياني للمستخدمين اليوميين
const dailyUsersCtx = document.getElementById('dailyUsersChart').getContext('2d');
new Chart(dailyUsersCtx, {
    type: 'bar',
    data: {
        labels: dailyUsersData.map(item => item.date),
        datasets: [{
            label: 'مستخدمون جدد',
            data: dailyUsersData.map(item => item.count),
            backgroundColor: 'rgba(54, 162, 235, 0.5)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// رسم بياني دائري لحالة الإعلانات
const adsStatusCtx = document.getElementById('adsStatusChart').getContext('2d');
const statusColors = {
    'active': '#28a745',
    'pending': '#ffc107',
    'rejected': '#dc3545',
    'expired': '#6c757d'
};

const statusLabels = {
    'active': 'نشطة',
    'pending': 'في الانتظار',
    'rejected': 'مرفوضة',
    'expired': 'منتهية الصلاحية'
};

new Chart(adsStatusCtx, {
    type: 'doughnut',
    data: {
        labels: adsStatusData.map(item => statusLabels[item.status] || item.status),
        datasets: [{
            data: adsStatusData.map(item => item.count),
            backgroundColor: adsStatusData.map(item => statusColors[item.status] || '#6c757d'),
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// تحديث الصفحة كل 5 دقائق
setTimeout(function() {
    location.reload();
}, 300000);
</script>

<?php include 'includes/footer.php'; ?>
