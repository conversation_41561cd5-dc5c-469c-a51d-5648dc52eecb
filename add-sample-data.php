<?php
// إضافة بيانات تجريبية للموقع
echo "<h1>📊 إضافة بيانات تجريبية</h1>";
echo "<style>
    body{font-family:Arial;margin:20px;background:#f5f5f5;} 
    .card{background:white;padding:20px;margin:15px 0;border-radius:8px;box-shadow:0 2px 4px rgba(0,0,0,0.1);}
    .success{color:green;} .error{color:red;} .info{color:blue;}
</style>";

try {
    require_once 'config/database.php';
    
    echo "<div class='card'>";
    echo "<h2>👥 إضافة مستخدمين تجريبيين</h2>";
    
    // إضافة مستخدمين تجريبيين
    $sample_users = [
        ['أحمد محمد', '<EMAIL>', 'ahmed123', '0501234567', 'الرياض'],
        ['فاطمة علي', '<EMAIL>', 'fatima123', '0509876543', 'جدة'],
        ['محمد سالم', '<EMAIL>', 'mohammed123', '0551234567', 'الدمام'],
        ['نورا أحمد', '<EMAIL>', 'nora123', '0559876543', 'مكة المكرمة']
    ];
    
    foreach ($sample_users as $user) {
        $hashedPassword = password_hash($user[2], PASSWORD_DEFAULT);
        try {
            $db->query("INSERT OR IGNORE INTO users (full_name, email, username, password, phone, city, user_type) VALUES (?, ?, ?, ?, ?, ?, 'user')", 
                [$user[0], $user[1], $user[2], $hashedPassword, $user[3], $user[4]]);
            echo "<p class='success'>✅ تم إضافة المستخدم: {$user[0]}</p>";
        } catch (Exception $e) {
            echo "<p class='error'>❌ خطأ في إضافة {$user[0]}: " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
    
    echo "<div class='card'>";
    echo "<h2>📢 إضافة إعلانات تجريبية</h2>";
    
    // الحصول على معرفات المستخدمين والفئات
    $users = $db->fetchAll("SELECT id FROM users WHERE user_type = 'user'");
    $categories = $db->fetchAll("SELECT id FROM categories");
    
    if (!empty($users) && !empty($categories)) {
        $sample_ads = [
            [
                'title' => 'سيارة تويوتا كامري 2020 للبيع',
                'description' => 'سيارة تويوتا كامري موديل 2020، حالة ممتازة، قطعت 50 ألف كيلو فقط. السيارة نظيفة جداً ولم تتعرض لأي حوادث.',
                'price' => 85000,
                'city' => 'الرياض',
                'phone' => '0501234567'
            ],
            [
                'title' => 'شقة للإيجار في حي الملز',
                'description' => 'شقة 3 غرف وصالة في حي الملز، الدور الثاني، مساحة 120 متر مربع، قريبة من الخدمات.',
                'price' => 2500,
                'city' => 'الرياض',
                'phone' => '0509876543'
            ],
            [
                'title' => 'آيفون 14 برو ماكس للبيع',
                'description' => 'آيفون 14 برو ماكس، 256 جيجا، لون أزرق، حالة ممتازة مع الكرتون والشاحن الأصلي.',
                'price' => 4200,
                'city' => 'جدة',
                'phone' => '0551234567'
            ],
            [
                'title' => 'طقم غرفة نوم مودرن',
                'description' => 'طقم غرفة نوم مودرن يتكون من سرير ودولابين وتسريحة، خشب ماليزي عالي الجودة.',
                'price' => 3500,
                'city' => 'الدمام',
                'phone' => '0559876543'
            ],
            [
                'title' => 'دراجة هوائية جبلية',
                'description' => 'دراجة هوائية جبلية ماركة Trek، 21 سرعة، حالة ممتازة، مناسبة للرحلات والتمارين.',
                'price' => 800,
                'city' => 'مكة المكرمة',
                'phone' => '0501234567'
            ]
        ];
        
        foreach ($sample_ads as $ad) {
            $user_id = $users[array_rand($users)]['id'];
            $category_id = $categories[array_rand($categories)]['id'];
            
            try {
                $db->query("INSERT INTO ads (user_id, category_id, title, description, price, city, phone) VALUES (?, ?, ?, ?, ?, ?, ?)", 
                    [$user_id, $category_id, $ad['title'], $ad['description'], $ad['price'], $ad['city'], $ad['phone']]);
                echo "<p class='success'>✅ تم إضافة الإعلان: {$ad['title']}</p>";
            } catch (Exception $e) {
                echo "<p class='error'>❌ خطأ في إضافة الإعلان: " . $e->getMessage() . "</p>";
            }
        }
    } else {
        echo "<p class='error'>❌ لا توجد مستخدمين أو فئات لإضافة الإعلانات</p>";
    }
    echo "</div>";
    
    // عرض الإحصائيات
    echo "<div class='card'>";
    echo "<h2>📊 إحصائيات الموقع</h2>";
    
    $users_count = $db->query("SELECT COUNT(*) as count FROM users")->fetch()['count'];
    $categories_count = $db->query("SELECT COUNT(*) as count FROM categories")->fetch()['count'];
    $ads_count = $db->query("SELECT COUNT(*) as count FROM ads")->fetch()['count'];
    
    echo "<p><strong>👥 عدد المستخدمين:</strong> $users_count</p>";
    echo "<p><strong>📂 عدد الفئات:</strong> $categories_count</p>";
    echo "<p><strong>📢 عدد الإعلانات:</strong> $ads_count</p>";
    echo "</div>";
    
    echo "<div class='card' style='background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;'>";
    echo "<h2>🎉 تم إضافة البيانات التجريبية بنجاح!</h2>";
    echo "<p>يمكنك الآن تصفح الموقع ورؤية الإعلانات والمستخدمين التجريبيين</p>";
    echo "<div style='margin-top: 15px;'>";
    echo "<a href='pages/home.php' style='background: white; color: #28a745; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 الصفحة الرئيسية</a>";
    echo "<a href='pages/search.php' style='background: white; color: #28a745; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 البحث</a>";
    echo "<a href='admin/ads.php' style='background: white; color: #28a745; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>📢 إدارة الإعلانات</a>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='card' style='border: 2px solid red;'>";
    echo "<h3 class='error'>❌ خطأ:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
