<?php
// سكريبت لإصلاح مشكلة كائن المصادقة في جميع الصفحات

echo "<h1>🔧 إصلاح مشكلة كائن المصادقة</h1>";
echo "<style>
    body{font-family:Arial;margin:20px;background:#f5f5f5;} 
    .success{color:green;} .error{color:red;} .info{color:blue;}
    .card{background:white;padding:20px;margin:15px 0;border-radius:8px;box-shadow:0 2px 4px rgba(0,0,0,0.1);}
</style>";

// قائمة الملفات التي تحتاج إصلاح
$files_to_fix = [
    'pages/my-ads.php',
    'pages/profile.php',
    'pages/favorites.php',
    'pages/edit-ad.php',
    'pages/ad-details.php',
    'pages/messages.php',
    'admin/ads.php',
    'admin/users.php',
    'admin/categories.php'
];

$pattern_to_find = "require_once \$root_path . '/includes/auth.php';";
$replacement = "require_once \$root_path . '/includes/auth.php';\n\n// إنشاء كائن المصادقة\n\$auth = new Auth(\$db);";

echo "<div class='card'>";
echo "<h2>📋 الملفات المراد إصلاحها:</h2>";

foreach ($files_to_fix as $file) {
    echo "<p><strong>$file</strong> - ";
    
    if (!file_exists($file)) {
        echo "<span class='error'>❌ الملف غير موجود</span></p>";
        continue;
    }
    
    $content = file_get_contents($file);
    $original_content = $content;
    
    // التحقق من وجود كائن المصادقة
    if (strpos($content, '$auth = new Auth($db);') !== false) {
        echo "<span class='info'>ℹ️ الملف محدث بالفعل</span></p>";
        continue;
    }
    
    // التحقق من وجود النمط المطلوب
    if (strpos($content, "require_once \$root_path . '/includes/auth.php';") !== false) {
        $content = str_replace(
            "require_once \$root_path . '/includes/auth.php';",
            $replacement,
            $content
        );
        
        if (file_put_contents($file, $content)) {
            echo "<span class='success'>✅ تم الإصلاح</span></p>";
        } else {
            echo "<span class='error'>❌ فشل في الحفظ</span></p>";
        }
    } else {
        echo "<span class='error'>❌ النمط غير موجود</span></p>";
    }
}

echo "</div>";

// التحقق من الملفات الأساسية
echo "<div class='card'>";
echo "<h2>🔍 التحقق من الملفات الأساسية:</h2>";

$core_files = [
    'config/database.php' => 'ملف قاعدة البيانات',
    'config/config.php' => 'ملف الإعدادات',
    'includes/functions.php' => 'ملف الوظائف',
    'includes/auth.php' => 'ملف المصادقة'
];

foreach ($core_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p><span class='success'>✅</span> $description ($file)</p>";
    } else {
        echo "<p><span class='error'>❌</span> $description ($file) - مفقود!</p>";
    }
}

echo "</div>";

// اختبار قاعدة البيانات
echo "<div class='card'>";
echo "<h2>🗄️ اختبار قاعدة البيانات:</h2>";

try {
    require_once 'config/database.php';
    echo "<p><span class='success'>✅</span> تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // اختبار إنشاء كائن المصادقة
    require_once 'includes/auth.php';
    $auth = new Auth($db);
    echo "<p><span class='success'>✅</span> تم إنشاء كائن المصادقة بنجاح</p>";
    
} catch (Exception $e) {
    echo "<p><span class='error'>❌</span> خطأ: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div class='card' style='background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;'>";
echo "<h2>🎉 تم الانتهاء من الإصلاح!</h2>";
echo "<p>يمكنك الآن اختبار الصفحات:</p>";
echo "<div style='margin-top: 15px;'>";
echo "<a href='pages/login.php' style='background: white; color: #28a745; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔐 تسجيل الدخول</a>";
echo "<a href='pages/register.php' style='background: white; color: #28a745; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>📝 التسجيل</a>";
echo "<a href='pages/home.php' style='background: white; color: #28a745; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 الرئيسية</a>";
echo "<a href='admin/index.php' style='background: white; color: #28a745; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>👨‍💼 الإدارة</a>";
echo "</div>";
echo "</div>";
?>
