<?php
// إنشاء جدول رسائل الاتصال
try {
    $pdo = new PDO('mysql:host=localhost;dbname=harajuna;charset=utf8mb4', 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // إنشاء جدول رسائل الاتصال
    $sql = "
    CREATE TABLE IF NOT EXISTS contact_messages (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        subject VARCHAR(200) NOT NULL,
        message TEXT NOT NULL,
        status ENUM('new', 'read', 'replied', 'closed') DEFAULT 'new',
        admin_reply TEXT,
        replied_by INT,
        replied_at TIMESTAMP NULL,
        ip_address VARCHAR(45),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_created_at (created_at),
        FOREIGN KEY (replied_by) REFERENCES users(id) ON DELETE SET NULL
    )";
    
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول رسائل الاتصال بنجاح<br>";
    
    echo "<hr>";
    echo "<h3>🎉 تم إعداد جدول رسائل الاتصال!</h3>";
    echo "<p>يمكنك الآن استخدام صفحة اتصل بنا:</p>";
    echo "<ul>";
    echo "<li><a href='pages/contact.php'>صفحة اتصل بنا</a></li>";
    echo "<li><a href='admin/messages.php'>إدارة الرسائل</a></li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
}
?>
