<?php
// إصلاح الجداول المطلوبة
try {
    $pdo = new PDO('mysql:host=localhost;dbname=harajuna;charset=utf8mb4', 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<h2>🔧 إصلاح الجداول المطلوبة</h2>";
    
    // إنشاء جدول التقارير
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS reports (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                ad_id INT NOT NULL,
                reason VARCHAR(100) NOT NULL,
                description TEXT,
                status ENUM('pending', 'reviewed', 'resolved', 'rejected') DEFAULT 'pending',
                reviewed_by INT,
                reviewed_at TIMESTAMP NULL,
                admin_notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_ad_id (ad_id),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE,
                FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL
            )
        ");
        echo "✅ جدول التقارير جاهز<br>";
    } catch (Exception $e) {
        echo "ℹ️ جدول التقارير موجود بالفعل<br>";
    }
    
    // إنشاء جدول الرسائل
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS messages (
                id INT PRIMARY KEY AUTO_INCREMENT,
                sender_id INT NOT NULL,
                receiver_id INT NOT NULL,
                ad_id INT NULL,
                message TEXT NOT NULL,
                is_read BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_sender_id (sender_id),
                INDEX idx_receiver_id (receiver_id),
                INDEX idx_ad_id (ad_id),
                INDEX idx_created_at (created_at),
                FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE SET NULL
            )
        ");
        echo "✅ جدول الرسائل جاهز<br>";
    } catch (Exception $e) {
        echo "ℹ️ جدول الرسائل موجود بالفعل<br>";
    }
    
    // إنشاء جدول المفضلة
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS favorites (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                ad_id INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY unique_favorite (user_id, ad_id),
                INDEX idx_user_id (user_id),
                INDEX idx_ad_id (ad_id),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE
            )
        ");
        echo "✅ جدول المفضلة جاهز<br>";
    } catch (Exception $e) {
        echo "ℹ️ جدول المفضلة موجود بالفعل<br>";
    }
    
    // إنشاء جدول رسائل الاتصال
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS contact_messages (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                subject VARCHAR(200) NOT NULL,
                message TEXT NOT NULL,
                status ENUM('new', 'read', 'replied', 'closed') DEFAULT 'new',
                admin_reply TEXT,
                replied_by INT,
                replied_at TIMESTAMP NULL,
                ip_address VARCHAR(45),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_status (status),
                INDEX idx_created_at (created_at),
                FOREIGN KEY (replied_by) REFERENCES users(id) ON DELETE SET NULL
            )
        ");
        echo "✅ جدول رسائل الاتصال جاهز<br>";
    } catch (Exception $e) {
        echo "ℹ️ جدول رسائل الاتصال موجود بالفعل<br>";
    }
    
    // إنشاء جدول الإعلانات العامة
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS announcements (
                id INT PRIMARY KEY AUTO_INCREMENT,
                title VARCHAR(200) NOT NULL,
                content TEXT NOT NULL,
                type ENUM('info', 'warning', 'success', 'danger') DEFAULT 'info',
                is_active BOOLEAN DEFAULT TRUE,
                show_on_homepage BOOLEAN DEFAULT TRUE,
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_is_active (is_active),
                INDEX idx_show_on_homepage (show_on_homepage),
                INDEX idx_created_at (created_at),
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
            )
        ");
        echo "✅ جدول الإعلانات العامة جاهز<br>";
    } catch (Exception $e) {
        echo "ℹ️ جدول الإعلانات العامة موجود بالفعل<br>";
    }
    
    // إضافة عمود image_path للفئات إذا لم يكن موجوداً
    try {
        $pdo->exec("ALTER TABLE categories ADD COLUMN image_path VARCHAR(255) AFTER icon");
        echo "✅ تم إضافة عمود image_path لجدول الفئات<br>";
    } catch (Exception $e) {
        echo "ℹ️ عمود image_path موجود بالفعل في جدول الفئات<br>";
    }
    
    // اختبار الجداول
    echo "<hr>";
    echo "<h3>📊 اختبار الجداول:</h3>";
    
    $tables = ['reports', 'messages', 'favorites', 'contact_messages', 'announcements'];
    
    foreach ($tables as $table) {
        try {
            $result = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $result->fetch()['count'];
            echo "<div style='color: green;'>✅ جدول <strong>$table</strong>: $count سجل</div>";
        } catch (Exception $e) {
            echo "<div style='color: red;'>❌ جدول <strong>$table</strong>: خطأ - " . $e->getMessage() . "</div>";
        }
    }
    
    echo "<hr>";
    echo "<h3>🎯 اختبار الصفحات:</h3>";
    echo "<ul>";
    echo "<li><a href='pages/ad-details.php?id=1' target='_blank'>اختبار تفاصيل الإعلان</a></li>";
    echo "<li><a href='pages/messages.php' target='_blank'>اختبار الرسائل</a></li>";
    echo "<li><a href='pages/contact.php' target='_blank'>اختبار اتصل بنا</a></li>";
    echo "<li><a href='admin/reports.php' target='_blank'>اختبار إدارة التقارير</a></li>";
    echo "<li><a href='admin/announcements.php' target='_blank'>اختبار الإعلانات العامة</a></li>";
    echo "</ul>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724;'>🎉 تم إصلاح جميع الجداول!</h3>";
    echo "<p style='color: #155724;'>يمكنك الآن اختبار جميع الميزات باستخدام الروابط أعلاه.</p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
}
?>
