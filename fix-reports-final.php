<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h3>🔧 إصلاح نهائي لجدول البلاغات</h3>";

try {
    // الخطوة 1: فحص الجدول الحالي
    $current_structure = $db->fetchAll("PRAGMA table_info(reports)");
    $has_ad_id = false;
    $has_reported_ad_id = false;
    
    foreach ($current_structure as $col) {
        if ($col['name'] === 'ad_id') $has_ad_id = true;
        if ($col['name'] === 'reported_ad_id') $has_reported_ad_id = true;
    }
    
    echo "<p><strong>الحالة الحالية:</strong></p>";
    echo "<ul>";
    echo "<li>ad_id موجود: " . ($has_ad_id ? "نعم" : "لا") . "</li>";
    echo "<li>reported_ad_id موجود: " . ($has_reported_ad_id ? "نعم" : "لا") . "</li>";
    echo "</ul>";
    
    if ($has_ad_id && !$has_reported_ad_id) {
        echo "<p style='color: orange;'>⚠️ سيتم تغيير ad_id إلى reported_ad_id</p>";
        
        // الخطوة 2: حفظ البيانات الموجودة
        $existing_data = $db->fetchAll("SELECT * FROM reports");
        echo "<p>✅ تم حفظ " . count($existing_data) . " سجل</p>";
        
        // الخطوة 3: حذف الجدول القديم
        $db->query("DROP TABLE reports");
        echo "<p>✅ تم حذف الجدول القديم</p>";
        
        // الخطوة 4: إنشاء الجدول الجديد
        $db->query("
            CREATE TABLE reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                reporter_id INTEGER NOT NULL,
                reported_ad_id INTEGER NOT NULL,
                report_type TEXT NOT NULL,
                reason TEXT,
                status TEXT DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (reporter_id) REFERENCES users(id),
                FOREIGN KEY (reported_ad_id) REFERENCES ads(id)
            )
        ");
        echo "<p>✅ تم إنشاء الجدول الجديد</p>";
        
        // الخطوة 5: استعادة البيانات
        foreach ($existing_data as $row) {
            $ad_id = $row['ad_id'] ?? null;
            if ($ad_id) {
                $db->query("
                    INSERT INTO reports (id, user_id, reporter_id, reported_ad_id, report_type, reason, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ", [
                    $row['id'],
                    $row['user_id'],
                    $row['reporter_id'] ?? $row['user_id'],
                    $ad_id, // استخدام ad_id القديم كـ reported_ad_id الجديد
                    $row['report_type'] ?? 'other',
                    $row['reason'] ?? '',
                    $row['status'] ?? 'pending',
                    $row['created_at'] ?? date('Y-m-d H:i:s')
                ]);
            }
        }
        echo "<p>✅ تم استعادة البيانات</p>";
        
    } elseif ($has_reported_ad_id && !$has_ad_id) {
        echo "<p style='color: green;'>✅ الجدول صحيح بالفعل</p>";
        
    } elseif (!$has_ad_id && !$has_reported_ad_id) {
        echo "<p style='color: red;'>❌ الجدول لا يحتوي على أي من العمودين</p>";
        
        // إنشاء الجدول من الصفر
        $db->query("
            CREATE TABLE IF NOT EXISTS reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                reporter_id INTEGER NOT NULL,
                reported_ad_id INTEGER NOT NULL,
                report_type TEXT NOT NULL,
                reason TEXT,
                status TEXT DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (reporter_id) REFERENCES users(id),
                FOREIGN KEY (reported_ad_id) REFERENCES ads(id)
            )
        ");
        echo "<p>✅ تم إنشاء الجدول من الصفر</p>";
    }
    
    // الخطوة 6: اختبار الإدراج
    echo "<h4>اختبار الإدراج:</h4>";
    $test_user = $db->fetch("SELECT id FROM users LIMIT 1");
    $test_ad = $db->fetch("SELECT id FROM ads LIMIT 1");
    
    if ($test_user && $test_ad) {
        try {
            $db->query("
                INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, created_at) 
                VALUES (?, ?, ?, ?, ?, datetime('now'))
            ", [$test_user['id'], $test_user['id'], $test_ad['id'], 'test', 'اختبار نهائي']);
            
            $inserted = $db->fetch("SELECT id FROM reports WHERE reason = 'اختبار نهائي'");
            if ($inserted) {
                echo "<p style='color: green;'>✅ تم الإدراج بنجاح (ID: {$inserted['id']})</p>";
                $db->query("DELETE FROM reports WHERE reason = 'اختبار نهائي'");
                echo "<p>✅ تم حذف البيانات التجريبية</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ فشل الاختبار: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>🎉 تم الإصلاح بنجاح!</h4>";
    echo "<p>يمكنك الآن اختبار البلاغات من أي صفحة إعلان</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ خطأ في الإصلاح:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
