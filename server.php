<?php
/**
 * خادم PHP المحلي لموقع حراجنا
 * يعمل على المنفذ 3500
 */

// إعدادات الخادم
$host = 'localhost';
$port = 8081;
$document_root = __DIR__;

// التحقق من إمكانية استخدام المنفذ
$socket = @fsockopen($host, $port, $errno, $errstr, 1);
if ($socket) {
    fclose($socket);
    echo "❌ المنفذ $port مستخدم بالفعل. يرجى إيقاف الخدمة الأخرى أو استخدام منفذ آخر.\n";
    exit(1);
}

// إنشاء مجلدات مطلوبة
$required_dirs = [
    'uploads',
    'uploads/ads',
    'uploads/users',
    'config',
    'logs'
];

foreach ($required_dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "✅ تم إنشاء مجلد: $dir\n";
    }
}

// إنشاء ملف .htaccess للحماية
$htaccess_content = "
# حماية ملفات التكوين
<Files ~ \"\\.(env|ini|log|sh|sql)$\">
    Order allow,deny
    Deny from all
</Files>

# إعادة توجيه الأخطاء
ErrorDocument 404 /pages/404.php
ErrorDocument 500 /pages/500.php

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css \"access plus 1 year\"
    ExpiresByType application/javascript \"access plus 1 year\"
    ExpiresByType image/png \"access plus 1 year\"
    ExpiresByType image/jpg \"access plus 1 year\"
    ExpiresByType image/jpeg \"access plus 1 year\"
    ExpiresByType image/gif \"access plus 1 year\"
</IfModule>
";

file_put_contents('.htaccess', $htaccess_content);

// إنشاء ملف robots.txt
$robots_content = "
User-agent: *
Disallow: /admin/
Disallow: /config/
Disallow: /includes/
Disallow: /install/
Disallow: /logs/
Allow: /

Sitemap: http://localhost:$port/sitemap.xml
";

file_put_contents('robots.txt', $robots_content);

// معلومات الخادم
echo "\n";
echo "🚀 بدء تشغيل خادم حراجنا المحلي...\n";
echo "📍 العنوان: http://$host:$port\n";
echo "📁 المجلد الجذر: $document_root\n";
echo "⏰ الوقت: " . date('Y-m-d H:i:s') . "\n";
echo "🔧 إصدار PHP: " . PHP_VERSION . "\n";
echo "\n";

// رسائل مفيدة
echo "📋 روابط مفيدة:\n";
echo "   🏠 الصفحة الرئيسية: http://$host:$port/pages/home.php\n";
echo "   🔧 تثبيت الموقع: http://$host:$port/install/install.php\n";
echo "   👨‍💼 لوحة الإدارة: http://$host:$port/admin/index.php\n";
echo "   📝 تسجيل دخول: http://$host:$port/pages/login.php\n";
echo "   ➕ إضافة إعلان: http://$host:$port/pages/add-ad.php\n";
echo "\n";

echo "💡 نصائح:\n";
echo "   • استخدم Ctrl+C لإيقاف الخادم\n";
echo "   • تأكد من تشغيل MySQL قبل استخدام الموقع\n";
echo "   • قم بزيارة صفحة التثبيت أولاً لإعداد قاعدة البيانات\n";
echo "\n";

echo "🔄 بدء الخادم...\n";
echo "=====================================\n";

// تشغيل الخادم
$command = "php -S $host:$port -t $document_root";

// إضافة معالج للإشارات لإيقاف الخادم بشكل صحيح
if (function_exists('pcntl_signal')) {
    pcntl_signal(SIGINT, function() {
        echo "\n\n🛑 تم إيقاف الخادم بواسطة المستخدم\n";
        echo "👋 شكراً لاستخدام حراجنا!\n";
        exit(0);
    });
}

// تسجيل بدء الخادم
$log_entry = date('Y-m-d H:i:s') . " - Server started on $host:$port\n";
file_put_contents('logs/server.log', $log_entry, FILE_APPEND | LOCK_EX);

// تشغيل الخادم
passthru($command);
?>
