<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h1>🚨 إصلاح طارئ لمشكلة البلاغات</h1>";

try {
    echo "<h3>الخطوة 1: فحص الجدول الحالي</h3>";
    
    // فحص هيكل الجدول
    $structure = $db->fetchAll("PRAGMA table_info(reports)");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'><th>اسم العمود</th><th>النوع</th><th>NOT NULL</th></tr>";
    
    $has_ad_id = false;
    $has_reported_ad_id = false;
    
    foreach ($structure as $col) {
        $highlight = '';
        if ($col['name'] === 'ad_id') {
            $has_ad_id = true;
            $highlight = 'background: #ffcccc;'; // أحمر للعمود الخطأ
        }
        if ($col['name'] === 'reported_ad_id') {
            $has_reported_ad_id = true;
            $highlight = 'background: #ccffcc;'; // أخضر للعمود الصحيح
        }
        
        echo "<tr style='$highlight'>";
        echo "<td><strong>{$col['name']}</strong></td>";
        echo "<td>{$col['type']}</td>";
        echo "<td>" . ($col['notnull'] ? 'نعم' : 'لا') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><strong>النتيجة:</strong></p>";
    echo "<ul>";
    echo "<li>ad_id موجود: " . ($has_ad_id ? "❌ نعم (مشكلة!)" : "✅ لا") . "</li>";
    echo "<li>reported_ad_id موجود: " . ($has_reported_ad_id ? "✅ نعم" : "❌ لا (مشكلة!)") . "</li>";
    echo "</ul>";
    
    if ($has_ad_id && !$has_reported_ad_id) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>🚨 المشكلة مؤكدة!</h4>";
        echo "<p>الجدول يحتوي على العمود الخطأ <code>ad_id</code> بدلاً من <code>reported_ad_id</code></p>";
        echo "<p>الكود يحاول الإدراج في <code>reported_ad_id</code> لكن الجدول لا يحتوي على هذا العمود</p>";
        echo "</div>";
        
        echo "<h3>الخطوة 2: حفظ البيانات الموجودة</h3>";
        $existing_reports = $db->fetchAll("SELECT * FROM reports");
        echo "<p>✅ تم العثور على " . count($existing_reports) . " بلاغ موجود</p>";
        
        echo "<h3>الخطوة 3: إعادة إنشاء الجدول بالهيكل الصحيح</h3>";
        
        // حذف الجدول القديم
        $db->query("DROP TABLE IF EXISTS reports");
        echo "<p>✅ تم حذف الجدول القديم</p>";
        
        // إنشاء الجدول الجديد بالهيكل الصحيح
        $db->query("
            CREATE TABLE reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                reporter_id INTEGER NOT NULL,
                reported_ad_id INTEGER NOT NULL,
                report_type TEXT NOT NULL,
                reason TEXT,
                status TEXT DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (reporter_id) REFERENCES users(id),
                FOREIGN KEY (reported_ad_id) REFERENCES ads(id)
            )
        ");
        echo "<p>✅ تم إنشاء الجدول الجديد بالهيكل الصحيح</p>";
        
        echo "<h3>الخطوة 4: استعادة البيانات</h3>";
        $restored = 0;
        foreach ($existing_reports as $report) {
            try {
                // استخدام ad_id القديم كـ reported_ad_id الجديد
                $ad_id = $report['ad_id'] ?? null;
                if ($ad_id) {
                    $db->query("
                        INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, status, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ", [
                        $report['user_id'],
                        $report['reporter_id'] ?? $report['user_id'],
                        $ad_id, // استخدام ad_id القديم كـ reported_ad_id الجديد
                        $report['report_type'] ?? 'other',
                        $report['reason'] ?? '',
                        $report['status'] ?? 'pending',
                        $report['created_at'] ?? date('Y-m-d H:i:s')
                    ]);
                    $restored++;
                }
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ تعذر استعادة بلاغ: " . $e->getMessage() . "</p>";
            }
        }
        echo "<p>✅ تم استعادة $restored بلاغ من أصل " . count($existing_reports) . "</p>";
        
    } elseif ($has_reported_ad_id && !$has_ad_id) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>✅ الجدول صحيح!</h4>";
        echo "<p>الجدول يحتوي على العمود الصحيح <code>reported_ad_id</code></p>";
        echo "</div>";
        
    } elseif ($has_ad_id && $has_reported_ad_id) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>⚠️ الجدول يحتوي على العمودين!</h4>";
        echo "<p>سيتم حذف العمود الخطأ <code>ad_id</code> والاحتفاظ بـ <code>reported_ad_id</code></p>";
        echo "</div>";
        
        // إعادة إنشاء الجدول بدون ad_id
        $existing_reports = $db->fetchAll("SELECT * FROM reports");
        
        $db->query("DROP TABLE reports");
        $db->query("
            CREATE TABLE reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                reporter_id INTEGER NOT NULL,
                reported_ad_id INTEGER NOT NULL,
                report_type TEXT NOT NULL,
                reason TEXT,
                status TEXT DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (reporter_id) REFERENCES users(id),
                FOREIGN KEY (reported_ad_id) REFERENCES ads(id)
            )
        ");
        
        foreach ($existing_reports as $report) {
            $ad_id = $report['reported_ad_id'] ?? $report['ad_id'] ?? null;
            if ($ad_id) {
                $db->query("
                    INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ", [
                    $report['user_id'],
                    $report['reporter_id'] ?? $report['user_id'],
                    $ad_id,
                    $report['report_type'] ?? 'other',
                    $report['reason'] ?? '',
                    $report['status'] ?? 'pending',
                    $report['created_at'] ?? date('Y-m-d H:i:s')
                ]);
            }
        }
        echo "<p>✅ تم تنظيف الجدول وإزالة العمود الخطأ</p>";
    }
    
    echo "<h3>الخطوة 5: فحص الجدول الجديد</h3>";
    $new_structure = $db->fetchAll("PRAGMA table_info(reports)");
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'><th>اسم العمود</th><th>النوع</th><th>NOT NULL</th></tr>";
    foreach ($new_structure as $col) {
        $highlight = ($col['name'] === 'reported_ad_id') ? 'background: #d4edda;' : '';
        echo "<tr style='$highlight'>";
        echo "<td><strong>{$col['name']}</strong></td>";
        echo "<td>{$col['type']}</td>";
        echo "<td>" . ($col['notnull'] ? 'نعم' : 'لا') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>الخطوة 6: اختبار فوري</h3>";
    
    // جلب بيانات للاختبار
    $test_user = $db->fetch("SELECT id FROM users LIMIT 1");
    $test_ad = $db->fetch("SELECT id FROM ads LIMIT 1");
    
    if ($test_user && $test_ad) {
        try {
            // اختبار الإدراج بنفس الطريقة المستخدمة في ad-details.php
            $test_reason = "اختبار طارئ - " . date('Y-m-d H:i:s');
            
            echo "<p><strong>اختبار الإدراج:</strong></p>";
            echo "<pre>INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, created_at) 
VALUES ({$test_user['id']}, {$test_user['id']}, {$test_ad['id']}, 'test', '$test_reason', datetime('now'))</pre>";
            
            $db->query("
                INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, created_at) 
                VALUES (?, ?, ?, ?, ?, datetime('now'))
            ", [
                $test_user['id'], 
                $test_user['id'], 
                $test_ad['id'], 
                'test', 
                $test_reason
            ]);
            
            $inserted = $db->fetch("SELECT * FROM reports WHERE reason = ? ORDER BY id DESC LIMIT 1", [$test_reason]);
            if ($inserted) {
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>🎉 نجح الاختبار الفوري!</h4>";
                echo "<p><strong>تفاصيل البلاغ المُدرج:</strong></p>";
                echo "<ul>";
                echo "<li>ID: {$inserted['id']}</li>";
                echo "<li>user_id: {$inserted['user_id']}</li>";
                echo "<li>reporter_id: {$inserted['reporter_id']}</li>";
                echo "<li>reported_ad_id: {$inserted['reported_ad_id']}</li>";
                echo "<li>report_type: {$inserted['report_type']}</li>";
                echo "<li>created_at: {$inserted['created_at']}</li>";
                echo "</ul>";
                echo "</div>";
                
                // حذف البيانات التجريبية
                $db->query("DELETE FROM reports WHERE id = ?", [$inserted['id']]);
                echo "<p>✅ تم حذف البيانات التجريبية</p>";
            }
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h4>❌ فشل الاختبار الفوري!</h4>";
            echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
            echo "<p><strong>كود الخطأ:</strong> " . $e->getCode() . "</p>";
            echo "</div>";
        }
    }
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;'>";
    echo "<h2>🎉 تم الإصلاح الطارئ!</h2>";
    echo "<p>جدول البلاغات الآن يحتوي على الهيكل الصحيح</p>";
    echo "<p><strong>العمود الصحيح:</strong> <code>reported_ad_id</code></p>";
    echo "<p>يمكنك الآن اختبار البلاغات من أي صفحة إعلان</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ في الإصلاح الطارئ:</h4>";
    echo "<p><strong>الرسالة:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>🧪 اختبر المشكلة الآن:</h3>";
echo "<div style='text-align: center;'>";

// جلب بعض الإعلانات للاختبار
try {
    $test_ads = $db->fetchAll("SELECT id, title FROM ads LIMIT 4");
    foreach ($test_ads as $ad) {
        echo "<a href='pages/ad-details.php?id={$ad['id']}' target='_blank' style='margin: 5px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>اختبار البلاغ - إعلان {$ad['id']}</a>";
    }
} catch (Exception $e) {
    echo "<p>خطأ في جلب الإعلانات: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div style='text-align: center; margin-top: 20px;'>";
echo "<a href='final-verification.php' style='margin: 5px; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>التحقق النهائي</a>";
echo "</div>";
?>
