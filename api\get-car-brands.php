<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';

try {
    // جلب ماركات السيارات النشطة مرتبة حسب الشعبية والترتيب
    $brands = $db->fetchAll("
        SELECT id, name, arabic_name, logo_path, is_popular, sort_order 
        FROM car_brands 
        WHERE is_active = 1 
        ORDER BY is_popular DESC, sort_order ASC, name ASC
    ");
    
    echo json_encode([
        'success' => true,
        'brands' => $brands,
        'count' => count($brands)
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في جلب البيانات: ' . $e->getMessage(),
        'brands' => []
    ]);
}
?>
