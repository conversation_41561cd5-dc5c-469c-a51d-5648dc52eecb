<?php
// تطبيق التصميم الحديث على جميع الصفحات
echo "<h1>🎨 تطبيق التصميم الاحترافي الموحد</h1>";

$pages_to_update = [
    'pages/home.php',
    'pages/categories.php', 
    'pages/ad-details.php',
    'pages/messages.php',
    'pages/contact.php',
    'pages/login.php',
    'pages/register.php',
    'pages/profile.php',
    'pages/my-ads.php',
    'pages/add-ad.php',
    'pages/search.php',
    'pages/favorites.php'
];

$admin_pages = [
    'admin/index.php',
    'admin/users.php',
    'admin/categories.php',
    'admin/ads.php',
    'admin/messages.php',
    'admin/reports.php',
    'admin/announcements.php',
    'admin/settings.php'
];

echo "<h2>📋 خطة التحديث:</h2>";
echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>✅ ما تم إنجازه:</h3>";
echo "<ul>";
echo "<li>✅ إنشاء ملف CSS احترافي موحد (modern-style.css)</li>";
echo "<li>✅ إنشاء header احترافي جديد (modern-header.php)</li>";
echo "<li>✅ إنشاء footer احترافي جديد (modern-footer.php)</li>";
echo "<li>✅ إنشاء صفحة اختبار للتصميم (test-modern-design.php)</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3e0; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🔄 المطلوب تحديثه:</h3>";
echo "<ul>";
foreach ($pages_to_update as $page) {
    $exists = file_exists($page) ? "✅" : "❌";
    echo "<li>$exists $page</li>";
}
echo "</ul>";
echo "</div>";

echo "<div style='background: #f3e5f5; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🛠️ صفحات الإدارة:</h3>";
echo "<ul>";
foreach ($admin_pages as $page) {
    $exists = file_exists($page) ? "✅" : "❌";
    echo "<li>$exists $page</li>";
}
echo "</ul>";
echo "</div>";

echo "<h2>🎯 الميزات الجديدة في التصميم:</h2>";
echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<ul>";
echo "<li>🎨 <strong>نظام ألوان احترافي:</strong> ألوان متدرجة وحديثة</li>";
echo "<li>📱 <strong>تصميم متجاوب:</strong> يعمل على جميع الأجهزة</li>";
echo "<li>⚡ <strong>أداء محسن:</strong> تحميل سريع وسلس</li>";
echo "<li>🔧 <strong>مكونات موحدة:</strong> أزرار وبطاقات وعناصر متسقة</li>";
echo "<li>✨ <strong>تأثيرات تفاعلية:</strong> انتقالات وحركات سلسة</li>";
echo "<li>🌙 <strong>دعم الوضع المظلم:</strong> تلقائي حسب تفضيل النظام</li>";
echo "<li>♿ <strong>إمكانية الوصول:</strong> متوافق مع معايير الوصول</li>";
echo "<li>🚀 <strong>تحسين SEO:</strong> بنية محسنة لمحركات البحث</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🔗 روابط الاختبار:</h2>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;'>";

// روابط التصميم الجديد
echo "<div style='background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);'>";
echo "<h4 style='color: #2563eb; margin-bottom: 15px;'>🎨 التصميم الجديد</h4>";
echo "<ul style='list-style: none; padding: 0;'>";
echo "<li style='margin: 10px 0;'><a href='test-modern-design.php' target='_blank' style='color: #2563eb; text-decoration: none; font-weight: 500;'>🧪 صفحة اختبار التصميم</a></li>";
echo "<li style='margin: 10px 0;'><a href='assets/css/modern-style.css' target='_blank' style='color: #2563eb; text-decoration: none; font-weight: 500;'>📄 ملف CSS الجديد</a></li>";
echo "<li style='margin: 10px 0;'><a href='pages/includes/modern-header.php' target='_blank' style='color: #2563eb; text-decoration: none; font-weight: 500;'>📄 Header الجديد</a></li>";
echo "<li style='margin: 10px 0;'><a href='pages/includes/modern-footer.php' target='_blank' style='color: #2563eb; text-decoration: none; font-weight: 500;'>📄 Footer الجديد</a></li>";
echo "</ul>";
echo "</div>";

// الصفحات الحالية
echo "<div style='background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);'>";
echo "<h4 style='color: #059669; margin-bottom: 15px;'>📄 الصفحات الحالية</h4>";
echo "<ul style='list-style: none; padding: 0;'>";
echo "<li style='margin: 10px 0;'><a href='pages/home.php' target='_blank' style='color: #059669; text-decoration: none; font-weight: 500;'>🏠 الصفحة الرئيسية</a></li>";
echo "<li style='margin: 10px 0;'><a href='pages/categories.php' target='_blank' style='color: #059669; text-decoration: none; font-weight: 500;'>📂 الفئات</a></li>";
echo "<li style='margin: 10px 0;'><a href='pages/ad-details.php?id=1' target='_blank' style='color: #059669; text-decoration: none; font-weight: 500;'>📋 تفاصيل الإعلان</a></li>";
echo "<li style='margin: 10px 0;'><a href='pages/messages.php' target='_blank' style='color: #059669; text-decoration: none; font-weight: 500;'>💬 الرسائل</a></li>";
echo "<li style='margin: 10px 0;'><a href='pages/contact.php' target='_blank' style='color: #059669; text-decoration: none; font-weight: 500;'>📞 اتصل بنا</a></li>";
echo "</ul>";
echo "</div>";

// صفحات الإدارة
echo "<div style='background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);'>";
echo "<h4 style='color: #dc2626; margin-bottom: 15px;'>⚙️ لوحة الإدارة</h4>";
echo "<ul style='list-style: none; padding: 0;'>";
echo "<li style='margin: 10px 0;'><a href='admin/index.php' target='_blank' style='color: #dc2626; text-decoration: none; font-weight: 500;'>🏠 الرئيسية</a></li>";
echo "<li style='margin: 10px 0;'><a href='admin/users.php' target='_blank' style='color: #dc2626; text-decoration: none; font-weight: 500;'>👥 المستخدمين</a></li>";
echo "<li style='margin: 10px 0;'><a href='admin/categories.php' target='_blank' style='color: #dc2626; text-decoration: none; font-weight: 500;'>📂 الفئات</a></li>";
echo "<li style='margin: 10px 0;'><a href='admin/reports.php' target='_blank' style='color: #dc2626; text-decoration: none; font-weight: 500;'>📊 التقارير</a></li>";
echo "<li style='margin: 10px 0;'><a href='admin/announcements.php' target='_blank' style='color: #dc2626; text-decoration: none; font-weight: 500;'>📢 الإعلانات</a></li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "<h2>📝 تعليمات التطبيق:</h2>";
echo "<div style='background: #fef7cd; padding: 20px; border-radius: 10px; margin: 20px 0; border-right: 4px solid #f59e0b;'>";
echo "<h4>لتطبيق التصميم الجديد على أي صفحة:</h4>";
echo "<ol>";
echo "<li><strong>استبدل include header:</strong><br>";
echo "<code style='background: #f3f4f6; padding: 5px; border-radius: 3px;'>include 'includes/modern-header.php';</code></li>";
echo "<li><strong>استبدل include footer:</strong><br>";
echo "<code style='background: #f3f4f6; padding: 5px; border-radius: 3px;'>include 'includes/modern-footer.php';</code></li>";
echo "<li><strong>أضف الفئات المناسبة للعناصر:</strong><br>";
echo "- استخدم <code style='background: #f3f4f6; padding: 2px; border-radius: 3px;'>btn btn-primary</code> للأزرار<br>";
echo "- استخدم <code style='background: #f3f4f6; padding: 2px; border-radius: 3px;'>card</code> للبطاقات<br>";
echo "- استخدم <code style='background: #f3f4f6; padding: 2px; border-radius: 3px;'>alert alert-success</code> للتنبيهات</li>";
echo "</ol>";
echo "</div>";

echo "<h2>🎨 مثال على التحديث:</h2>";
echo "<div style='background: #f8fafc; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h4>قبل التحديث:</h4>";
echo "<pre style='background: #fee2e2; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
echo htmlspecialchars('<?php include "includes/header.php"; ?>
<div class="container">
    <button class="btn btn-primary">زر عادي</button>
</div>
<?php include "includes/footer.php"; ?>');
echo "</pre>";

echo "<h4>بعد التحديث:</h4>";
echo "<pre style='background: #dcfce7; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
echo htmlspecialchars('<?php include "includes/modern-header.php"; ?>
<div class="container">
    <button class="btn btn-primary">
        <i class="fas fa-star me-2"></i>
        زر احترافي
    </button>
</div>
<?php include "includes/modern-footer.php"; ?>');
echo "</pre>";
echo "</div>";

echo "<div style='background: #d1fae5; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>";
echo "<h3 style='color: #065f46; margin-bottom: 15px;'>🎉 التصميم الاحترافي جاهز!</h3>";
echo "<p style='color: #047857; margin-bottom: 20px;'>يمكنك الآن تطبيق التصميم الجديد على جميع الصفحات للحصول على مظهر احترافي وموحد.</p>";
echo "<div style='display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;'>";
echo "<a href='test-modern-design.php' target='_blank' style='background: #059669; color: white; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600;'>";
echo "🧪 اختبر التصميم الجديد";
echo "</a>";
echo "<a href='pages/home.php' target='_blank' style='background: #2563eb; color: white; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600;'>";
echo "🏠 عرض الصفحة الرئيسية";
echo "</a>";
echo "</div>";
echo "</div>";

// إضافة CSS للتنسيق
echo "<style>
body { 
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
    line-height: 1.6; 
    margin: 20px; 
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    direction: rtl;
    text-align: right;
}
h1, h2, h3, h4 { 
    color: #1e293b; 
    margin-bottom: 1rem; 
}
a { 
    transition: all 0.3s ease; 
}
a:hover { 
    transform: translateY(-2px); 
}
code {
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}
pre {
    font-family: 'Courier New', monospace;
    font-size: 0.85em;
    line-height: 1.4;
    direction: ltr;
    text-align: left;
}
</style>";

echo "<link href='https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap' rel='stylesheet'>";
?>
