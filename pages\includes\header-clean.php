<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($page_title) ? $page_title . ' - ' : '' ?>حراجنا</title>
    <meta name="description" content="<?= isset($page_description) ? $page_description : 'موقع حراجنا للإعلانات المبوبة' ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <!-- Performance Optimization -->
    <link rel="preload" href="../assets/css/style.css" as="style">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    
    <!-- Theme Color -->
    <meta name="theme-color" content="#3b82f6">
</head>
<body class="fade-in">

<!-- شريط التنقل -->
<nav class="navbar navbar-expand-lg fixed-top">
    <div class="container">
        <!-- الشعار -->
        <a class="navbar-brand" href="home.php">
            <i class="fas fa-store me-2"></i>
            حراجنا
        </a>

        <!-- زر القائمة للجوال -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon">
                <i class="fas fa-bars"></i>
            </span>
        </button>

        <!-- قائمة التنقل -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'home.php' ? 'active' : '' ?>" href="home.php">
                        <i class="fas fa-home me-1"></i>
                        الرئيسية
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'categories.php' ? 'active' : '' ?>" href="categories.php">
                        <i class="fas fa-th-large me-1"></i>
                        الفئات
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'search.php' ? 'active' : '' ?>" href="search.php">
                        <i class="fas fa-search me-1"></i>
                        البحث
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'contact.php' ? 'active' : '' ?>" href="contact.php">
                        <i class="fas fa-envelope me-1"></i>
                        اتصل بنا
                    </a>
                </li>
            </ul>

            <!-- قائمة المستخدم -->
            <ul class="navbar-nav">
                <?php if (isLoggedIn()): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" 
                           data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i>
                            <?= $_SESSION['username'] ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="profile.php">
                                    <i class="fas fa-user me-2"></i>
                                    الملف الشخصي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="my-ads.php">
                                    <i class="fas fa-bullhorn me-2"></i>
                                    إعلاناتي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="messages.php">
                                    <i class="fas fa-envelope me-2"></i>
                                    الرسائل
                                    <?php
                                    $unread_count = 0;
                                    if (isset($db)) {
                                        try {
                                            $unread = $db->fetch("SELECT COUNT(*) as count FROM messages WHERE receiver_id = ? AND is_read = 0", [$_SESSION['user_id']]);
                                            $unread_count = $unread['count'] ?? 0;
                                        } catch (Exception $e) {
                                            $unread_count = 0;
                                        }
                                    }
                                    if ($unread_count > 0):
                                    ?>
                                        <span class="badge bg-danger ms-1"><?= $unread_count ?></span>
                                    <?php endif; ?>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="favorites.php">
                                    <i class="fas fa-heart me-2"></i>
                                    المفضلة
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="add-ad.php">
                                    <i class="fas fa-plus-circle me-2"></i>
                                    إضافة إعلان
                                </a>
                            </li>
                            <?php if (isAdmin()): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="../admin/index.php">
                                        <i class="fas fa-cog me-2"></i>
                                        لوحة الإدارة
                                    </a>
                                </li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </li>
                <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link" href="login.php">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            تسجيل الدخول
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-primary text-white ms-2" href="register.php">
                            <i class="fas fa-user-plus me-1"></i>
                            إنشاء حساب
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</nav>

<!-- مساحة للتعويض عن الشريط الثابت -->
<div style="height: 80px;"></div>

<!-- JavaScript للتفاعل مع الشريط -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const navbar = document.querySelector('.navbar');
    
    // تأثير التمرير
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
    
    // إغلاق القائمة عند النقر على رابط (للجوال)
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    const navbarCollapse = document.querySelector('.navbar-collapse');
    
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            if (navbarCollapse.classList.contains('show')) {
                const bsCollapse = new bootstrap.Collapse(navbarCollapse);
                bsCollapse.hide();
            }
        });
    });
});
</script>

<!-- بداية المحتوى الرئيسي -->
<main class="main-content">
