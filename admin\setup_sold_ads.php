<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدمن
requireAdmin();

try {
    // إضافة أعمدة للإعلانات المباعة
    try {
        $db->query("ALTER TABLE ads ADD COLUMN is_sold BOOLEAN DEFAULT FALSE");
        echo "تم إضافة عمود is_sold بنجاح<br>";
    } catch (Exception $e) {
        echo "عمود is_sold موجود مسبقاً<br>";
    }
    
    try {
        $db->query("ALTER TABLE ads ADD COLUMN sold_at TIMESTAMP NULL");
        echo "تم إضافة عمود sold_at بنجاح<br>";
    } catch (Exception $e) {
        echo "عمود sold_at موجود مسبقاً<br>";
    }
    
    try {
        $db->query("ALTER TABLE ads ADD COLUMN hide_sold_at TIMESTAMP NULL");
        echo "تم إضافة عمود hide_sold_at بنجاح<br>";
    } catch (Exception $e) {
        echo "عمود hide_sold_at موجود مسبقاً<br>";
    }
    
    // إضافة فهارس للأداء
    try {
        $db->query("ALTER TABLE ads ADD INDEX idx_is_sold (is_sold)");
        echo "تم إضافة فهرس is_sold بنجاح<br>";
    } catch (Exception $e) {
        echo "فهرس is_sold موجود مسبقاً<br>";
    }
    
    try {
        $db->query("ALTER TABLE ads ADD INDEX idx_sold_at (sold_at)");
        echo "تم إضافة فهرس sold_at بنجاح<br>";
    } catch (Exception $e) {
        echo "فهرس sold_at موجود مسبقاً<br>";
    }
    
    echo "<br>تم إعداد نظام الإعلانات المباعة بنجاح!";
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
