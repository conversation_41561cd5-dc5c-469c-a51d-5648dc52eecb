    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- DataTables -->
    <script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Custom JavaScript -->
    <script>
        $(document).ready(function() {
            // تفعيل DataTables
            $('.data-table').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
                },
                "pageLength": 25,
                "responsive": true,
                "order": [[ 0, "desc" ]]
            });
            
            // تأكيد الحذف
            $('.delete-btn').on('click', function(e) {
                e.preventDefault();
                const url = $(this).attr('href');
                const title = $(this).data('title') || 'هذا العنصر';
                
                Swal.fire({
                    title: 'هل أنت متأكد؟',
                    text: `سيتم حذف ${title} نهائياً`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'نعم، احذف',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = url;
                    }
                });
            });
            
            // تأكيد الإجراءات المهمة
            $('.confirm-action').on('click', function(e) {
                e.preventDefault();
                const url = $(this).attr('href');
                const action = $(this).data('action') || 'تنفيذ هذا الإجراء';
                
                Swal.fire({
                    title: 'تأكيد الإجراء',
                    text: `هل تريد ${action}؟`,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'نعم',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = url;
                    }
                });
            });
            
            // إخفاء الرسائل تلقائياً
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
            
            // تحديث الوقت الحالي
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleString('ar-SA', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
                $('#current-time').text(timeString);
            }
            
            if ($('#current-time').length) {
                updateTime();
                setInterval(updateTime, 60000); // تحديث كل دقيقة
            }
            
            // تفعيل tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            // تفعيل popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
            
            // معاينة الصور قبل الرفع
            $('.image-input').on('change', function() {
                const input = this;
                const preview = $(input).siblings('.image-preview');
                
                if (input.files && input.files[0]) {
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        preview.html(`<img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">`);
                    };
                    
                    reader.readAsDataURL(input.files[0]);
                }
            });
            
            // تحديد/إلغاء تحديد جميع العناصر
            $('#select-all').on('change', function() {
                $('.item-checkbox').prop('checked', this.checked);
                updateBulkActions();
            });
            
            $('.item-checkbox').on('change', function() {
                updateBulkActions();
            });
            
            function updateBulkActions() {
                const checkedCount = $('.item-checkbox:checked').length;
                if (checkedCount > 0) {
                    $('.bulk-actions').show();
                    $('.bulk-count').text(checkedCount);
                } else {
                    $('.bulk-actions').hide();
                }
            }
            
            // إجراءات مجمعة
            $('.bulk-action-btn').on('click', function(e) {
                e.preventDefault();
                const action = $(this).data('action');
                const checkedItems = $('.item-checkbox:checked').map(function() {
                    return this.value;
                }).get();
                
                if (checkedItems.length === 0) {
                    Swal.fire('تنبيه', 'يرجى تحديد عنصر واحد على الأقل', 'warning');
                    return;
                }
                
                Swal.fire({
                    title: 'تأكيد الإجراء المجمع',
                    text: `سيتم تطبيق الإجراء على ${checkedItems.length} عنصر`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'تأكيد',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // إرسال الطلب
                        $.post(window.location.href, {
                            bulk_action: action,
                            items: checkedItems,
                            csrf_token: $('meta[name="csrf-token"]').attr('content')
                        }).done(function(response) {
                            location.reload();
                        }).fail(function() {
                            Swal.fire('خطأ', 'حدث خطأ أثناء تنفيذ الإجراء', 'error');
                        });
                    }
                });
            });
            
            // البحث المباشر
            $('.live-search').on('keyup', function() {
                const searchTerm = $(this).val().toLowerCase();
                const targetTable = $($(this).data('target'));
                
                targetTable.find('tbody tr').each(function() {
                    const rowText = $(this).text().toLowerCase();
                    $(this).toggle(rowText.indexOf(searchTerm) > -1);
                });
            });
            
            // تحديث الإحصائيات تلقائياً
            function updateStats() {
                $.get('ajax/get_stats.php').done(function(data) {
                    if (data.success) {
                        Object.keys(data.stats).forEach(function(key) {
                            $(`#stat-${key}`).text(data.stats[key]);
                        });
                    }
                });
            }
            
            // تحديث الإحصائيات كل 5 دقائق
            if ($('.stats-container').length) {
                setInterval(updateStats, 300000);
            }
            
            // تحسين تجربة المستخدم للنماذج
            $('form').on('submit', function() {
                $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...');
            });
            
            // إعادة تحميل الصفحة عند تغيير الفلتر
            $('.filter-select').on('change', function() {
                const url = new URL(window.location);
                url.searchParams.set($(this).attr('name'), $(this).val());
                window.location.href = url.toString();
            });
        });
        
        // دالة لعرض الإشعارات
        function showNotification(message, type = 'success') {
            const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer);
                    toast.addEventListener('mouseleave', Swal.resumeTimer);
                }
            });
            
            Toast.fire({
                icon: type,
                title: message
            });
        }
        
        // دالة لتحديث العداد
        function updateCounter(element, newValue) {
            const $element = $(element);
            const currentValue = parseInt($element.text().replace(/,/g, ''));
            
            $({ counter: currentValue }).animate({ counter: newValue }, {
                duration: 1000,
                easing: 'swing',
                step: function() {
                    $element.text(Math.ceil(this.counter).toLocaleString());
                }
            });
        }
        
        // دالة لتصدير البيانات
        function exportData(format, table = 'data-table') {
            const data = [];
            const headers = [];
            
            // جمع العناوين
            $(`.${table} thead th`).each(function() {
                headers.push($(this).text().trim());
            });
            
            // جمع البيانات
            $(`.${table} tbody tr:visible`).each(function() {
                const row = [];
                $(this).find('td').each(function() {
                    row.push($(this).text().trim());
                });
                data.push(row);
            });
            
            if (format === 'csv') {
                exportToCSV(headers, data);
            } else if (format === 'excel') {
                exportToExcel(headers, data);
            }
        }
        
        function exportToCSV(headers, data) {
            let csv = headers.join(',') + '\n';
            data.forEach(row => {
                csv += row.map(cell => `"${cell}"`).join(',') + '\n';
            });
            
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `export_${new Date().getTime()}.csv`;
            link.click();
        }
    </script>
    
    <?php if (isset($extra_js)): ?>
        <?= $extra_js ?>
    <?php endif; ?>
</body>
</html>
