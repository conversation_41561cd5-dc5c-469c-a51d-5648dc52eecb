<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// تسجيل دخول تجريبي
if (!isLoggedIn()) {
    $user = $db->fetch("SELECT * FROM users LIMIT 1");
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['logged_in'] = true;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي - حراجنا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-success { background: #d4edda; border: 1px solid #c3e6cb; }
        .test-warning { background: #fff3cd; border: 1px solid #ffeaa7; }
        .test-error { background: #f8d7da; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="text-center mb-5">
            <h1><i class="fas fa-check-circle text-success"></i> اختبار نهائي</h1>
            <p class="lead">التحقق من أن جميع الإصلاحات تعمل بشكل صحيح</p>
        </div>

        <?php if (isLoggedIn()): ?>
            <div class="alert alert-success">
                <i class="fas fa-user-check"></i> تم تسجيل الدخول كـ: <strong><?= $_SESSION['username'] ?></strong>
            </div>

            <div class="row">
                <!-- اختبار البلاغات -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h5><i class="fas fa-flag"></i> اختبار البلاغات</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $reports_test_result = "success";
                            $reports_message = "";
                            
                            try {
                                // فحص هيكل الجدول
                                $structure = $db->fetchAll("PRAGMA table_info(reports)");
                                $has_reported_ad_id = false;
                                foreach ($structure as $col) {
                                    if ($col['name'] === 'reported_ad_id') {
                                        $has_reported_ad_id = true;
                                        break;
                                    }
                                }
                                
                                if (!$has_reported_ad_id) {
                                    $reports_test_result = "error";
                                    $reports_message = "❌ العمود reported_ad_id غير موجود";
                                } else {
                                    // اختبار الإدراج
                                    $test_ad = $db->fetch("SELECT id FROM ads LIMIT 1");
                                    if ($test_ad) {
                                        $db->query("
                                            INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason) 
                                            VALUES (?, ?, ?, ?, ?)
                                        ", [$_SESSION['user_id'], $_SESSION['user_id'], $test_ad['id'], 'test', 'اختبار نهائي']);
                                        
                                        $inserted = $db->fetch("SELECT id FROM reports WHERE reason = 'اختبار نهائي'");
                                        if ($inserted) {
                                            $reports_message = "✅ تم إدراج البلاغ بنجاح (ID: {$inserted['id']})";
                                            $db->query("DELETE FROM reports WHERE reason = 'اختبار نهائي'");
                                        } else {
                                            $reports_test_result = "error";
                                            $reports_message = "❌ فشل في إدراج البلاغ";
                                        }
                                    } else {
                                        $reports_test_result = "warning";
                                        $reports_message = "⚠️ لا توجد إعلانات للاختبار";
                                    }
                                }
                            } catch (Exception $e) {
                                $reports_test_result = "error";
                                $reports_message = "❌ خطأ: " . $e->getMessage();
                            }
                            ?>
                            
                            <div class="alert test-<?= $reports_test_result ?>">
                                <?= $reports_message ?>
                            </div>
                            
                            <?php if ($reports_test_result === "success"): ?>
                                <a href="pages/ad-details.php?id=<?= $test_ad['id'] ?? 1 ?>" class="btn btn-warning" target="_blank">
                                    <i class="fas fa-flag"></i> اختبار البلاغ الحقيقي
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- اختبار المفضلة -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h5><i class="fas fa-heart"></i> اختبار المفضلة</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $favorites_test_result = "success";
                            $favorites_message = "";
                            
                            try {
                                // فحص وجود جدول المفضلة
                                $favorites_structure = $db->fetchAll("PRAGMA table_info(favorites)");
                                if (empty($favorites_structure)) {
                                    // إنشاء جدول المفضلة إذا لم يكن موجوداً
                                    $db->query("
                                        CREATE TABLE favorites (
                                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                                            user_id INTEGER NOT NULL,
                                            ad_id INTEGER NOT NULL,
                                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                                            UNIQUE(user_id, ad_id),
                                            FOREIGN KEY (user_id) REFERENCES users(id),
                                            FOREIGN KEY (ad_id) REFERENCES ads(id)
                                        )
                                    ");
                                }
                                
                                // اختبار الإدراج
                                $test_ad = $db->fetch("SELECT id FROM ads LIMIT 1");
                                if ($test_ad) {
                                    $db->query("INSERT OR IGNORE INTO favorites (user_id, ad_id) VALUES (?, ?)", 
                                              [$_SESSION['user_id'], $test_ad['id']]);
                                    
                                    $inserted = $db->fetch("SELECT id FROM favorites WHERE user_id = ? AND ad_id = ?", 
                                                          [$_SESSION['user_id'], $test_ad['id']]);
                                    if ($inserted) {
                                        $favorites_message = "✅ تم إدراج المفضلة بنجاح (ID: {$inserted['id']})";
                                        $db->query("DELETE FROM favorites WHERE user_id = ? AND ad_id = ?", 
                                                  [$_SESSION['user_id'], $test_ad['id']]);
                                    } else {
                                        $favorites_test_result = "error";
                                        $favorites_message = "❌ فشل في إدراج المفضلة";
                                    }
                                } else {
                                    $favorites_test_result = "warning";
                                    $favorites_message = "⚠️ لا توجد إعلانات للاختبار";
                                }
                            } catch (Exception $e) {
                                $favorites_test_result = "error";
                                $favorites_message = "❌ خطأ: " . $e->getMessage();
                            }
                            ?>
                            
                            <div class="alert test-<?= $favorites_test_result ?>">
                                <?= $favorites_message ?>
                            </div>
                            
                            <?php if ($favorites_test_result === "success"): ?>
                                <a href="test-favorites.php" class="btn btn-danger" target="_blank">
                                    <i class="fas fa-heart"></i> اختبار المفضلة التفاعلي
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ملخص الحالة -->
            <div class="card mt-4">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-chart-line"></i> ملخص الحالة</h5>
                </div>
                <div class="card-body">
                    <?php
                    $overall_status = "success";
                    if ($reports_test_result === "error" || $favorites_test_result === "error") {
                        $overall_status = "error";
                    } elseif ($reports_test_result === "warning" || $favorites_test_result === "warning") {
                        $overall_status = "warning";
                    }
                    ?>
                    
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="card test-<?= $reports_test_result ?>">
                                <div class="card-body">
                                    <h6>البلاغات</h6>
                                    <i class="fas fa-flag fa-2x"></i>
                                    <p class="mt-2">
                                        <?= $reports_test_result === "success" ? "✅ يعمل" : 
                                           ($reports_test_result === "warning" ? "⚠️ تحذير" : "❌ خطأ") ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card test-<?= $favorites_test_result ?>">
                                <div class="card-body">
                                    <h6>المفضلة</h6>
                                    <i class="fas fa-heart fa-2x"></i>
                                    <p class="mt-2">
                                        <?= $favorites_test_result === "success" ? "✅ يعمل" : 
                                           ($favorites_test_result === "warning" ? "⚠️ تحذير" : "❌ خطأ") ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card test-<?= $overall_status ?>">
                                <div class="card-body">
                                    <h6>الحالة العامة</h6>
                                    <i class="fas fa-<?= $overall_status === "success" ? "check-circle" : 
                                                      ($overall_status === "warning" ? "exclamation-triangle" : "times-circle") ?> fa-2x"></i>
                                    <p class="mt-2">
                                        <?= $overall_status === "success" ? "✅ ممتاز" : 
                                           ($overall_status === "warning" ? "⚠️ جيد" : "❌ يحتاج إصلاح") ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="card mt-4">
                <div class="card-header bg-secondary text-white">
                    <h5><i class="fas fa-link"></i> روابط سريعة للاختبار</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="pages/home.php" class="btn btn-outline-primary w-100" target="_blank">
                                <i class="fas fa-home"></i> الصفحة الرئيسية
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="pages/ads.php" class="btn btn-outline-info w-100" target="_blank">
                                <i class="fas fa-list"></i> جميع الإعلانات
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="pages/profile.php" class="btn btn-outline-success w-100" target="_blank">
                                <i class="fas fa-user"></i> الملف الشخصي
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="admin/index.php" class="btn btn-outline-warning w-100" target="_blank">
                                <i class="fas fa-cog"></i> لوحة الإدارة
                            </a>
                        </div>
                    </div>
                </div>
            </div>

        <?php else: ?>
            <div class="alert alert-danger text-center">
                <h4><i class="fas fa-exclamation-triangle"></i> لم يتم تسجيل الدخول</h4>
                <p>يجب تسجيل الدخول لإجراء الاختبارات</p>
                <a href="pages/login.php" class="btn btn-primary">تسجيل الدخول</a>
            </div>
        <?php endif; ?>

        <div class="text-center mt-5">
            <a href="fixes-summary.php" class="btn btn-lg btn-success">
                <i class="fas fa-clipboard-check"></i> عرض ملخص جميع الإصلاحات
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
