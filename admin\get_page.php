<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدمن
requireAdmin();

header('Content-Type: application/json');

if (!isset($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف الصفحة مطلوب']);
    exit();
}

$page_id = (int)$_GET['id'];

try {
    $page = $db->fetch("SELECT * FROM pages WHERE id = ?", [$page_id]);
    
    if ($page) {
        echo json_encode(['success' => true, 'page' => $page]);
    } else {
        echo json_encode(['success' => false, 'message' => 'الصفحة غير موجودة']);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات']);
}
?>
