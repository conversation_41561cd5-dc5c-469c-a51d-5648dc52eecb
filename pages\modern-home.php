<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

try {
    require_once $root_path . '/config/database.php';
    require_once $root_path . '/config/config.php';
    require_once $root_path . '/includes/functions.php';
    require_once $root_path . '/includes/auth.php';
} catch (Exception $e) {
    die("خطأ في تحميل الملفات: " . $e->getMessage());
}

$page_title = 'الصفحة الرئيسية - حراجنا';
$page_description = 'موقع حراجنا للإعلانات المبوبة - أفضل موقع للبيع والشراء في المملكة العربية السعودية';

// جلب الإحصائيات
$stats = [
    'total_ads' => 0,
    'total_users' => 0,
    'total_categories' => 0,
    'active_ads' => 0
];

try {
    if (isset($db)) {
        $stats['total_ads'] = $db->fetch("SELECT COUNT(*) as count FROM ads")['count'] ?? 0;
        $stats['total_users'] = $db->fetch("SELECT COUNT(*) as count FROM users")['count'] ?? 0;
        $stats['total_categories'] = $db->fetch("SELECT COUNT(*) as count FROM categories WHERE is_active = 1")['count'] ?? 0;
        $stats['active_ads'] = $db->fetch("SELECT COUNT(*) as count FROM ads WHERE status = 'active'")['count'] ?? 0;
    }
} catch (Exception $e) {
    // في حالة عدم توفر قاعدة البيانات، استخدم قيم افتراضية
    $stats = [
        'total_ads' => 15420,
        'total_users' => 8750,
        'total_categories' => 12,
        'active_ads' => 12340
    ];
}

// جلب الفئات
$categories = [];
try {
    if (isset($db)) {
        $categories = $db->fetchAll("
            SELECT c.*, COUNT(a.id) as ads_count 
            FROM categories c 
            LEFT JOIN ads a ON c.id = a.category_id AND a.status = 'active'
            WHERE c.is_active = 1 
            GROUP BY c.id 
            ORDER BY ads_count DESC 
            LIMIT 8
        ");
    }
} catch (Exception $e) {
    // فئات افتراضية
    $categories = [
        ['id' => 1, 'name' => 'السيارات', 'icon' => 'fas fa-car', 'ads_count' => 2450],
        ['id' => 2, 'name' => 'العقارات', 'icon' => 'fas fa-home', 'ads_count' => 1890],
        ['id' => 3, 'name' => 'الإلكترونيات', 'icon' => 'fas fa-laptop', 'ads_count' => 1650],
        ['id' => 4, 'name' => 'الأثاث', 'icon' => 'fas fa-couch', 'ads_count' => 980],
        ['id' => 5, 'name' => 'الوظائف', 'icon' => 'fas fa-briefcase', 'ads_count' => 750],
        ['id' => 6, 'name' => 'الخدمات', 'icon' => 'fas fa-tools', 'ads_count' => 620],
        ['id' => 7, 'name' => 'الأزياء', 'icon' => 'fas fa-tshirt', 'ads_count' => 540],
        ['id' => 8, 'name' => 'الرياضة', 'icon' => 'fas fa-football-ball', 'ads_count' => 320]
    ];
}

// جلب أحدث الإعلانات
$latest_ads = [];
try {
    if (isset($db)) {
        $latest_ads = $db->fetchAll("
            SELECT a.*, u.username, c.name as category_name,
                   (SELECT image_path FROM ad_images WHERE ad_id = a.id AND is_primary = 1 LIMIT 1) as primary_image
            FROM ads a
            JOIN users u ON a.user_id = u.id
            JOIN categories c ON a.category_id = c.id
            WHERE a.status = 'active'
            ORDER BY a.created_at DESC
            LIMIT 8
        ");
    }
} catch (Exception $e) {
    // إعلانات افتراضية للعرض
    $latest_ads = [
        [
            'id' => 1,
            'title' => 'سيارة تويوتا كامري 2020',
            'price' => 85000,
            'city' => 'الرياض',
            'category_name' => 'السيارات',
            'primary_image' => null,
            'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
        ],
        [
            'id' => 2,
            'title' => 'شقة للإيجار في جدة',
            'price' => 3500,
            'city' => 'جدة',
            'category_name' => 'العقارات',
            'primary_image' => null,
            'created_at' => date('Y-m-d H:i:s', strtotime('-4 hours'))
        ],
        [
            'id' => 3,
            'title' => 'لابتوب ديل جديد',
            'price' => 2800,
            'city' => 'الدمام',
            'category_name' => 'الإلكترونيات',
            'primary_image' => null,
            'created_at' => date('Y-m-d H:i:s', strtotime('-6 hours'))
        ]
    ];
}

include 'includes/modern-header.php';
?>

<!-- Hero Section -->
<section class="hero-section py-6" style="background: var(--gradient-hero); color: white; min-height: 60vh; display: flex; align-items: center;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4 text-white">
                    مرحباً بك في حراجنا
                </h1>
                <p class="lead mb-4 text-white">
                    أفضل موقع للإعلانات المبوبة في المملكة العربية السعودية. 
                    ابحث واشتري وبع بكل سهولة وأمان.
                </p>
                
                <!-- شريط البحث -->
                <div class="search-bar bg-white rounded-custom p-4 mb-4">
                    <form action="search.php" method="GET" class="row g-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="searchQuery" name="q" placeholder="ابحث عن أي شيء...">
                                <label for="searchQuery">ابحث عن أي شيء...</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating">
                                <select class="form-select" id="searchCity" name="city">
                                    <option value="">جميع المدن</option>
                                    <option value="الرياض">الرياض</option>
                                    <option value="جدة">جدة</option>
                                    <option value="الدمام">الدمام</option>
                                    <option value="مكة">مكة</option>
                                    <option value="المدينة">المدينة المنورة</option>
                                </select>
                                <label for="searchCity">المدينة</label>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary h-100 w-100">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="d-flex gap-3 flex-wrap">
                    <a href="add-ad.php" class="btn btn-light btn-lg">
                        <i class="fas fa-plus-circle me-2"></i>
                        أضف إعلانك
                    </a>
                    <a href="categories.php" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-th-large me-2"></i>
                        تصفح الفئات
                    </a>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <div class="hero-image">
                    <i class="fas fa-store icon-2xl text-white opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="py-6 bg-light">
    <div class="container">
        <div class="row text-center">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card hover-scale h-100">
                    <div class="card-body">
                        <div class="icon-xl text-primary mb-3">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        <h3 class="text-primary"><?= number_format($stats['total_ads']) ?></h3>
                        <p class="text-muted mb-0">إجمالي الإعلانات</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card hover-scale h-100">
                    <div class="card-body">
                        <div class="icon-xl text-success mb-3">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h3 class="text-success"><?= number_format($stats['active_ads']) ?></h3>
                        <p class="text-muted mb-0">إعلان نشط</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card hover-scale h-100">
                    <div class="card-body">
                        <div class="icon-xl text-warning mb-3">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="text-warning"><?= number_format($stats['total_users']) ?></h3>
                        <p class="text-muted mb-0">مستخدم مسجل</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card hover-scale h-100">
                    <div class="card-body">
                        <div class="icon-xl text-info mb-3">
                            <i class="fas fa-th-large"></i>
                        </div>
                        <h3 class="text-info"><?= number_format($stats['total_categories']) ?></h3>
                        <p class="text-muted mb-0">فئة متاحة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="py-6">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="text-gradient">الفئات الشائعة</h2>
                <p class="lead text-muted">اختر الفئة التي تناسبك</p>
            </div>
        </div>
        
        <div class="row g-4">
            <?php foreach ($categories as $category): ?>
                <div class="col-lg-3 col-md-6">
                    <a href="category-ads.php?category=<?= $category['id'] ?>" class="text-decoration-none">
                        <div class="card hover-shadow h-100">
                            <div class="card-body text-center">
                                <div class="icon-xl text-primary mb-3">
                                    <i class="<?= $category['icon'] ?? 'fas fa-folder' ?>"></i>
                                </div>
                                <h5 class="card-title"><?= $category['name'] ?></h5>
                                <p class="text-muted mb-0">
                                    <?= number_format($category['ads_count']) ?> إعلان
                                </p>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-5">
            <a href="categories.php" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-th-large me-2"></i>
                عرض جميع الفئات
            </a>
        </div>
    </div>
</section>

<!-- Latest Ads Section -->
<section class="py-6 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2>أحدث الإعلانات</h2>
                <p class="lead text-muted">تصفح أحدث الإعلانات المضافة</p>
            </div>
        </div>
        
        <div class="row g-4">
            <?php foreach ($latest_ads as $ad): ?>
                <div class="col-lg-3 col-md-6">
                    <div class="ad-card">
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            <?php if ($ad['primary_image']): ?>
                                <img src="../uploads/ads/<?= $ad['primary_image'] ?>" alt="<?= $ad['title'] ?>" class="img-fluid">
                            <?php else: ?>
                                <i class="fas fa-image icon-xl text-muted"></i>
                            <?php endif; ?>
                        </div>
                        <div class="card-body">
                            <h6 class="card-title"><?= substr($ad['title'], 0, 50) ?>...</h6>
                            <div class="price text-primary fw-bold mb-2">
                                <?= number_format($ad['price']) ?> ريال
                            </div>
                            <div class="meta">
                                <small class="text-muted">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    <?= $ad['city'] ?>
                                </small>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    <?= timeAgo($ad['created_at']) ?>
                                </small>
                            </div>
                            <div class="actions">
                                <a href="ad-details.php?id=<?= $ad['id'] ?>" class="btn btn-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>
                                    عرض التفاصيل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-5">
            <a href="search.php" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-search me-2"></i>
                عرض المزيد من الإعلانات
            </a>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-6">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="text-gradient">لماذا حراجنا؟</h2>
                <p class="lead text-muted">نوفر لك أفضل تجربة للبيع والشراء</p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="card hover-shadow h-100">
                    <div class="card-body text-center">
                        <div class="icon-xl text-success mb-3">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h5 class="card-title">آمن وموثوق</h5>
                        <p class="card-text">
                            نضمن لك تجربة آمنة مع نظام حماية متقدم وتحقق من الهوية.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="card hover-shadow h-100">
                    <div class="card-body text-center">
                        <div class="icon-xl text-primary mb-3">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h5 class="card-title">سريع وسهل</h5>
                        <p class="card-text">
                            أضف إعلانك في دقائق واحصل على عروض سريعة من المشترين.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="card hover-shadow h-100">
                    <div class="card-body text-center">
                        <div class="icon-xl text-warning mb-3">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h5 class="card-title">دعم 24/7</h5>
                        <p class="card-text">
                            فريق دعم متاح على مدار الساعة لمساعدتك في أي وقت.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/modern-footer.php'; ?>
