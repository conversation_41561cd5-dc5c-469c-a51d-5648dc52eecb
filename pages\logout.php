<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';

try {
    // تسجيل نشاط تسجيل الخروج في السجلات
    if (isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        // إدراج سجل تسجيل الخروج
        $db->query(
            "INSERT INTO activity_logs (user_id, action_type, description, ip_address, user_agent) VALUES (?, ?, ?, ?, ?)",
            [$user_id, 'logout', 'تسجيل خروج المستخدم', $ip_address, $user_agent]
        );
    }
} catch (Exception $e) {
    // تجاهل أخطاء السجلات
}

// مسح جميع بيانات الجلسة
$_SESSION = array();

// حذف ملف تعريف الارتباط للجلسة إذا كان موجوداً
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// تدمير الجلسة
session_destroy();

// إعادة التوجيه إلى الصفحة الرئيسية مع رسالة نجاح
header('Location: home.php?logout=success');
exit();
?>
