@echo off
chcp 65001 >nul
title 🏪 موقع حراجنا - خادم محلي
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🏪 موقع حراجنا                           ║
echo ║                  نظام الإعلانات المبوبة                    ║
echo ║                     خادم محلي للتطوير                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM التحقق من وجود PHP
echo 🔍 التحقق من PHP...
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: PHP غير مثبت أو غير موجود في PATH
    echo.
    echo 💡 حلول مقترحة:
    echo    1. تثبيت PHP من: https://www.php.net/downloads
    echo    2. إضافة مجلد PHP إلى متغير البيئة PATH
    echo    3. استخدام XAMPP أو WAMP
    echo.
    pause
    exit /b 1
)

echo ✅ PHP متوفر
php --version | findstr "PHP"
echo.

REM إنشاء المجلدات المطلوبة
echo 📁 إنشاء المجلدات المطلوبة...
if not exist "uploads" mkdir uploads
if not exist "uploads\ads" mkdir uploads\ads
if not exist "uploads\users" mkdir uploads\users
if not exist "logs" mkdir logs
if not exist "config" mkdir config
echo ✅ تم إنشاء المجلدات

REM التحقق من الملفات المطلوبة
echo.
echo 📋 التحقق من الملفات المطلوبة...
set "missing_files=0"

if not exist "index.php" (
    echo ❌ index.php مفقود
    set /a missing_files+=1
) else (
    echo ✅ index.php موجود
)

if not exist "pages\home.php" (
    echo ❌ pages\home.php مفقود
    set /a missing_files+=1
) else (
    echo ✅ pages\home.php موجود
)

if not exist "install\install.php" (
    echo ❌ install\install.php مفقود
    set /a missing_files+=1
) else (
    echo ✅ install\install.php موجود
)

if %missing_files% gtr 0 (
    echo.
    echo ⚠️  تحذير: بعض الملفات مفقودة، لكن سيتم المتابعة...
)

REM التحقق من المنفذ
echo.
echo 🔍 التحقق من المنفذ 8081...
netstat -an | findstr ":8081" >nul
if %errorlevel% equ 0 (
    echo ⚠️  تحذير: المنفذ 8081 قد يكون مستخدماً
    echo    سيتم محاولة التشغيل على أي حال...
) else (
    echo ✅ المنفذ 8081 متاح
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      🌐 الروابط المهمة                      ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║ 🏠 الصفحة الرئيسية:                                        ║
echo ║    http://localhost:8081                                     ║
echo ║                                                              ║
echo ║ 🧪 صفحة الاختبار:                                          ║
echo ║    http://localhost:8081/test.php                            ║
echo ║                                                              ║
echo ║ 🔧 تثبيت الموقع:                                           ║
echo ║    http://localhost:8081/install/install.php                 ║
echo ║                                                              ║
echo ║ 🏠 صفحة الموقع:                                            ║
echo ║    http://localhost:8081/pages/home.php                      ║
echo ║                                                              ║
echo ║ 👨‍💼 لوحة الإدارة:                                           ║
echo ║    http://localhost:8081/admin/index.php                     ║
echo ║                                                              ║
echo ║ 📝 تسجيل الدخول:                                            ║
echo ║    http://localhost:8081/pages/login.php                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔐 بيانات الاختبار:
echo    👤 المدير - اسم المستخدم: admin / كلمة المرور: password
echo.

echo 💡 نصائح:
echo    • استخدم Ctrl+C لإيقاف الخادم
echo    • ابدأ بزيارة صفحة الاختبار للتأكد من عمل PHP
echo    • ثم انتقل لصفحة التثبيت لإعداد قاعدة البيانات
echo.

echo ════════════════════════════════════════════════════════════════
echo 🚀 بدء تشغيل الخادم على localhost:8081
echo ════════════════════════════════════════════════════════════════
echo.

REM فتح المتصفح تلقائياً
echo 🌐 فتح المتصفح...
start http://localhost:8081

REM تشغيل الخادم
echo 🔄 الخادم يعمل الآن... (اضغط Ctrl+C للإيقاف)
echo.
php -S localhost:8081

echo.
echo 🛑 تم إيقاف خادم حراجنا
echo 👋 شكراً لاستخدام موقع حراجنا!
echo.
pause
