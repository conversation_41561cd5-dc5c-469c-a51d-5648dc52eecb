/* تصميم احترافي موحد - حراجنا 2025 */

/* استيراد خطوط Google */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

/* المتغيرات الأساسية */
:root {
    /* الألوان الرئيسية - نظام ألوان احترافي */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* الألوان المحايدة */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* ألوان الحالة */
    --success-500: #10b981;
    --success-600: #059669;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --danger-500: #ef4444;
    --danger-600: #dc2626;
    --info-500: #06b6d4;
    --info-600: #0891b2;

    /* التدرجات الحديثة */
    --gradient-primary: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-500) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--gray-700) 0%, var(--gray-600) 100%);
    --gradient-success: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);
    --gradient-warning: linear-gradient(135deg, var(--warning-600) 0%, var(--warning-500) 100%);
    --gradient-danger: linear-gradient(135deg, var(--danger-600) 0%, var(--danger-500) 100%);
    --gradient-info: linear-gradient(135deg, var(--info-600) 0%, var(--info-500) 100%);

    /* تدرجات خاصة */
    --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-card: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    --gradient-glass: linear-gradient(145deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);

    /* الظلال المتقدمة */
    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

    /* ظلال ملونة */
    --shadow-primary: 0 10px 25px -5px rgba(59, 130, 246, 0.3);
    --shadow-success: 0 10px 25px -5px rgba(16, 185, 129, 0.3);
    --shadow-warning: 0 10px 25px -5px rgba(245, 158, 11, 0.3);
    --shadow-danger: 0 10px 25px -5px rgba(239, 68, 68, 0.3);

    /* الحدود والزوايا */
    --radius-none: 0px;
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-3xl: 2rem;
    --radius-full: 9999px;

    /* المسافات */
    --space-px: 1px;
    --space-0: 0px;
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    --space-32: 8rem;

    /* الانتقالات */
    --transition-none: none;
    --transition-all: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 100ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* أحجام الخطوط */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;

    /* أوزان الخطوط */
    --font-thin: 100;
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
    --font-black: 900;

    /* ارتفاع الأسطر */
    --leading-none: 1;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    /* عرض الحاوي */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;

    /* متغيرات خاصة بالموقع */
    --header-height: 80px;
    --sidebar-width: 280px;
    --content-max-width: 1200px;

    /* الألوان الخاصة بالموقع */
    --brand-primary: var(--primary-600);
    --brand-secondary: var(--gray-700);
    --brand-accent: #f59e0b;

    /* خلفيات متدرجة للصفحات */
    --bg-page: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --bg-section: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    --bg-card: #ffffff;
    --bg-overlay: rgba(15, 23, 42, 0.8);
}

/* إعدادات عامة */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
    line-height: var(--leading-normal);
}

body {
    font-family: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: var(--font-normal);
    color: var(--gray-700);
    background: var(--bg-page);
    direction: rtl;
    text-align: right;
    line-height: var(--leading-relaxed);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* تحسينات الخطوط والنصوص */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-bold);
    color: var(--gray-900);
    line-height: var(--leading-tight);
    margin-bottom: var(--space-4);
    letter-spacing: -0.025em;
}

h1 {
    font-size: var(--text-4xl);
    font-weight: var(--font-extrabold);
}

h2 {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
}

h3 {
    font-size: var(--text-2xl);
    font-weight: var(--font-semibold);
}

h4 {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
}

h5 {
    font-size: var(--text-lg);
    font-weight: var(--font-medium);
}

h6 {
    font-size: var(--text-base);
    font-weight: var(--font-medium);
}

p {
    margin-bottom: var(--space-4);
    color: var(--gray-600);
    line-height: var(--leading-relaxed);
}

a {
    color: var(--brand-primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-700);
    text-decoration: underline;
}

/* الحاويات */
.container {
    width: 100%;
    max-width: var(--content-max-width);
    margin: 0 auto;
    padding-left: var(--space-4);
    padding-right: var(--space-4);
}

.container-fluid {
    width: 100%;
    padding-left: var(--space-4);
    padding-right: var(--space-4);
}

/* الشبكة المرنة */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-left: calc(var(--space-3) * -1);
    margin-right: calc(var(--space-3) * -1);
}

.col,
.col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
.col-sm, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6,
.col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12,
.col-md, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6,
.col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12,
.col-lg, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6,
.col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12,
.col-xl, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6,
.col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12 {
    position: relative;
    width: 100%;
    padding-left: var(--space-3);
    padding-right: var(--space-3);
}

/* شريط التنقل الاحترافي */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
    padding: var(--space-4) 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: var(--transition-normal);
    height: var(--header-height);
    display: flex;
    align-items: center;
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98) !important;
    box-shadow: var(--shadow-lg);
}

.navbar-brand {
    font-family: 'Cairo', sans-serif;
    font-weight: var(--font-black);
    font-size: var(--text-2xl);
    background: var(--gradient-hero);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none !important;
    margin-left: var(--space-6);
    transition: var(--transition-normal);
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.navbar-nav {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-right: auto;
}

.navbar-nav .nav-link {
    font-weight: var(--font-medium);
    color: var(--gray-700) !important;
    padding: var(--space-3) var(--space-4) !important;
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition-fast);
    z-index: -1;
}

.navbar-nav .nav-link:hover::before,
.navbar-nav .nav-link.active::before {
    opacity: 1;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: white !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-primary);
}

.navbar-toggler {
    border: none;
    padding: var(--space-2);
    border-radius: var(--radius-md);
    background: var(--gray-100);
    transition: var(--transition-fast);
}

.navbar-toggler:hover {
    background: var(--gray-200);
    transform: scale(1.05);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    outline: none;
}

/* الأزرار الاحترافية */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    font-family: inherit;
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    line-height: var(--leading-none);
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-all);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    user-select: none;
    -webkit-user-select: none;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn:active {
    transform: translateY(0);
    transition: var(--transition-fast);
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* أنواع الأزرار */
.btn-primary {
    background: var(--gradient-primary);
    color: white;
    border-color: var(--primary-600);
}

.btn-primary:hover {
    background: var(--gradient-primary);
    filter: brightness(1.1);
    color: white;
    box-shadow: var(--shadow-primary);
}

.btn-secondary {
    background: var(--gradient-secondary);
    color: white;
    border-color: var(--gray-600);
}

.btn-secondary:hover {
    background: var(--gradient-secondary);
    filter: brightness(1.1);
    color: white;
}

.btn-success {
    background: var(--gradient-success);
    color: white;
    border-color: var(--success-600);
}

.btn-success:hover {
    background: var(--gradient-success);
    filter: brightness(1.1);
    color: white;
    box-shadow: var(--shadow-success);
}

.btn-warning {
    background: var(--gradient-warning);
    color: white;
    border-color: var(--warning-600);
}

.btn-warning:hover {
    background: var(--gradient-warning);
    filter: brightness(1.1);
    color: white;
    box-shadow: var(--shadow-warning);
}

.btn-danger {
    background: var(--gradient-danger);
    color: white;
    border-color: var(--danger-600);
}

.btn-danger:hover {
    background: var(--gradient-danger);
    filter: brightness(1.1);
    color: white;
    box-shadow: var(--shadow-danger);
}

.btn-info {
    background: var(--gradient-info);
    color: white;
    border-color: var(--info-600);
}

.btn-info:hover {
    background: var(--gradient-info);
    filter: brightness(1.1);
    color: white;
}

/* الأزرار المحددة */
.btn-outline-primary {
    background: transparent;
    color: var(--primary-600);
    border-color: var(--primary-600);
}

.btn-outline-primary:hover {
    background: var(--primary-600);
    color: white;
    box-shadow: var(--shadow-primary);
}

.btn-outline-secondary {
    background: transparent;
    color: var(--gray-600);
    border-color: var(--gray-600);
}

.btn-outline-secondary:hover {
    background: var(--gray-600);
    color: white;
}

/* أحجام الأزرار */
.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-xs);
    border-radius: var(--radius-md);
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-lg);
    border-radius: var(--radius-xl);
}

.btn-xl {
    padding: var(--space-5) var(--space-10);
    font-size: var(--text-xl);
    border-radius: var(--radius-2xl);
}

/* البطاقات الاحترافية */
.card {
    background: var(--bg-card);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    overflow: hidden;
    position: relative;
    margin-bottom: var(--space-6);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-glass);
    opacity: 0;
    transition: var(--transition-normal);
    pointer-events: none;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    background: var(--gradient-section);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--space-6);
    font-weight: var(--font-semibold);
    color: var(--gray-800);
    position: relative;
    z-index: 1;
}

.card-body {
    padding: var(--space-6);
    position: relative;
    z-index: 1;
}

.card-footer {
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    padding: var(--space-6);
    position: relative;
    z-index: 1;
}

/* بطاقات الإعلانات المتقدمة */
.ad-card {
    background: var(--bg-card);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    position: relative;
    margin-bottom: var(--space-6);
}

.ad-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition-normal);
    pointer-events: none;
}

.ad-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-2xl);
}

.ad-card:hover::after {
    opacity: 0.05;
}

.ad-card .card-img-top {
    height: 220px;
    object-fit: cover;
    transition: var(--transition-slow);
    position: relative;
    z-index: 1;
}

.ad-card:hover .card-img-top {
    transform: scale(1.1);
}

.ad-card .card-body {
    position: relative;
    z-index: 2;
    background: var(--bg-card);
}

.ad-card .card-title {
    font-weight: var(--font-bold);
    color: var(--gray-900);
    margin-bottom: var(--space-3);
    font-size: var(--text-lg);
}

.ad-card .card-text {
    color: var(--gray-600);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--space-4);
}

.ad-card .price {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: var(--primary-600);
    margin-bottom: var(--space-4);
}

.ad-card .meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--text-sm);
    color: var(--gray-500);
    margin-bottom: var(--space-4);
}

.ad-card .actions {
    display: flex;
    gap: var(--space-2);
    flex-wrap: wrap;
}

.ad-card .btn {
    flex: 1;
    min-width: auto;
}

/* النماذج الاحترافية */
.form-control,
.form-select {
    display: block;
    width: 100%;
    padding: var(--space-4);
    font-family: inherit;
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    line-height: var(--leading-normal);
    color: var(--gray-700);
    background: var(--bg-card);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.form-control:focus,
.form-select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: white;
}

.form-control::placeholder {
    color: var(--gray-400);
    opacity: 1;
}

.form-control:disabled,
.form-select:disabled {
    background: var(--gray-100);
    color: var(--gray-500);
    cursor: not-allowed;
}

/* النماذج العائمة */
.form-floating {
    position: relative;
}

.form-floating > .form-control,
.form-floating > .form-select {
    height: calc(3.5rem + 2px);
    line-height: 1.25;
    padding: 1rem 0.75rem;
}

.form-floating > label {
    position: absolute;
    top: 0;
    right: 0.75rem;
    left: auto;
    height: 100%;
    padding: 1rem 0;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 100% 0;
    transition: var(--transition-fast);
    color: var(--gray-500);
    font-weight: var(--font-medium);
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    color: var(--primary-600);
}

/* مجموعات النماذج */
.form-group {
    margin-bottom: var(--space-6);
}

.form-label {
    display: inline-block;
    margin-bottom: var(--space-2);
    font-weight: var(--font-medium);
    color: var(--gray-700);
}

.form-text {
    margin-top: var(--space-1);
    font-size: var(--text-sm);
    color: var(--gray-500);
}

/* التنبيهات الاحترافية */
.alert {
    position: relative;
    padding: var(--space-4) var(--space-6);
    margin-bottom: var(--space-6);
    border: 1px solid transparent;
    border-radius: var(--radius-xl);
    font-weight: var(--font-medium);
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
}

.alert-dismissible {
    padding-left: var(--space-12);
}

.alert .btn-close {
    position: absolute;
    top: var(--space-4);
    left: var(--space-4);
    padding: var(--space-2);
    background: transparent;
    border: none;
    border-radius: var(--radius-md);
    opacity: 0.7;
    transition: var(--transition-fast);
}

.alert .btn-close:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.1);
}

.alert-primary {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
    border-color: var(--primary-200);
    color: var(--primary-800);
    border-right: 4px solid var(--primary-500);
}

.alert-success {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    border-color: #a7f3d0;
    color: var(--success-800);
    border-right: 4px solid var(--success-500);
}

.alert-warning {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    border-color: #fde68a;
    color: var(--warning-800);
    border-right: 4px solid var(--warning-500);
}

.alert-danger {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    border-color: #fca5a5;
    color: var(--danger-800);
    border-right: 4px solid var(--danger-500);
}

.alert-info {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-color: #7dd3fc;
    color: var(--info-800);
    border-right: 4px solid var(--info-500);
}

/* الشارات الاحترافية */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    line-height: var(--leading-none);
    color: white;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--radius-full);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge.bg-primary {
    background: var(--gradient-primary);
}

.badge.bg-secondary {
    background: var(--gradient-secondary);
}

.badge.bg-success {
    background: var(--gradient-success);
}

.badge.bg-warning {
    background: var(--gradient-warning);
}

.badge.bg-danger {
    background: var(--gradient-danger);
}

.badge.bg-info {
    background: var(--gradient-info);
}

/* الجداول الاحترافية */
.table {
    width: 100%;
    margin-bottom: var(--space-6);
    color: var(--gray-700);
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.table thead th {
    background: var(--gradient-section);
    color: var(--gray-800);
    font-weight: var(--font-semibold);
    padding: var(--space-4) var(--space-6);
    border-bottom: 2px solid var(--gray-200);
    text-align: right;
    vertical-align: bottom;
}

.table tbody td {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--gray-100);
    vertical-align: middle;
    text-align: right;
}

.table tbody tr {
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background: var(--gray-50);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* القوائم الاحترافية */
.list-group {
    display: flex;
    flex-direction: column;
    padding-right: 0;
    margin-bottom: 0;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    background: var(--bg-card);
}

.list-group-item {
    position: relative;
    display: block;
    padding: var(--space-4) var(--space-6);
    color: var(--gray-700);
    text-decoration: none;
    background: var(--bg-card);
    border: none;
    border-bottom: 1px solid var(--gray-100);
    transition: var(--transition-fast);
}

.list-group-item:hover {
    background: var(--gray-50);
    color: var(--gray-900);
}

.list-group-item.active {
    background: var(--gradient-primary);
    color: white;
    border-color: var(--primary-600);
}

.list-group-item:last-child {
    border-bottom: none;
}

/* النوافذ المنبثقة (Modals) */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1055;
    display: none;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    outline: 0;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: var(--space-4);
    pointer-events: none;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background: var(--bg-card);
    background-clip: padding-box;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    outline: 0;
    overflow: hidden;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gradient-section);
}

.modal-title {
    margin-bottom: 0;
    line-height: var(--leading-normal);
    font-weight: var(--font-semibold);
    color: var(--gray-900);
}

.modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: var(--space-6);
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--space-3);
    padding: var(--space-6);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    width: 100vw;
    height: 100vh;
    background: var(--bg-overlay);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

/* الفوتر الاحترافي */
.footer {
    background: var(--gray-900);
    color: var(--gray-300);
    margin-top: auto;
    padding: var(--space-16) 0 var(--space-8);
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
    z-index: -1;
}

.footer h5 {
    color: white;
    font-weight: var(--font-bold);
    margin-bottom: var(--space-6);
    font-size: var(--text-lg);
}

.footer p {
    color: var(--gray-400);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--space-4);
}

.footer a {
    color: var(--gray-400);
    text-decoration: none;
    transition: var(--transition-fast);
    display: inline-block;
    padding: var(--space-1) 0;
}

.footer a:hover {
    color: var(--primary-400);
    transform: translateX(-4px);
}

.footer .social-links {
    display: flex;
    gap: var(--space-4);
    margin-top: var(--space-6);
}

.footer .social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: var(--gray-800);
    border-radius: var(--radius-full);
    transition: var(--transition-normal);
}

.footer .social-links a:hover {
    background: var(--primary-600);
    transform: translateY(-4px);
    box-shadow: var(--shadow-primary);
}

.footer .copyright {
    border-top: 1px solid var(--gray-700);
    margin-top: var(--space-12);
    padding-top: var(--space-8);
    text-align: center;
    color: var(--gray-500);
}

/* تحسينات للصور */
.img-fluid {
    max-width: 100%;
    height: auto;
}

.img-thumbnail {
    padding: var(--space-1);
    background: var(--bg-card);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    max-width: 100%;
    height: auto;
    transition: var(--transition-normal);
}

.img-thumbnail:hover {
    border-color: var(--primary-300);
    box-shadow: var(--shadow-lg);
}

/* تحسينات للأيقونات */
.icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.icon-sm {
    width: 1rem;
    height: 1rem;
    font-size: 0.875rem;
}

.icon-md {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 1.25rem;
}

.icon-lg {
    width: 2rem;
    height: 2rem;
    font-size: 1.5rem;
}

.icon-xl {
    width: 3rem;
    height: 3rem;
    font-size: 2rem;
}

.icon-2xl {
    width: 4rem;
    height: 4rem;
    font-size: 2.5rem;
}

/* فئات مساعدة */
.text-gradient {
    background: var(--gradient-hero);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient {
    background: var(--gradient-primary);
}

.shadow-custom {
    box-shadow: var(--shadow-2xl) !important;
}

.border-custom {
    border: 2px solid var(--primary-500) !important;
}

.rounded-custom {
    border-radius: var(--radius-3xl) !important;
}

.hover-scale {
    transition: var(--transition-normal);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-shadow {
    transition: var(--transition-normal);
}

.hover-shadow:hover {
    box-shadow: var(--shadow-xl);
}

/* تحسينات للتحميل */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* تحسينات للتفاعل */
.interactive {
    cursor: pointer;
    transition: var(--transition-normal);
}

.interactive:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* تحسينات للجوال */
@media (max-width: 576px) {
    :root {
        --space-4: 0.75rem;
        --space-6: 1rem;
        --space-8: 1.5rem;
        --space-12: 2rem;
        --space-16: 2.5rem;
        --text-4xl: 2rem;
        --text-3xl: 1.75rem;
        --text-2xl: 1.5rem;
    }

    .container {
        padding-left: var(--space-4);
        padding-right: var(--space-4);
    }

    .navbar-brand {
        font-size: var(--text-xl);
    }

    .btn {
        width: 100%;
        margin-bottom: var(--space-2);
    }

    .btn-group .btn {
        width: auto;
        margin-bottom: 0;
    }

    .card-body {
        padding: var(--space-4);
    }

    .modal-dialog {
        margin: var(--space-2);
    }

    .ad-card:hover {
        transform: none;
    }

    .table {
        font-size: var(--text-sm);
    }

    .table thead th,
    .table tbody td {
        padding: var(--space-3);
    }

    h1 { font-size: var(--text-3xl); }
    h2 { font-size: var(--text-2xl); }
    h3 { font-size: var(--text-xl); }
}

@media (max-width: 768px) {
    .navbar-nav {
        flex-direction: column;
        gap: var(--space-1);
        margin-top: var(--space-4);
    }

    .navbar-nav .nav-link {
        text-align: center;
        padding: var(--space-3) var(--space-4);
    }

    .footer {
        padding: var(--space-12) 0 var(--space-6);
    }

    .footer .social-links {
        justify-content: center;
    }
}

/* تحسينات للطباعة */
@media print {
    .navbar,
    .footer,
    .btn,
    .alert,
    .modal {
        display: none !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }

    body {
        background: white;
        color: black;
    }

    .text-gradient {
        -webkit-text-fill-color: initial;
        color: black;
    }
}

/* تحسينات للإمكانية */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* تحسينات للتركيز */
*:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* تحسينات للحركة */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-page: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
        --bg-card: var(--gray-800);
        --bg-section: linear-gradient(145deg, var(--gray-800) 0%, var(--gray-700) 100%);
    }

    body {
        color: var(--gray-200);
    }

    .card {
        background: var(--gray-800);
        border-color: var(--gray-700);
    }

    .navbar {
        background: rgba(31, 41, 55, 0.95) !important;
        border-bottom-color: var(--gray-700);
    }

    .form-control,
    .form-select {
        background: var(--gray-700);
        border-color: var(--gray-600);
        color: var(--gray-200);
    }

    .table {
        background: var(--gray-800);
        color: var(--gray-200);
    }

    .table thead th {
        background: var(--gray-700);
        color: var(--gray-200);
    }
}

/* تحسينات إضافية للتصميم */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.neon-glow {
    box-shadow: 0 0 20px var(--primary-500);
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(100px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}