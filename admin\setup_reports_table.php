<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدمن
requireAdmin();

try {
    // حذف الجدول إذا كان موجوداً لإعادة إنشائه بالشكل الصحيح
    $db->query("DROP TABLE IF EXISTS reports");

    // إنشاء جدول التقارير
    $db->query("
        CREATE TABLE reports (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            reporter_id INT NOT NULL,
            reported_ad_id INT NULL,
            reported_user_id INT NULL,
            report_type ENUM('spam', 'inappropriate', 'fake', 'fraud', 'other') NOT NULL,
            reason TEXT NOT NULL,
            status ENUM('pending', 'reviewed', 'resolved', 'rejected') DEFAULT 'pending',
            reviewed_by INT NULL,
            reviewed_at TIMESTAMP NULL,
            admin_notes TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user (user_id),
            INDEX idx_reporter (reporter_id),
            INDEX idx_reported_ad (reported_ad_id),
            INDEX idx_reported_user (reported_user_id),
            INDEX idx_status (status),
            INDEX idx_created (created_at)
        )
    ");
    
    // التحقق من وجود تقارير
    $existing_reports = $db->fetch("SELECT COUNT(*) as count FROM reports")['count'];
    
    if ($existing_reports == 0) {
        // إدراج بعض التقارير التجريبية
        $sample_reports = [
            [
                'user_id' => 1,
                'reporter_id' => 1,
                'reported_ad_id' => 1,
                'report_type' => 'spam',
                'reason' => 'هذا الإعلان يحتوي على محتوى مكرر ومزعج',
                'status' => 'pending'
            ],
            [
                'user_id' => 1,
                'reporter_id' => 1,
                'reported_ad_id' => 2,
                'report_type' => 'inappropriate',
                'reason' => 'محتوى غير مناسب',
                'status' => 'reviewed'
            ],
            [
                'user_id' => 1,
                'reporter_id' => 1,
                'reported_user_id' => 2,
                'report_type' => 'fraud',
                'reason' => 'مستخدم مشبوه يحاول الاحتيال',
                'status' => 'resolved'
            ]
        ];

        foreach ($sample_reports as $report) {
            $db->query(
                "INSERT INTO reports (user_id, reporter_id, reported_ad_id, reported_user_id, report_type, reason, status) VALUES (?, ?, ?, ?, ?, ?, ?)",
                [
                    $report['user_id'],
                    $report['reporter_id'],
                    $report['reported_ad_id'] ?? null,
                    $report['reported_user_id'] ?? null,
                    $report['report_type'],
                    $report['reason'],
                    $report['status']
                ]
            );
        }
        
        echo "تم إنشاء جدول التقارير وإدراج البيانات التجريبية بنجاح!";
    } else {
        echo "جدول التقارير موجود مسبقاً ويحتوي على {$existing_reports} تقرير.";
    }
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
