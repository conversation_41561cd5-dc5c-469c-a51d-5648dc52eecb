<?php
// إنشاء جدول التقارير
try {
    $pdo = new PDO('mysql:host=localhost;dbname=harajuna;charset=utf8mb4', 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // إنشاء جدول التقارير
    $sql = "
    CREATE TABLE IF NOT EXISTS reports (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        ad_id INT NOT NULL,
        reason VARCHAR(100) NOT NULL,
        description TEXT,
        status ENUM('pending', 'reviewed', 'resolved', 'rejected') DEFAULT 'pending',
        reviewed_by INT,
        reviewed_at TIMESTAMP NULL,
        admin_notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_ad_id (ad_id),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE,
        FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL
    )";
    
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول التقارير بنجاح<br>";
    
    // إضافة عمود image_path للفئات إذا لم يكن موجوداً
    try {
        $pdo->exec("ALTER TABLE categories ADD COLUMN image_path VARCHAR(255) AFTER icon");
        echo "✅ تم إضافة عمود image_path لجدول الفئات<br>";
    } catch (Exception $e) {
        echo "ℹ️ عمود image_path موجود بالفعل في جدول الفئات<br>";
    }
    
    echo "<hr>";
    echo "<h3>🎉 تم إعداد الجداول بنجاح!</h3>";
    echo "<p>يمكنك الآن استخدام جميع ميزات الموقع:</p>";
    echo "<ul>";
    echo "<li><a href='pages/home.php'>الصفحة الرئيسية</a></li>";
    echo "<li><a href='pages/ad-details.php?id=1'>اختبار الإبلاغ</a></li>";
    echo "<li><a href='admin/categories.php'>إدارة الفئات</a></li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
}
?>
