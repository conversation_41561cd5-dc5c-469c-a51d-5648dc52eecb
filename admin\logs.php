<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدمن
requireAdmin();

$error = '';
$success = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $action = $_POST['action'];
        
        switch ($action) {
            case 'clear_logs':
                $log_type = $_POST['log_type'] ?? 'all';
                if ($log_type === 'all') {
                    $db->query("DELETE FROM activity_logs");
                } else {
                    $db->query("DELETE FROM activity_logs WHERE action_type = ?", [$log_type]);
                }
                $success = 'تم مسح السجلات بنجاح';
                break;
                
            case 'delete_old_logs':
                $days = (int)($_POST['days'] ?? 30);
                $db->query("DELETE FROM activity_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)", [$days]);
                $success = "تم حذف السجلات الأقدم من {$days} يوم";
                break;
        }
        
        // إعادة توجيه لتجنب إعادة الإرسال
        header('Location: logs.php');
        exit();
        
    } catch (Exception $e) {
        $error = 'حدث خطأ: ' . $e->getMessage();
    }
}

// جلب السجلات
try {
    $search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
    $action_filter = isset($_GET['action']) ? sanitize($_GET['action']) : 'all';
    $user_filter = isset($_GET['user']) ? sanitize($_GET['user']) : 'all';
    $date_from = isset($_GET['date_from']) ? sanitize($_GET['date_from']) : '';
    $date_to = isset($_GET['date_to']) ? sanitize($_GET['date_to']) : '';
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $items_per_page = 50;
    $offset = ($page - 1) * $items_per_page;

    $where_conditions = [];
    $params = [];

    if (!empty($search)) {
        $where_conditions[] = "(l.description LIKE ? OR l.ip_address LIKE ? OR u.username LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    if ($action_filter !== 'all') {
        $where_conditions[] = "l.action_type = ?";
        $params[] = $action_filter;
    }

    if ($user_filter !== 'all') {
        $where_conditions[] = "l.user_id = ?";
        $params[] = $user_filter;
    }

    if (!empty($date_from)) {
        $where_conditions[] = "DATE(l.created_at) >= ?";
        $params[] = $date_from;
    }

    if (!empty($date_to)) {
        $where_conditions[] = "DATE(l.created_at) <= ?";
        $params[] = $date_to;
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    $sql = "SELECT l.*, u.username, u.full_name
            FROM activity_logs l 
            LEFT JOIN users u ON l.user_id = u.id 
            $where_clause 
            ORDER BY l.created_at DESC 
            LIMIT $items_per_page OFFSET $offset";

    $logs = $db->fetchAll($sql, $params);

    // عدد النتائج الإجمالي
    $count_sql = "SELECT COUNT(*) as total FROM activity_logs l LEFT JOIN users u ON l.user_id = u.id $where_clause";
    $total_logs_count = $db->fetch($count_sql, $params)['total'];
    $total_pages = ceil($total_logs_count / $items_per_page);

    // إحصائيات
    $stats = [
        'total' => $db->fetch("SELECT COUNT(*) as count FROM activity_logs")['count'],
        'today' => $db->fetch("SELECT COUNT(*) as count FROM activity_logs WHERE DATE(created_at) = CURDATE()")['count'],
        'week' => $db->fetch("SELECT COUNT(*) as count FROM activity_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")['count'],
        'month' => $db->fetch("SELECT COUNT(*) as count FROM activity_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)")['count'],
    ];

    // أنواع الأنشطة
    $action_types = $db->fetchAll("SELECT action_type, COUNT(*) as count FROM activity_logs GROUP BY action_type ORDER BY count DESC");

    // المستخدمون النشطون
    $active_users = $db->fetchAll("
        SELECT u.username, u.full_name, COUNT(l.id) as activity_count 
        FROM activity_logs l 
        JOIN users u ON l.user_id = u.id 
        WHERE l.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY l.user_id 
        ORDER BY activity_count DESC 
        LIMIT 10
    ");

} catch (Exception $e) {
    $error = 'خطأ في جلب البيانات: ' . $e->getMessage();
    $logs = [];
    $stats = ['total' => 0, 'today' => 0, 'week' => 0, 'month' => 0];
    $action_types = [];
    $active_users = [];
    $total_pages = 0;
}

$page_title = 'سجلات النشاط';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">سجلات النشاط</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#clearLogsModal">
                            <i class="fas fa-trash"></i> مسح السجلات
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-warning" data-bs-toggle="modal" data-bs-target="#deleteOldLogsModal">
                            <i class="fas fa-calendar-times"></i> حذف القديمة
                        </button>
                    </div>
                </div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?= $error ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?= $success ?>
                </div>
            <?php endif; ?>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-list fa-2x mb-2"></i>
                            <h4><?= number_format($stats['total']) ?></h4>
                            <p class="mb-0">إجمالي السجلات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-calendar-day fa-2x mb-2"></i>
                            <h4><?= number_format($stats['today']) ?></h4>
                            <p class="mb-0">اليوم</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-calendar-week fa-2x mb-2"></i>
                            <h4><?= number_format($stats['week']) ?></h4>
                            <p class="mb-0">هذا الأسبوع</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                            <h4><?= number_format($stats['month']) ?></h4>
                            <p class="mb-0">هذا الشهر</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- فلاتر البحث -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-6">
                                    <label for="search" class="form-label">البحث</label>
                                    <input type="text" class="form-control" id="search" name="search" 
                                           value="<?= htmlspecialchars($search) ?>" placeholder="ابحث في الوصف أو IP أو المستخدم">
                                </div>
                                <div class="col-md-3">
                                    <label for="action" class="form-label">نوع النشاط</label>
                                    <select class="form-select" id="action" name="action">
                                        <option value="all" <?= $action_filter === 'all' ? 'selected' : '' ?>>جميع الأنشطة</option>
                                        <option value="login" <?= $action_filter === 'login' ? 'selected' : '' ?>>تسجيل دخول</option>
                                        <option value="logout" <?= $action_filter === 'logout' ? 'selected' : '' ?>>تسجيل خروج</option>
                                        <option value="create" <?= $action_filter === 'create' ? 'selected' : '' ?>>إنشاء</option>
                                        <option value="update" <?= $action_filter === 'update' ? 'selected' : '' ?>>تحديث</option>
                                        <option value="delete" <?= $action_filter === 'delete' ? 'selected' : '' ?>>حذف</option>
                                        <option value="admin" <?= $action_filter === 'admin' ? 'selected' : '' ?>>إدارية</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="date_from" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?= htmlspecialchars($date_from) ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="date_to" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?= htmlspecialchars($date_to) ?>">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="submit" class="btn btn-primary d-block w-100">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <a href="logs.php" class="btn btn-secondary d-block w-100">
                                        <i class="fas fa-undo"></i> إعادة تعيين
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- جدول السجلات -->
                    <div class="card">
                        <div class="card-body">
                            <?php if (empty($logs)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-list fa-3x text-muted mb-3"></i>
                                    <h5>لا توجد سجلات</h5>
                                    <p class="text-muted">لم يتم العثور على أي سجلات</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-striped table-sm">
                                        <thead>
                                            <tr>
                                                <th>المستخدم</th>
                                                <th>النشاط</th>
                                                <th>الوصف</th>
                                                <th>IP</th>
                                                <th>التاريخ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($logs as $log): ?>
                                                <tr>
                                                    <td>
                                                        <?php if ($log['username']): ?>
                                                            <strong><?= htmlspecialchars($log['username']) ?></strong>
                                                            <?php if ($log['full_name']): ?>
                                                                <br><small class="text-muted"><?= htmlspecialchars($log['full_name']) ?></small>
                                                            <?php endif; ?>
                                                        <?php else: ?>
                                                            <span class="text-muted">مستخدم محذوف</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $action_classes = [
                                                            'login' => 'success',
                                                            'logout' => 'secondary',
                                                            'create' => 'primary',
                                                            'update' => 'warning',
                                                            'delete' => 'danger',
                                                            'admin' => 'info'
                                                        ];
                                                        $action_labels = [
                                                            'login' => 'دخول',
                                                            'logout' => 'خروج',
                                                            'create' => 'إنشاء',
                                                            'update' => 'تحديث',
                                                            'delete' => 'حذف',
                                                            'admin' => 'إدارية'
                                                        ];
                                                        $class = $action_classes[$log['action_type']] ?? 'secondary';
                                                        $label = $action_labels[$log['action_type']] ?? $log['action_type'];
                                                        ?>
                                                        <span class="badge bg-<?= $class ?>"><?= $label ?></span>
                                                    </td>
                                                    <td><?= htmlspecialchars($log['description']) ?></td>
                                                    <td>
                                                        <code><?= htmlspecialchars($log['ip_address']) ?></code>
                                                    </td>
                                                    <td>
                                                        <small><?= date('Y-m-d H:i:s', strtotime($log['created_at'])) ?></small>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- ترقيم الصفحات -->
                                <?php if ($total_pages > 1): ?>
                                    <nav aria-label="ترقيم الصفحات">
                                        <ul class="pagination justify-content-center">
                                            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                                <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                                    <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&action=<?= $action_filter ?>&date_from=<?= $date_from ?>&date_to=<?= $date_to ?>">
                                                        <?= $i ?>
                                                    </a>
                                                </li>
                                            <?php endfor; ?>
                                        </ul>
                                    </nav>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- الشريط الجانبي للإحصائيات -->
                <div class="col-lg-4">
                    <!-- أنواع الأنشطة -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">أنواع الأنشطة</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($action_types)): ?>
                                <p class="text-muted text-center">لا توجد بيانات</p>
                            <?php else: ?>
                                <?php foreach ($action_types as $type): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span><?= $action_labels[$type['action_type']] ?? $type['action_type'] ?></span>
                                        <span class="badge bg-primary"><?= number_format($type['count']) ?></span>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- المستخدمون النشطون -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">المستخدمون النشطون (7 أيام)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($active_users)): ?>
                                <p class="text-muted text-center">لا توجد بيانات</p>
                            <?php else: ?>
                                <?php foreach ($active_users as $user): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <strong><?= htmlspecialchars($user['username']) ?></strong>
                                            <?php if ($user['full_name']): ?>
                                                <br><small class="text-muted"><?= htmlspecialchars($user['full_name']) ?></small>
                                            <?php endif; ?>
                                        </div>
                                        <span class="badge bg-success"><?= number_format($user['activity_count']) ?></span>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- نموذج مسح السجلات -->
<div class="modal fade" id="clearLogsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">مسح السجلات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="clear_logs">

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        تحذير: هذا الإجراء لا يمكن التراجع عنه!
                    </div>

                    <div class="mb-3">
                        <label for="log_type" class="form-label">نوع السجلات المراد مسحها</label>
                        <select class="form-select" id="log_type" name="log_type">
                            <option value="all">جميع السجلات</option>
                            <option value="login">سجلات تسجيل الدخول</option>
                            <option value="logout">سجلات تسجيل الخروج</option>
                            <option value="create">سجلات الإنشاء</option>
                            <option value="update">سجلات التحديث</option>
                            <option value="delete">سجلات الحذف</option>
                            <option value="admin">السجلات الإدارية</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">مسح السجلات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج حذف السجلات القديمة -->
<div class="modal fade" id="deleteOldLogsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">حذف السجلات القديمة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="delete_old_logs">

                    <div class="mb-3">
                        <label for="days" class="form-label">حذف السجلات الأقدم من (بالأيام)</label>
                        <select class="form-select" id="days" name="days">
                            <option value="7">7 أيام</option>
                            <option value="30" selected>30 يوم</option>
                            <option value="90">90 يوم</option>
                            <option value="180">180 يوم</option>
                            <option value="365">سنة واحدة</option>
                        </select>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        سيتم حذف جميع السجلات الأقدم من الفترة المحددة.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">حذف السجلات القديمة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
