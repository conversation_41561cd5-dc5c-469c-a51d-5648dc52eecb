<?php
// حل مشاكل قاعدة البيانات
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔧 حل مشاكل قاعدة البيانات</h2>";

try {
    require_once 'config/database.php';
    
    if (!isset($db)) {
        throw new Exception("لم يتم إنشاء كائن قاعدة البيانات");
    }
    
    echo "<h3>1. إصلاح جدول reports</h3>";
    
    // التحقق من وجود جدول reports
    $reports_exists = $db->fetch("SELECT name FROM sqlite_master WHERE type='table' AND name='reports'");
    
    if (!$reports_exists) {
        echo "📝 إنشاء جدول reports...<br>";
        $db->query("
            CREATE TABLE reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                reporter_id INTEGER NOT NULL,
                reported_ad_id INTEGER NULL,
                reported_user_id INTEGER NULL,
                report_type TEXT NOT NULL CHECK(report_type IN ('spam', 'inappropriate', 'fake', 'fraud', 'other')),
                reason TEXT NOT NULL,
                status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'reviewed', 'resolved', 'rejected')),
                reviewed_by INTEGER NULL,
                reviewed_at DATETIME NULL,
                admin_notes TEXT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (reporter_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (reported_ad_id) REFERENCES ads(id) ON DELETE CASCADE,
                FOREIGN KEY (reported_user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL
            )
        ");
        echo "✅ تم إنشاء جدول reports<br>";
    } else {
        echo "✅ جدول reports موجود مسبقاً<br>";
        
        // التحقق من هيكل الجدول
        $columns = $db->fetchAll("PRAGMA table_info(reports)");
        $column_names = array_column($columns, 'name');
        
        // التحقق من وجود العمود ad_id
        if (in_array('ad_id', $column_names) && !in_array('reported_ad_id', $column_names)) {
            echo "🔄 تحديث هيكل جدول reports...<br>";
            
            // إنشاء جدول جديد بالهيكل الصحيح
            $db->query("
                CREATE TABLE reports_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    reporter_id INTEGER NOT NULL,
                    reported_ad_id INTEGER NULL,
                    reported_user_id INTEGER NULL,
                    report_type TEXT NOT NULL CHECK(report_type IN ('spam', 'inappropriate', 'fake', 'fraud', 'other')),
                    reason TEXT NOT NULL,
                    status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'reviewed', 'resolved', 'rejected')),
                    reviewed_by INTEGER NULL,
                    reviewed_at DATETIME NULL,
                    admin_notes TEXT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (reporter_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (reported_ad_id) REFERENCES ads(id) ON DELETE CASCADE,
                    FOREIGN KEY (reported_user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL
                )
            ");
            
            // نسخ البيانات الموجودة
            $existing_reports = $db->fetchAll("SELECT * FROM reports");
            foreach ($existing_reports as $report) {
                $db->query("
                    INSERT INTO reports_new (
                        id, user_id, reporter_id, reported_ad_id, reported_user_id, 
                        report_type, reason, status, reviewed_by, reviewed_at, 
                        admin_notes, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ", [
                    $report['id'],
                    $report['user_id'] ?? $report['reporter_id'] ?? 1,
                    $report['reporter_id'] ?? $report['user_id'] ?? 1,
                    $report['ad_id'] ?? $report['reported_ad_id'] ?? null,
                    $report['reported_user_id'] ?? null,
                    $report['report_type'] ?? $report['reason'] ?? 'other',
                    $report['reason'] ?? $report['description'] ?? 'لا يوجد وصف',
                    $report['status'] ?? 'pending',
                    $report['reviewed_by'] ?? null,
                    $report['reviewed_at'] ?? null,
                    $report['admin_notes'] ?? null,
                    $report['created_at'] ?? date('Y-m-d H:i:s')
                ]);
            }
            
            // حذف الجدول القديم وإعادة تسمية الجديد
            $db->query("DROP TABLE reports");
            $db->query("ALTER TABLE reports_new RENAME TO reports");
            
            echo "✅ تم تحديث هيكل جدول reports<br>";
        }
    }
    
    echo "<h3>2. إصلاح جدول favorites</h3>";
    
    // التحقق من وجود جدول favorites
    $favorites_exists = $db->fetch("SELECT name FROM sqlite_master WHERE type='table' AND name='favorites'");
    
    if (!$favorites_exists) {
        echo "📝 إنشاء جدول favorites...<br>";
        $db->query("
            CREATE TABLE favorites (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                ad_id INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(user_id, ad_id),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE
            )
        ");
        echo "✅ تم إنشاء جدول favorites<br>";
    } else {
        echo "✅ جدول favorites موجود مسبقاً<br>";
    }
    
    echo "<h3>3. اختبار العمليات</h3>";
    
    // اختبار إدراج بلاغ تجريبي
    try {
        // التحقق من وجود مستخدم وإعلان للاختبار
        $test_user = $db->fetch("SELECT id FROM users LIMIT 1");
        $test_ad = $db->fetch("SELECT id FROM ads LIMIT 1");
        
        if ($test_user && $test_ad) {
            // محاولة إدراج بلاغ تجريبي
            $db->query("
                INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason) 
                VALUES (?, ?, ?, ?, ?)
            ", [
                $test_user['id'],
                $test_user['id'],
                $test_ad['id'],
                'spam',
                'اختبار البلاغ'
            ]);
            
            // حذف البلاغ التجريبي
            $db->query("DELETE FROM reports WHERE reason = 'اختبار البلاغ'");
            
            echo "✅ اختبار إدراج البلاغات يعمل بشكل صحيح<br>";
        } else {
            echo "⚠️ لا توجد بيانات مستخدمين أو إعلانات للاختبار<br>";
        }
        
        // اختبار إدراج مفضلة تجريبية
        if ($test_user && $test_ad) {
            $db->query("
                INSERT OR IGNORE INTO favorites (user_id, ad_id) 
                VALUES (?, ?)
            ", [$test_user['id'], $test_ad['id']]);
            
            // حذف المفضلة التجريبية
            $db->query("DELETE FROM favorites WHERE user_id = ? AND ad_id = ?", [$test_user['id'], $test_ad['id']]);
            
            echo "✅ اختبار إدراج المفضلة يعمل بشكل صحيح<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ خطأ في اختبار العمليات: " . $e->getMessage() . "<br>";
    }
    
    echo "<h3>4. ملخص الإصلاحات</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ تم إصلاح المشاكل التالية:</h4>";
    echo "<ul>";
    echo "<li>إصلاح هيكل جدول reports ليدعم العمود reported_ad_id بدلاً من ad_id</li>";
    echo "<li>التأكد من وجود جدول favorites</li>";
    echo "<li>إضافة القيود المناسبة للجداول</li>";
    echo "<li>اختبار العمليات للتأكد من عملها</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ خطأ:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>🔗 اختبار الصفحات:</h3>";
echo "<ul>";
echo "<li><a href='pages/ad-details.php?id=1' target='_blank'>اختبار صفحة تفاصيل الإعلان</a></li>";
echo "<li><a href='pages/login.php' target='_blank'>تسجيل الدخول</a></li>";
echo "<li><a href='debug.php' target='_blank'>صفحة التشخيص</a></li>";
echo "</ul>";
?>
