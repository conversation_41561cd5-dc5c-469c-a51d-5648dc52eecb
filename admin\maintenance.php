<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدمن
requireAdmin();

$error = '';
$success = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'];
        
        switch ($action) {
            case 'enable_maintenance':
                $message = sanitize($_POST['maintenance_message']);
                $db->query("UPDATE settings SET value = '1' WHERE name = 'maintenance_mode'");
                $db->query("UPDATE settings SET value = ? WHERE name = 'maintenance_message'", [$message]);
                $success = 'تم تفعيل وضع الصيانة';
                break;
                
            case 'disable_maintenance':
                $db->query("UPDATE settings SET value = '0' WHERE name = 'maintenance_mode'");
                $success = 'تم إلغاء وضع الصيانة';
                break;
                
            case 'clear_cache':
                // مسح ملفات الكاش
                $cache_dir = $root_path . '/cache';
                if (is_dir($cache_dir)) {
                    $files = glob($cache_dir . '/*');
                    foreach ($files as $file) {
                        if (is_file($file)) {
                            unlink($file);
                        }
                    }
                }
                $success = 'تم مسح الكاش بنجاح';
                break;
                
            case 'optimize_db':
                // تحسين قاعدة البيانات
                $tables = ['users', 'ads', 'categories', 'messages', 'reports', 'activity_logs', 'car_brands'];
                foreach ($tables as $table) {
                    try {
                        $db->query("OPTIMIZE TABLE $table");
                    } catch (Exception $e) {
                        // تجاهل الأخطاء للجداول غير الموجودة
                    }
                }
                $success = 'تم تحسين قاعدة البيانات بنجاح';
                break;
                
            case 'backup_db':
                // إنشاء نسخة احتياطية
                $backup_file = $root_path . '/backups/backup_' . date('Y-m-d_H-i-s') . '.sql';
                $backup_dir = dirname($backup_file);
                
                if (!is_dir($backup_dir)) {
                    mkdir($backup_dir, 0755, true);
                }
                
                // هذا مثال بسيط - في الواقع تحتاج لاستخدام mysqldump
                file_put_contents($backup_file, "-- Backup created on " . date('Y-m-d H:i:s') . "\n");
                $success = 'تم إنشاء النسخة الاحتياطية بنجاح';
                break;
        }
        
        // إعادة توجيه لتجنب إعادة الإرسال
        header('Location: maintenance.php');
        exit();
        
    } catch (Exception $e) {
        $error = 'حدث خطأ: ' . $e->getMessage();
    }
}

// جلب إعدادات الصيانة
try {
    $maintenance_setting = $db->fetch("SELECT value FROM settings WHERE name = 'maintenance_mode'");
    $maintenance_mode = $maintenance_setting ? $maintenance_setting['value'] : '0';

    $message_setting = $db->fetch("SELECT value FROM settings WHERE name = 'maintenance_message'");
    $maintenance_message = $message_setting ? $message_setting['value'] : 'الموقع تحت الصيانة';
    
    // إحصائيات النظام
    $stats = [
        'total_users' => $db->fetch("SELECT COUNT(*) as count FROM users")['count'],
        'total_ads' => $db->fetch("SELECT COUNT(*) as count FROM ads")['count'],
        'active_ads' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE status = 'active'")['count'],
        'pending_ads' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE status = 'pending'")['count'],
        'total_categories' => $db->fetch("SELECT COUNT(*) as count FROM categories")['count'],
        'total_messages' => $db->fetch("SELECT COUNT(*) as count FROM messages")['count'],
        'pending_reports' => $db->fetch("SELECT COUNT(*) as count FROM reports WHERE status = 'pending'")['count'],
    ];
    
    // معلومات الخادم
    $server_info = [
        'php_version' => phpversion(),
        'mysql_version' => $db->fetch("SELECT VERSION() as version")['version'],
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف',
        'disk_space' => disk_free_space('.'),
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
    ];
    
} catch (Exception $e) {
    $error = 'خطأ في جلب البيانات: ' . $e->getMessage();
    $maintenance_mode = '0';
    $maintenance_message = '';
    $stats = [];
    $server_info = [];
}

$page_title = 'صيانة النظام';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">صيانة النظام</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <?php if ($maintenance_mode == '1'): ?>
                        <span class="badge bg-warning fs-6 me-2">وضع الصيانة مفعل</span>
                    <?php else: ?>
                        <span class="badge bg-success fs-6 me-2">النظام يعمل بشكل طبيعي</span>
                    <?php endif; ?>
                </div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?= $error ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?= $success ?>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- وضع الصيانة -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-tools me-2"></i>
                                وضع الصيانة
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if ($maintenance_mode == '1'): ?>
                                <div class="alert alert-warning">
                                    <strong>وضع الصيانة مفعل حالياً</strong><br>
                                    الرسالة: <?= htmlspecialchars($maintenance_message) ?>
                                </div>
                                
                                <form method="POST">
                                    <input type="hidden" name="action" value="disable_maintenance">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-play"></i> إلغاء وضع الصيانة
                                    </button>
                                </form>
                            <?php else: ?>
                                <form method="POST">
                                    <input type="hidden" name="action" value="enable_maintenance">
                                    
                                    <div class="mb-3">
                                        <label for="maintenance_message" class="form-label">رسالة الصيانة</label>
                                        <textarea class="form-control" id="maintenance_message" name="maintenance_message" rows="3"><?= htmlspecialchars($maintenance_message) ?></textarea>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-pause"></i> تفعيل وضع الصيانة
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- أدوات الصيانة -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-wrench me-2"></i>
                                أدوات الصيانة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="clear_cache">
                                    <button type="submit" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-broom"></i> مسح الكاش
                                    </button>
                                </form>
                                
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="optimize_db">
                                    <button type="submit" class="btn btn-outline-info w-100">
                                        <i class="fas fa-database"></i> تحسين قاعدة البيانات
                                    </button>
                                </form>
                                
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="backup_db">
                                    <button type="submit" class="btn btn-outline-success w-100">
                                        <i class="fas fa-download"></i> إنشاء نسخة احتياطية
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات النظام -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-bar me-2"></i>
                                إحصائيات النظام
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <div class="text-center">
                                        <h4 class="text-primary"><?= number_format($stats['total_users']) ?></h4>
                                        <p class="mb-0">إجمالي المستخدمين</p>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="text-center">
                                        <h4 class="text-success"><?= number_format($stats['active_ads']) ?></h4>
                                        <p class="mb-0">الإعلانات النشطة</p>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="text-center">
                                        <h4 class="text-warning"><?= number_format($stats['pending_ads']) ?></h4>
                                        <p class="mb-0">الإعلانات المعلقة</p>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="text-center">
                                        <h4 class="text-danger"><?= number_format($stats['pending_reports']) ?></h4>
                                        <p class="mb-0">التقارير المعلقة</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات الخادم -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-server me-2"></i>
                                معلومات الخادم
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>إصدار PHP:</strong></td>
                                            <td><?= $server_info['php_version'] ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>إصدار MySQL:</strong></td>
                                            <td><?= $server_info['mysql_version'] ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>خادم الويب:</strong></td>
                                            <td><?= $server_info['server_software'] ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>المساحة المتاحة:</strong></td>
                                            <td><?= formatBytes($server_info['disk_space']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>حد الذاكرة:</strong></td>
                                            <td><?= $server_info['memory_limit'] ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>وقت التنفيذ الأقصى:</strong></td>
                                            <td><?= $server_info['max_execution_time'] ?> ثانية</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
