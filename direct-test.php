<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h2>🧪 اختبار مباشر للبلاغات</h2>";

// تسجيل دخول تلقائي
$user = $db->fetch("SELECT * FROM users LIMIT 1");
if ($user) {
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['logged_in'] = true;
    echo "<p>✅ تم تسجيل الدخول كـ: {$user['username']} (ID: {$user['id']})</p>";
}

// جلب إعلان للاختبار
$ad = $db->fetch("SELECT * FROM ads LIMIT 1");
if (!$ad) {
    echo "<p>❌ لا توجد إعلانات للاختبار</p>";
    exit;
}

echo "<p>✅ سيتم اختبار البلاغ على الإعلان: {$ad['title']} (ID: {$ad['id']})</p>";

echo "<h3>1. فحص هيكل جدول reports:</h3>";
$structure = $db->fetchAll("PRAGMA table_info(reports)");
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>العمود</th><th>النوع</th><th>NOT NULL</th></tr>";
foreach ($structure as $col) {
    $highlight = '';
    if ($col['name'] === 'ad_id') $highlight = 'background: #ffcccc;';
    if ($col['name'] === 'reported_ad_id') $highlight = 'background: #ccffcc;';
    echo "<tr style='$highlight'><td>{$col['name']}</td><td>{$col['type']}</td><td>" . ($col['notnull'] ? 'نعم' : 'لا') . "</td></tr>";
}
echo "</table>";

echo "<h3>2. اختبار الإدراج المباشر:</h3>";

// محاولة الإدراج بنفس الطريقة المستخدمة في ad-details.php
$ad_id = $ad['id'];
$user_id = $_SESSION['user_id'];
$reason = 'test';
$description = 'اختبار مباشر - ' . date('Y-m-d H:i:s');

echo "<p><strong>البيانات المُدرجة:</strong></p>";
echo "<ul>";
echo "<li>user_id: $user_id</li>";
echo "<li>reporter_id: $user_id</li>";
echo "<li>reported_ad_id: $ad_id</li>";
echo "<li>report_type: $reason</li>";
echo "<li>reason: $description</li>";
echo "</ul>";

echo "<p><strong>الاستعلام:</strong></p>";
echo "<pre>INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, created_at) VALUES ($user_id, $user_id, $ad_id, '$reason', '$description', datetime('now'))</pre>";

try {
    $db->query("INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))",
              [$user_id, $user_id, $ad_id, $reason, $description]);
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ نجح الإدراج!</h4>";
    
    // التحقق من الإدراج
    $inserted = $db->fetch("SELECT * FROM reports WHERE reason = ? ORDER BY id DESC LIMIT 1", [$description]);
    if ($inserted) {
        echo "<p>تم إدراج البلاغ بنجاح:</p>";
        echo "<ul>";
        echo "<li>ID: {$inserted['id']}</li>";
        echo "<li>user_id: {$inserted['user_id']}</li>";
        echo "<li>reporter_id: {$inserted['reporter_id']}</li>";
        echo "<li>reported_ad_id: {$inserted['reported_ad_id']}</li>";
        echo "<li>report_type: {$inserted['report_type']}</li>";
        echo "<li>created_at: {$inserted['created_at']}</li>";
        echo "</ul>";
        
        // حذف البيانات التجريبية
        $db->query("DELETE FROM reports WHERE id = ?", [$inserted['id']]);
        echo "<p>✅ تم حذف البيانات التجريبية</p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ فشل الإدراج!</h4>";
    echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>كود الخطأ:</strong> " . $e->getCode() . "</p>";
    
    // تحليل الخطأ
    if (strpos($e->getMessage(), 'reports.ad_id') !== false) {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h5>🔍 تحليل الخطأ:</h5>";
        echo "<p>الخطأ يشير إلى أن الجدول يحتوي على عمود <code>ad_id</code> وليس <code>reported_ad_id</code></p>";
        echo "<p><strong>الحل:</strong> يجب إصلاح هيكل الجدول</p>";
        echo "<button onclick='location.href=\"fix-reports-final.php\"' style='background: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;'>إصلاح الجدول</button>";
        echo "</div>";
    }
    echo "</div>";
}

echo "<h3>3. اختبار بديل (إذا فشل الأول):</h3>";
echo "<p>محاولة الإدراج باستخدام <code>ad_id</code> بدلاً من <code>reported_ad_id</code>:</p>";

try {
    $db->query("INSERT INTO reports (user_id, reporter_id, ad_id, report_type, reason, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))",
              [$user_id, $user_id, $ad_id, $reason, $description . ' - بديل']);
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>⚠️ نجح الإدراج بـ ad_id!</h4>";
    echo "<p>هذا يعني أن الجدول يستخدم <code>ad_id</code> وليس <code>reported_ad_id</code></p>";
    echo "<p><strong>المطلوب:</strong> تحديث الكود أو تحديث الجدول</p>";
    
    // حذف البيانات التجريبية
    $db->query("DELETE FROM reports WHERE reason = ?", [$description . ' - بديل']);
    echo "<p>✅ تم حذف البيانات التجريبية</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ فشل الإدراج أيضاً بـ ad_id!</h4>";
    echo "<p>رسالة الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>🔗 روابط للاختبار:</h3>";
echo "<a href='comprehensive-test.php' style='margin: 5px; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px;'>الاختبار الشامل</a>";
echo "<a href='pages/ad-details.php?id={$ad['id']}' target='_blank' style='margin: 5px; padding: 8px 16px; background: #28a745; color: white; text-decoration: none; border-radius: 4px;'>اختبار حقيقي</a>";
echo "<a href='check-database-structure.php' style='margin: 5px; padding: 8px 16px; background: #6c757d; color: white; text-decoration: none; border-radius: 4px;'>فحص قاعدة البيانات</a>";
?>
