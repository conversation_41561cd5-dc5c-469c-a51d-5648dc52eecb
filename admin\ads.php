<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// إنشاء كائن المصادقة
$auth = new Auth($db);

// التحقق من صلاحيات الإدمن
requireAdmin();

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && isset($_POST['ad_id'])) {
        $ad_id = (int)$_POST['ad_id'];
        $action = $_POST['action'];
        
        switch ($action) {
            case 'approve':
                $db->query("UPDATE ads SET status = 'active' WHERE id = ?", [$ad_id]);
                $_SESSION['success'] = 'تم الموافقة على الإعلان';
                break;
                
            case 'reject':
                $db->query("UPDATE ads SET status = 'rejected' WHERE id = ?", [$ad_id]);
                $_SESSION['success'] = 'تم رفض الإعلان';
                break;
                
            case 'feature':
                $db->query("UPDATE ads SET is_featured = 1 WHERE id = ?", [$ad_id]);
                $_SESSION['success'] = 'تم تمييز الإعلان';
                break;
                
            case 'unfeature':
                $db->query("UPDATE ads SET is_featured = 0 WHERE id = ?", [$ad_id]);
                $_SESSION['success'] = 'تم إلغاء تمييز الإعلان';
                break;
                
            case 'delete':
                // حذف الصور أولاً
                $images = $db->fetchAll("SELECT image_path FROM ad_images WHERE ad_id = ?", [$ad_id]);
                foreach ($images as $image) {
                    deleteImage($image['image_path']);
                }
                
                // حذف الإعلان
                $db->query("DELETE FROM ads WHERE id = ?", [$ad_id]);
                $_SESSION['success'] = 'تم حذف الإعلان';
                break;
        }
        
        header('Location: ads.php');
        exit();
    }
}

// فلترة الإعلانات
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : 'all';
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';

$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$items_per_page = 20;
$offset = ($page - 1) * $items_per_page;

// بناء الاستعلام
$where_conditions = [];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = "a.status = ?";
    $params[] = $status_filter;
}

if ($category_filter > 0) {
    $where_conditions[] = "a.category_id = ?";
    $params[] = $category_filter;
}

if (!empty($search)) {
    $where_conditions[] = "(a.title LIKE ? OR a.description LIKE ? OR u.username LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب الإعلانات
$sql = "
    SELECT a.*, u.username, u.full_name, c.name as category_name,
           (SELECT image_path FROM ad_images WHERE ad_id = a.id AND is_primary = 1 LIMIT 1) as primary_image
    FROM ads a
    JOIN users u ON a.user_id = u.id
    JOIN categories c ON a.category_id = c.id
    $where_clause
    ORDER BY a.created_at DESC
    LIMIT $items_per_page OFFSET $offset
";

$ads = $db->fetchAll($sql, $params);

// عدد النتائج الإجمالي
$count_sql = "
    SELECT COUNT(*) as total
    FROM ads a
    JOIN users u ON a.user_id = u.id
    JOIN categories c ON a.category_id = c.id
    $where_clause
";
$total_ads = $db->fetch($count_sql, $params)['total'];
$total_pages = ceil($total_ads / $items_per_page);

// إحصائيات سريعة
$stats = [
    'total' => $db->fetch("SELECT COUNT(*) as count FROM ads")['count'],
    'active' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE status = 'active'")['count'],
    'pending' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE status = 'pending'")['count'],
    'rejected' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE status = 'rejected'")['count'],
    'featured' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE is_featured = 1")['count'],
];

// جلب الفئات للفلتر
$categories = $db->fetchAll("SELECT * FROM categories WHERE parent_id IS NULL ORDER BY name");

$page_title = 'إدارة الإعلانات';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة الإعلانات</h1>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-2 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($stats['total']) ?></h4>
                                    <p class="mb-0">إجمالي</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-bullhorn fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($stats['active']) ?></h4>
                                    <p class="mb-0">نشطة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($stats['pending']) ?></h4>
                                    <p class="mb-0">قيد المراجعة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($stats['rejected']) ?></h4>
                                    <p class="mb-0">مرفوضة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-times-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($stats['featured']) ?></h4>
                                    <p class="mb-0">مميزة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-star fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <input type="text" name="search" class="form-control" placeholder="البحث..." value="<?= htmlspecialchars($search) ?>">
                        </div>
                        <div class="col-md-2">
                            <select name="status" class="form-select">
                                <option value="all">جميع الحالات</option>
                                <option value="active" <?= $status_filter === 'active' ? 'selected' : '' ?>>نشطة</option>
                                <option value="pending" <?= $status_filter === 'pending' ? 'selected' : '' ?>>قيد المراجعة</option>
                                <option value="rejected" <?= $status_filter === 'rejected' ? 'selected' : '' ?>>مرفوضة</option>
                                <option value="expired" <?= $status_filter === 'expired' ? 'selected' : '' ?>>منتهية الصلاحية</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select name="category" class="form-select">
                                <option value="">جميع الفئات</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id'] ?>" <?= $category_filter == $category['id'] ? 'selected' : '' ?>>
                                        <?= $category['name'] ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                        <div class="col-md-3">
                            <?php if (!empty($search) || $status_filter !== 'all' || $category_filter > 0): ?>
                                <a href="ads.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> مسح الفلاتر
                                </a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول الإعلانات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">قائمة الإعلانات (<?= number_format($total_ads) ?> إعلان)</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($ads)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الصورة</th>
                                        <th>العنوان</th>
                                        <th>البائع</th>
                                        <th>الفئة</th>
                                        <th>السعر</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($ads as $ad): ?>
                                        <tr>
                                            <td>
                                                <?php if ($ad['primary_image']): ?>
                                                    <img src="../uploads/<?= $ad['primary_image'] ?>" 
                                                         class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-light d-flex align-items-center justify-content-center" 
                                                         style="width: 50px; height: 50px;">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="fw-bold"><?= substr($ad['title'], 0, 30) ?>...</div>
                                                <small class="text-muted">ID: <?= $ad['id'] ?></small>
                                                <?php if ($ad['is_featured']): ?>
                                                    <span class="badge bg-warning text-dark">مميز</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div><?= $ad['full_name'] ?></div>
                                                <small class="text-muted">@<?= $ad['username'] ?></small>
                                            </td>
                                            <td><?= $ad['category_name'] ?></td>
                                            <td class="text-success fw-bold"><?= formatPrice($ad['price']) ?></td>
                                            <td>
                                                <span class="badge bg-<?= 
                                                    $ad['status'] === 'active' ? 'success' : 
                                                    ($ad['status'] === 'pending' ? 'warning' : 
                                                    ($ad['status'] === 'rejected' ? 'danger' : 'secondary')) 
                                                ?>">
                                                    <?php
                                                    $status_labels = [
                                                        'active' => 'نشط',
                                                        'pending' => 'قيد المراجعة',
                                                        'rejected' => 'مرفوض',
                                                        'expired' => 'منتهي الصلاحية'
                                                    ];
                                                    echo $status_labels[$ad['status']] ?? $ad['status'];
                                                    ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div><?= date('Y-m-d', strtotime($ad['created_at'])) ?></div>
                                                <small class="text-muted"><?= date('H:i', strtotime($ad['created_at'])) ?></small>
                                            </td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                            type="button" data-bs-toggle="dropdown">
                                                        إجراءات
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item" href="../pages/ad-details.php?id=<?= $ad['id'] ?>" target="_blank">
                                                                <i class="fas fa-eye"></i> عرض
                                                            </a>
                                                        </li>
                                                        
                                                        <?php if ($ad['status'] === 'pending'): ?>
                                                        <li>
                                                            <form method="post" class="d-inline">
                                                                <input type="hidden" name="ad_id" value="<?= $ad['id'] ?>">
                                                                <input type="hidden" name="action" value="approve">
                                                                <button type="submit" class="dropdown-item text-success">
                                                                    <i class="fas fa-check"></i> موافقة
                                                                </button>
                                                            </form>
                                                        </li>
                                                        <li>
                                                            <form method="post" class="d-inline">
                                                                <input type="hidden" name="ad_id" value="<?= $ad['id'] ?>">
                                                                <input type="hidden" name="action" value="reject">
                                                                <button type="submit" class="dropdown-item text-danger">
                                                                    <i class="fas fa-times"></i> رفض
                                                                </button>
                                                            </form>
                                                        </li>
                                                        <?php endif; ?>
                                                        
                                                        <?php if (!$ad['is_featured']): ?>
                                                        <li>
                                                            <form method="post" class="d-inline">
                                                                <input type="hidden" name="ad_id" value="<?= $ad['id'] ?>">
                                                                <input type="hidden" name="action" value="feature">
                                                                <button type="submit" class="dropdown-item text-warning">
                                                                    <i class="fas fa-star"></i> تمييز
                                                                </button>
                                                            </form>
                                                        </li>
                                                        <?php else: ?>
                                                        <li>
                                                            <form method="post" class="d-inline">
                                                                <input type="hidden" name="ad_id" value="<?= $ad['id'] ?>">
                                                                <input type="hidden" name="action" value="unfeature">
                                                                <button type="submit" class="dropdown-item">
                                                                    <i class="fas fa-star-half-alt"></i> إلغاء التمييز
                                                                </button>
                                                            </form>
                                                        </li>
                                                        <?php endif; ?>
                                                        
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <form method="post" class="d-inline">
                                                                <input type="hidden" name="ad_id" value="<?= $ad['id'] ?>">
                                                                <input type="hidden" name="action" value="delete">
                                                                <button type="submit" class="dropdown-item text-danger delete-btn" 
                                                                        data-title="<?= $ad['title'] ?>">
                                                                    <i class="fas fa-trash"></i> حذف
                                                                </button>
                                                            </form>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- الصفحات -->
                        <?php if ($total_pages > 1): ?>
                        <nav aria-label="صفحات الإعلانات" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>">السابق</a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                        <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"><?= $i ?></a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>">التالي</a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>

                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                            <h5>لا توجد إعلانات</h5>
                            <p class="text-muted">لا توجد إعلانات تطابق معايير البحث</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
