<?php
require_once 'functions.php';

class Auth {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    public function login($username, $password, $remember = false) {
        try {
            // البحث عن المستخدم
            $sql = "SELECT * FROM users WHERE (username = ? OR email = ?) AND is_active = 1";
            $user = $this->db->fetch($sql, [$username, $username]);
            
            if (!$user) {
                return ['success' => false, 'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'];
            }
            
            // التحقق من كلمة المرور
            if (!verifyPassword($password, $user['password'])) {
                return ['success' => false, 'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'];
            }
            
            // إنشاء الجلسة
            $this->createSession($user);
            
            // تحديث آخر تسجيل دخول
            $this->updateLastLogin($user['id']);
            
            // إنشاء cookie للتذكر إذا طُلب ذلك
            if ($remember) {
                $this->createRememberToken($user['id']);
            }
            
            // تسجيل النشاط
            $this->logActivity($user['id'], 'login', 'users', $user['id']);
            
            return ['success' => true, 'user' => $user];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'حدث خطأ أثناء تسجيل الدخول'];
        }
    }
    
    public function register($data) {
        try {
            // التحقق من صحة البيانات
            $validation = $this->validateRegistration($data);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }
            
            // التحقق من عدم وجود المستخدم
            if ($this->userExists($data['username'], $data['email'])) {
                return ['success' => false, 'message' => 'اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل'];
            }
            
            // تشفير كلمة المرور
            $hashedPassword = hashPassword($data['password']);
            
            // إنشاء رمز التحقق
            $verificationToken = generateToken();
            
            // إدراج المستخدم الجديد
            $sql = "INSERT INTO users (username, email, password, full_name, phone, city, region, verification_token, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $this->db->query($sql, [
                $data['username'],
                $data['email'],
                $hashedPassword,
                $data['full_name'],
                $data['phone'],
                $data['city'],
                $data['region'],
                $verificationToken
            ]);
            
            $userId = $this->db->lastInsertId();
            
            // إرسال بريد التحقق (محاكاة)
            $this->sendVerificationEmail($data['email'], $verificationToken);
            
            // تسجيل النشاط
            $this->logActivity($userId, 'register', 'users', $userId);
            
            return ['success' => true, 'message' => 'تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'حدث خطأ أثناء إنشاء الحساب'];
        }
    }
    
    public function logout() {
        // حذف remember token إذا وجد
        if (isset($_COOKIE['remember_token'])) {
            $this->deleteRememberToken($_COOKIE['remember_token']);
            setcookie('remember_token', '', time() - 3600, '/');
        }
        
        // تسجيل النشاط
        if (isset($_SESSION['user_id'])) {
            $this->logActivity($_SESSION['user_id'], 'logout', 'users', $_SESSION['user_id']);
        }
        
        // تدمير الجلسة
        session_destroy();
        
        return ['success' => true];
    }
    
    public function verifyEmail($token) {
        try {
            $sql = "SELECT id FROM users WHERE verification_token = ? AND is_verified = 0";
            $user = $this->db->fetch($sql, [$token]);
            
            if (!$user) {
                return ['success' => false, 'message' => 'رمز التحقق غير صحيح أو منتهي الصلاحية'];
            }
            
            // تحديث حالة التحقق
            $sql = "UPDATE users SET is_verified = 1, verification_token = NULL WHERE id = ?";
            $this->db->query($sql, [$user['id']]);
            
            return ['success' => true, 'message' => 'تم تأكيد البريد الإلكتروني بنجاح'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'حدث خطأ أثناء التحقق'];
        }
    }
    
    public function resetPassword($email) {
        try {
            $sql = "SELECT id FROM users WHERE email = ? AND is_active = 1";
            $user = $this->db->fetch($sql, [$email]);
            
            if (!$user) {
                return ['success' => false, 'message' => 'البريد الإلكتروني غير موجود'];
            }
            
            // إنشاء رمز إعادة تعيين
            $resetToken = generateToken();
            $resetExpires = date('Y-m-d H:i:s', strtotime('+1 hour'));
            
            $sql = "UPDATE users SET reset_token = ?, reset_expires = ? WHERE id = ?";
            $this->db->query($sql, [$resetToken, $resetExpires, $user['id']]);
            
            // إرسال بريد إعادة التعيين (محاكاة)
            $this->sendResetEmail($email, $resetToken);
            
            return ['success' => true, 'message' => 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'حدث خطأ أثناء إعادة تعيين كلمة المرور'];
        }
    }
    
    public function changePassword($token, $newPassword) {
        try {
            $sql = "SELECT id FROM users WHERE reset_token = ? AND reset_expires > NOW()";
            $user = $this->db->fetch($sql, [$token]);
            
            if (!$user) {
                return ['success' => false, 'message' => 'رمز إعادة التعيين غير صحيح أو منتهي الصلاحية'];
            }
            
            $hashedPassword = hashPassword($newPassword);
            
            $sql = "UPDATE users SET password = ?, reset_token = NULL, reset_expires = NULL WHERE id = ?";
            $this->db->query($sql, [$hashedPassword, $user['id']]);
            
            return ['success' => true, 'message' => 'تم تغيير كلمة المرور بنجاح'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'حدث خطأ أثناء تغيير كلمة المرور'];
        }
    }
    
    private function createSession($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['full_name'] = $user['full_name'];
        $_SESSION['user_type'] = $user['user_type'];
        $_SESSION['is_verified'] = $user['is_verified'];
        $_SESSION['last_activity'] = time();
    }
    
    private function updateLastLogin($userId) {
        $sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
        $this->db->query($sql, [$userId]);
    }
    
    private function createRememberToken($userId) {
        $token = generateToken();
        $expires = date('Y-m-d H:i:s', strtotime('+30 days'));
        
        // حفظ الرمز في قاعدة البيانات (يمكن إضافة جدول منفصل)
        setcookie('remember_token', $token, strtotime('+30 days'), '/');
    }
    
    private function deleteRememberToken($token) {
        // حذف الرمز من قاعدة البيانات
    }
    
    private function validateRegistration($data) {
        if (empty($data['username']) || strlen($data['username']) < 3) {
            return ['valid' => false, 'message' => 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل'];
        }
        
        if (!validateEmail($data['email'])) {
            return ['valid' => false, 'message' => 'البريد الإلكتروني غير صحيح'];
        }
        
        if (empty($data['password']) || strlen($data['password']) < 6) {
            return ['valid' => false, 'message' => 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'];
        }
        
        if (empty($data['full_name'])) {
            return ['valid' => false, 'message' => 'الاسم الكامل مطلوب'];
        }
        
        if (!empty($data['phone']) && !validatePhone($data['phone'])) {
            return ['valid' => false, 'message' => 'رقم الهاتف غير صحيح'];
        }
        
        return ['valid' => true];
    }
    
    private function userExists($username, $email) {
        $sql = "SELECT id FROM users WHERE username = ? OR email = ?";
        $user = $this->db->fetch($sql, [$username, $email]);
        return $user !== false;
    }
    
    private function sendVerificationEmail($email, $token) {
        // محاكاة إرسال البريد الإلكتروني
        // في التطبيق الحقيقي، استخدم مكتبة مثل PHPMailer
        $verificationLink = SITE_URL . "/pages/verify.php?token=" . $token;
        // mail($email, "تأكيد البريد الإلكتروني", "يرجى النقر على الرابط: " . $verificationLink);
    }
    
    private function sendResetEmail($email, $token) {
        // محاكاة إرسال البريد الإلكتروني
        $resetLink = SITE_URL . "/pages/reset-password.php?token=" . $token;
        // mail($email, "إعادة تعيين كلمة المرور", "يرجى النقر على الرابط: " . $resetLink);
    }
    
    private function logActivity($userId, $action, $table, $recordId) {
        $sql = "INSERT INTO activity_logs (user_id, action, table_name, record_id, ip_address, user_agent, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())";
        
        $this->db->query($sql, [
            $userId,
            $action,
            $table,
            $recordId,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    }
    
    public function checkSession() {
        // التحقق من انتهاء صلاحية الجلسة
        if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity']) > SESSION_TIMEOUT) {
            $this->logout();
            return false;
        }
        
        $_SESSION['last_activity'] = time();
        return isset($_SESSION['user_id']);
    }
    
    public function getCurrentUser() {
        if (!$this->checkSession()) {
            return null;
        }
        
        $sql = "SELECT * FROM users WHERE id = ? AND is_active = 1";
        return $this->db->fetch($sql, [$_SESSION['user_id']]);
    }
}

// إنشاء كائن المصادقة
$auth = new Auth($db);
?>
