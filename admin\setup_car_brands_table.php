<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدمن
requireAdmin();

try {
    // إنشاء جدول ماركات السيارات
    $db->query("
        CREATE TABLE IF NOT EXISTS car_brands (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE,
            arabic_name VARCHAR(100) NOT NULL,
            logo_path VARCHAR(255) NULL,
            is_popular BOOLEAN DEFAULT FALSE,
            is_active BOOLEAN DEFAULT TRUE,
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_name (name),
            INDEX idx_popular (is_popular),
            INDEX idx_active (is_active),
            INDEX idx_sort (sort_order)
        )
    ");
    
    // إضافة عمود car_brand_id إلى جدول الإعلانات
    try {
        $db->query("ALTER TABLE ads ADD COLUMN car_brand_id INT NULL AFTER category_id");
        $db->query("ALTER TABLE ads ADD INDEX idx_car_brand (car_brand_id)");
    } catch (Exception $e) {
        // العمود موجود مسبقاً
    }
    
    // التحقق من وجود ماركات
    $existing_brands = $db->fetch("SELECT COUNT(*) as count FROM car_brands")['count'];
    
    if ($existing_brands == 0) {
        // إدراج ماركات السيارات
        $car_brands = [
            ['Toyota', 'تويوتا', 'toyota-logo.svg', 1],
            ['Nissan', 'نيسان', 'nissan-logo.svg', 1],
            ['Honda', 'هوندا', 'honda-logo.svg', 1],
            ['Hyundai', 'هيونداي', 'hyundai-logo.svg', 1],
            ['Kia', 'كيا', 'kia-logo.svg', 1],
            ['Chevrolet', 'شيفروليه', 'chevrolet-logo.svg', 1],
            ['Ford', 'فورد', 'ford-logo.svg', 1],
            ['BMW', 'بي إم دبليو', 'bmw-logo.svg', 1],
            ['Mercedes', 'مرسيدس', 'mercedes-logo.svg', 1],
            ['Audi', 'أودي', 'audi-logo.svg', 1],
            ['Lexus', 'لكزس', 'lexus-logo.svg', 1],
            ['Mazda', 'مازدا', 'mazda-logo.svg', 1],
            ['Volkswagen', 'فولكس واجن', 'volkswagen-logo.svg', 0],
            ['Peugeot', 'بيجو', 'peugeot-logo.svg', 0],
            ['Renault', 'رينو', 'renault-logo.svg', 0],
            ['Mitsubishi', 'ميتسوبيشي', 'mitsubishi-logo.svg', 0],
            ['Subaru', 'سوبارو', 'subaru-logo.svg', 0],
            ['Suzuki', 'سوزوكي', 'suzuki-logo.svg', 0],
            ['Infiniti', 'إنفينيتي', 'infiniti-logo.svg', 0],
            ['Acura', 'أكورا', 'acura-logo.svg', 0],
            ['Cadillac', 'كاديلاك', 'cadillac-logo.svg', 0],
            ['Jeep', 'جيب', 'jeep-logo.svg', 0],
            ['Land Rover', 'لاند روفر', 'landrover-logo.svg', 0],
            ['Porsche', 'بورش', 'porsche-logo.svg', 0],
            ['Jaguar', 'جاكوار', 'jaguar-logo.svg', 0],
            ['Volvo', 'فولفو', 'volvo-logo.svg', 0],
            ['Genesis', 'جينيسيس', 'genesis-logo.svg', 0],
            ['Tesla', 'تيسلا', 'tesla-logo.svg', 0],
            ['GMC', 'جي إم سي', 'gmc-logo.svg', 0],
            ['Dodge', 'دودج', 'dodge-logo.svg', 0]
        ];
        
        foreach ($car_brands as $index => $brand) {
            $db->query(
                "INSERT INTO car_brands (name, arabic_name, logo_path, is_popular, sort_order) VALUES (?, ?, ?, ?, ?)",
                [$brand[0], $brand[1], $brand[2], $brand[3], $index + 1]
            );
        }
        
        echo "تم إنشاء جدول ماركات السيارات وإدراج " . count($car_brands) . " ماركة بنجاح!";
    } else {
        echo "جدول ماركات السيارات موجود مسبقاً ويحتوي على {$existing_brands} ماركة.";
    }
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
