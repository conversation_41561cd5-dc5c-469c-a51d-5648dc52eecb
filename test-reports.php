<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// تسجيل دخول تجريبي للاختبار
if (!isLoggedIn()) {
    $user = $db->fetch("SELECT * FROM users LIMIT 1");
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['logged_in'] = true;
    }
}

$success = '';
$error = '';

// معالجة إرسال البلاغ
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'report_ad') {
    if (isLoggedIn()) {
        $ad_id = (int)($_POST['ad_id'] ?? 0);
        $reason = sanitize($_POST['reason'] ?? '');
        $description = sanitize($_POST['description'] ?? '');

        if (!empty($reason) && $ad_id > 0) {
            try {
                $db->query("INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))",
                          [$_SESSION['user_id'], $_SESSION['user_id'], $ad_id, $reason, $description]);
                $success = 'تم إرسال البلاغ بنجاح';
            } catch (Exception $e) {
                $error = 'خطأ في إرسال البلاغ: ' . $e->getMessage();
            }
        } else {
            $error = 'يجب اختيار سبب البلاغ وتحديد الإعلان';
        }
    } else {
        $error = 'يجب تسجيل الدخول أولاً';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البلاغات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>🚨 اختبار البلاغات</h2>
        
        <?php if ($success): ?>
            <div class="alert alert-success"><?= $success ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>
        
        <?php if (isLoggedIn()): ?>
            <div class="alert alert-success">
                ✅ تم تسجيل الدخول كـ: <?= $_SESSION['username'] ?>
            </div>
            
            <?php
            $ads = $db->fetchAll("SELECT * FROM ads WHERE status = 'active' LIMIT 5");
            ?>
            
            <?php if ($ads): ?>
                <h3>الإعلانات المتاحة للإبلاغ عنها:</h3>
                <div class="row">
                    <?php foreach ($ads as $ad): ?>
                        <div class="col-md-6 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title"><?= htmlspecialchars($ad['title']) ?></h5>
                                    <p class="card-text"><?= htmlspecialchars(substr($ad['description'], 0, 100)) ?>...</p>
                                    <p class="text-success fw-bold"><?= number_format($ad['price']) ?> ريال</p>
                                    
                                    <button class="btn btn-outline-warning report-ad" data-ad-id="<?= $ad['id'] ?>">
                                        <i class="fas fa-flag"></i> إبلاغ
                                    </button>
                                    
                                    <a href="pages/ad-details.php?id=<?= $ad['id'] ?>" class="btn btn-primary">
                                        عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <h3>البلاغات المرسلة:</h3>
                <?php
                $reports = $db->fetchAll("
                    SELECT r.*, a.title as ad_title 
                    FROM reports r 
                    LEFT JOIN ads a ON r.reported_ad_id = a.id 
                    WHERE r.user_id = ? 
                    ORDER BY r.created_at DESC
                ", [$_SESSION['user_id']]);
                ?>
                
                <?php if ($reports): ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الإعلان</th>
                                    <th>السبب</th>
                                    <th>الوصف</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($reports as $report): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($report['ad_title'] ?? 'غير محدد') ?></td>
                                        <td><?= htmlspecialchars($report['report_type']) ?></td>
                                        <td><?= htmlspecialchars($report['reason']) ?></td>
                                        <td>
                                            <span class="badge bg-<?= $report['status'] === 'pending' ? 'warning' : 'success' ?>">
                                                <?= $report['status'] ?>
                                            </span>
                                        </td>
                                        <td><?= date('Y-m-d H:i', strtotime($report['created_at'])) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-muted">لم ترسل أي بلاغات بعد</p>
                <?php endif; ?>
                
            <?php else: ?>
                <div class="alert alert-warning">
                    لا توجد إعلانات للاختبار. <a href="install/install.php">قم بإعداد الموقع أولاً</a>
                </div>
            <?php endif; ?>
            
        <?php else: ?>
            <div class="alert alert-danger">
                لم يتم تسجيل الدخول. <a href="pages/login.php">سجل الدخول</a>
            </div>
        <?php endif; ?>
        
        <a href="test-fixes.php" class="btn btn-secondary">العودة للاختبارات</a>
    </div>

    <!-- Modal للإبلاغ -->
    <div class="modal fade" id="reportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إبلاغ عن الإعلان</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="report_ad">
                    <input type="hidden" name="ad_id" id="report_ad_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="reason" class="form-label">سبب الإبلاغ</label>
                            <select class="form-select" name="reason" required>
                                <option value="">اختر السبب</option>
                                <option value="spam">رسائل مزعجة</option>
                                <option value="inappropriate">محتوى غير مناسب</option>
                                <option value="fake">إعلان مزيف</option>
                                <option value="fraud">احتيال</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">تفاصيل إضافية (اختياري)</label>
                            <textarea class="form-control" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">إرسال البلاغ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // إظهار modal الإبلاغ
    document.querySelectorAll('.report-ad').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const adId = this.getAttribute('data-ad-id');
            document.getElementById('report_ad_id').value = adId;
            
            const modal = new bootstrap.Modal(document.getElementById('reportModal'));
            modal.show();
        });
    });
    </script>
</body>
</html>
