<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ad_id'])) {
    $ad_id = (int)$_POST['ad_id'];
    
    try {
        // التحقق من ملكية الإعلان
        $ad = $db->fetch("SELECT * FROM ads WHERE id = ? AND user_id = ?", [$ad_id, $_SESSION['user_id']]);
        
        if (!$ad) {
            $error = 'الإعلان غير موجود أو ليس لديك صلاحية لتعديله';
        } else {
            // تحديث الإعلان كمباع
            $sold_at = date('Y-m-d H:i:s');
            $hide_sold_at = date('Y-m-d H:i:s', strtotime('+3 days')); // إخفاء بعد 3 أيام
            
            $db->query(
                "UPDATE ads SET is_sold = 1, sold_at = ?, hide_sold_at = ? WHERE id = ?",
                [$sold_at, $hide_sold_at, $ad_id]
            );
            
            $success = 'تم تحديد الإعلان كمباع بنجاح. سيختفي الإعلان تلقائياً بعد 3 أيام.';
        }
        
    } catch (Exception $e) {
        $error = 'حدث خطأ: ' . $e->getMessage();
    }
}

// إعادة توجيه مع الرسالة
if ($success) {
    header('Location: my-ads.php?success=' . urlencode($success));
} else {
    header('Location: my-ads.php?error=' . urlencode($error));
}
exit();
?>
