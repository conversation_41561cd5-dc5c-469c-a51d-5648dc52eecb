<?php
// إصلاح قسري لجدول البلاغات
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h2>🔧 إصلاح قسري لجدول البلاغات</h2>";

try {
    echo "<h3>الخطوة 1: حفظ البيانات الموجودة</h3>";
    
    // حفظ البيانات الموجودة
    $existing_reports = [];
    try {
        $existing_reports = $db->fetchAll("SELECT * FROM reports");
        echo "✅ تم حفظ " . count($existing_reports) . " بلاغ موجود<br>";
    } catch (Exception $e) {
        echo "ℹ️ لا توجد بيانات سابقة أو خطأ في القراءة: " . $e->getMessage() . "<br>";
    }
    
    echo "<h3>الخطوة 2: حذف الجدول القديم</h3>";
    try {
        $db->query("DROP TABLE IF EXISTS reports");
        echo "✅ تم حذف الجدول القديم<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في حذف الجدول: " . $e->getMessage() . "<br>";
    }
    
    echo "<h3>الخطوة 3: إنشاء الجدول الجديد</h3>";
    try {
        $db->query("
            CREATE TABLE reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                reporter_id INTEGER NOT NULL,
                reported_ad_id INTEGER NOT NULL,
                report_type TEXT NOT NULL,
                reason TEXT,
                status TEXT DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (reporter_id) REFERENCES users(id),
                FOREIGN KEY (reported_ad_id) REFERENCES ads(id)
            )
        ");
        echo "✅ تم إنشاء الجدول الجديد بنجاح<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في إنشاء الجدول: " . $e->getMessage() . "<br>";
        throw $e;
    }
    
    echo "<h3>الخطوة 4: استعادة البيانات</h3>";
    if (!empty($existing_reports)) {
        $restored = 0;
        foreach ($existing_reports as $report) {
            try {
                // تحديد العمود الصحيح للإعلان
                $ad_id = isset($report['reported_ad_id']) ? $report['reported_ad_id'] : 
                        (isset($report['ad_id']) ? $report['ad_id'] : null);
                
                if ($ad_id) {
                    $db->query("
                        INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, status, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ", [
                        $report['user_id'],
                        $report['reporter_id'] ?? $report['user_id'],
                        $ad_id,
                        $report['report_type'] ?? 'other',
                        $report['reason'] ?? '',
                        $report['status'] ?? 'pending',
                        $report['created_at'] ?? date('Y-m-d H:i:s')
                    ]);
                    $restored++;
                }
            } catch (Exception $e) {
                echo "⚠️ خطأ في استعادة بلاغ: " . $e->getMessage() . "<br>";
            }
        }
        echo "✅ تم استعادة $restored بلاغ<br>";
    } else {
        echo "ℹ️ لا توجد بيانات سابقة لاستعادتها<br>";
    }
    
    echo "<h3>الخطوة 5: فحص الجدول الجديد</h3>";
    $structure = $db->fetchAll("PRAGMA table_info(reports)");
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>NOT NULL</th><th>القيمة الافتراضية</th></tr>";
    foreach ($structure as $col) {
        $highlight = ($col['name'] === 'reported_ad_id') ? 'background: #d4edda;' : '';
        echo "<tr style='$highlight'>";
        echo "<td><strong>{$col['name']}</strong></td>";
        echo "<td>{$col['type']}</td>";
        echo "<td>" . ($col['notnull'] ? 'نعم' : 'لا') . "</td>";
        echo "<td>{$col['dflt_value']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>الخطوة 6: اختبار الإدراج</h3>";
    
    // جلب بيانات للاختبار
    $test_user = $db->fetch("SELECT id FROM users LIMIT 1");
    $test_ad = $db->fetch("SELECT id FROM ads WHERE status = 'active' LIMIT 1");
    
    if ($test_user && $test_ad) {
        try {
            // اختبار الإدراج
            $db->query("
                INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, created_at) 
                VALUES (?, ?, ?, ?, ?, datetime('now'))
            ", [
                $test_user['id'], 
                $test_user['id'], 
                $test_ad['id'], 
                'test', 
                'اختبار الإصلاح - سيتم حذفه'
            ]);
            
            echo "✅ تم اختبار الإدراج بنجاح<br>";
            
            // التحقق من الإدراج
            $inserted = $db->fetch("SELECT * FROM reports WHERE reason = 'اختبار الإصلاح - سيتم حذفه'");
            if ($inserted) {
                echo "✅ تم العثور على البلاغ المُدرج: ID = {$inserted['id']}<br>";
                
                // حذف البيانات التجريبية
                $db->query("DELETE FROM reports WHERE reason = 'اختبار الإصلاح - سيتم حذفه'");
                echo "✅ تم حذف البيانات التجريبية<br>";
            }
            
        } catch (Exception $e) {
            echo "❌ خطأ في اختبار الإدراج: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "⚠️ لا توجد بيانات مستخدمين أو إعلانات للاختبار<br>";
        echo "المستخدم: " . ($test_user ? "✅" : "❌") . "<br>";
        echo "الإعلان: " . ($test_ad ? "✅" : "❌") . "<br>";
    }
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;'>";
    echo "<h3>🎉 تم الإصلاح بنجاح!</h3>";
    echo "<p>جدول البلاغات الآن يحتوي على العمود الصحيح <code>reported_ad_id</code></p>";
    echo "<p>يمكنك الآن اختبار إرسال البلاغات</p>";
    echo "<a href='pages/ad-details.php?id={$test_ad['id']}' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار البلاغ</a>";
    echo "<a href='test-reports.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة اختبار البلاغات</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ في الإصلاح:</h4>";
    echo "<p><strong>الرسالة:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<a href='debug.php' style='margin: 5px; padding: 8px 16px; background: #6c757d; color: white; text-decoration: none; border-radius: 4px;'>تشخيص المشاكل</a>";
echo "<a href='test-fixes.php' style='margin: 5px; padding: 8px 16px; background: #17a2b8; color: white; text-decoration: none; border-radius: 4px;'>اختبار شامل</a>";
echo "<a href='fixes-summary.php' style='margin: 5px; padding: 8px 16px; background: #28a745; color: white; text-decoration: none; border-radius: 4px;'>ملخص الإصلاحات</a>";
?>
