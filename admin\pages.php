<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدمن
requireAdmin();

$error = '';
$success = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        try {
            switch ($action) {
                case 'add_page':
                    $title = sanitize($_POST['title']);
                    $slug = sanitize($_POST['slug']);
                    $content = $_POST['content']; // لا نقوم بتنظيف المحتوى لأنه قد يحتوي على HTML
                    $meta_description = sanitize($_POST['meta_description']);
                    $is_active = isset($_POST['is_active']) ? 1 : 0;
                    
                    // التحقق من عدم تكرار الـ slug
                    $existing = $db->fetch("SELECT id FROM pages WHERE slug = ?", [$slug]);
                    if ($existing) {
                        $error = 'الرابط المختصر موجود مسبقاً';
                    } else {
                        $db->query("INSERT INTO pages (title, slug, content, meta_description, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())", 
                                  [$title, $slug, $content, $meta_description, $is_active]);
                        $success = 'تم إضافة الصفحة بنجاح';
                    }
                    break;
                    
                case 'edit_page':
                    $page_id = (int)$_POST['page_id'];
                    $title = sanitize($_POST['title']);
                    $slug = sanitize($_POST['slug']);
                    $content = $_POST['content'];
                    $meta_description = sanitize($_POST['meta_description']);
                    $is_active = isset($_POST['is_active']) ? 1 : 0;
                    
                    // التحقق من عدم تكرار الـ slug
                    $existing = $db->fetch("SELECT id FROM pages WHERE slug = ? AND id != ?", [$slug, $page_id]);
                    if ($existing) {
                        $error = 'الرابط المختصر موجود مسبقاً';
                    } else {
                        $db->query("UPDATE pages SET title = ?, slug = ?, content = ?, meta_description = ?, is_active = ?, updated_at = NOW() WHERE id = ?", 
                                  [$title, $slug, $content, $meta_description, $is_active, $page_id]);
                        $success = 'تم تحديث الصفحة بنجاح';
                    }
                    break;
                    
                case 'delete_page':
                    $page_id = (int)$_POST['page_id'];
                    $db->query("DELETE FROM pages WHERE id = ?", [$page_id]);
                    $success = 'تم حذف الصفحة بنجاح';
                    break;
                    
                case 'toggle_status':
                    $page_id = (int)$_POST['page_id'];
                    $db->query("UPDATE pages SET is_active = NOT is_active, updated_at = NOW() WHERE id = ?", [$page_id]);
                    $success = 'تم تغيير حالة الصفحة';
                    break;
            }
        } catch (Exception $e) {
            $error = 'حدث خطأ: ' . $e->getMessage();
        }
        
        if (!$error) {
            header('Location: pages.php');
            exit();
        }
    }
}

// جلب الصفحات
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : 'all';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$items_per_page = 20;
$offset = ($page - 1) * $items_per_page;

$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(title LIKE ? OR content LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status_filter !== 'all') {
    $where_conditions[] = "is_active = ?";
    $params[] = ($status_filter === 'active') ? 1 : 0;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

$sql = "SELECT * FROM pages $where_clause ORDER BY created_at DESC LIMIT $items_per_page OFFSET $offset";
$pages = $db->fetchAll($sql, $params);

// عدد النتائج الإجمالي
$count_sql = "SELECT COUNT(*) as total FROM pages $where_clause";
$total_pages_count = $db->fetch($count_sql, $params)['total'];
$total_pages = ceil($total_pages_count / $items_per_page);

// إحصائيات
$stats = [
    'total' => $db->fetch("SELECT COUNT(*) as count FROM pages")['count'],
    'active' => $db->fetch("SELECT COUNT(*) as count FROM pages WHERE is_active = 1")['count'],
    'inactive' => $db->fetch("SELECT COUNT(*) as count FROM pages WHERE is_active = 0")['count'],
];

$page_title = 'إدارة الصفحات';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة الصفحات</h1>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPageModal">
                    <i class="fas fa-plus"></i> إضافة صفحة جديدة
                </button>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?= $error ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?= $success ?>
                </div>
            <?php endif; ?>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-4 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4><?= number_format($stats['total']) ?></h4>
                            <p class="mb-0">إجمالي الصفحات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4><?= number_format($stats['active']) ?></h4>
                            <p class="mb-0">صفحات نشطة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h4><?= number_format($stats['inactive']) ?></h4>
                            <p class="mb-0">صفحات غير نشطة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?= htmlspecialchars($search) ?>" placeholder="ابحث في العنوان أو المحتوى">
                        </div>
                        <div class="col-md-4">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="all" <?= $status_filter === 'all' ? 'selected' : '' ?>>جميع الصفحات</option>
                                <option value="active" <?= $status_filter === 'active' ? 'selected' : '' ?>>نشطة</option>
                                <option value="inactive" <?= $status_filter === 'inactive' ? 'selected' : '' ?>>غير نشطة</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block w-100">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول الصفحات -->
            <div class="card">
                <div class="card-body">
                    <?php if (empty($pages)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <h5>لا توجد صفحات</h5>
                            <p class="text-muted">لم يتم العثور على أي صفحات</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>العنوان</th>
                                        <th>الرابط المختصر</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>آخر تحديث</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pages as $page): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($page['title']) ?></strong>
                                                <?php if ($page['meta_description']): ?>
                                                    <br><small class="text-muted"><?= htmlspecialchars(substr($page['meta_description'], 0, 100)) ?>...</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <code><?= htmlspecialchars($page['slug']) ?></code>
                                                <br><a href="<?= SITE_URL ?>/pages/<?= $page['slug'] ?>.php" target="_blank" class="small">
                                                    <i class="fas fa-external-link-alt"></i> عرض
                                                </a>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $page['is_active'] ? 'success' : 'secondary' ?>">
                                                    <?= $page['is_active'] ? 'نشطة' : 'غير نشطة' ?>
                                                </span>
                                            </td>
                                            <td><?= date('Y-m-d H:i', strtotime($page['created_at'])) ?></td>
                                            <td><?= date('Y-m-d H:i', strtotime($page['updated_at'])) ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            onclick="editPage(<?= $page['id'] ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="action" value="toggle_status">
                                                        <input type="hidden" name="page_id" value="<?= $page['id'] ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-<?= $page['is_active'] ? 'warning' : 'success' ?>"
                                                                onclick="return confirm('هل أنت متأكد؟')">
                                                            <i class="fas fa-<?= $page['is_active'] ? 'eye-slash' : 'eye' ?>"></i>
                                                        </button>
                                                    </form>
                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="action" value="delete_page">
                                                        <input type="hidden" name="page_id" value="<?= $page['id'] ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                                onclick="return confirm('هل أنت متأكد من حذف هذه الصفحة؟')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- ترقيم الصفحات -->
                        <?php if ($total_pages > 1): ?>
                            <nav aria-label="ترقيم الصفحات">
                                <ul class="pagination justify-content-center">
                                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                            <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= $status_filter ?>">
                                                <?= $i ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- نموذج إضافة صفحة جديدة -->
<div class="modal fade" id="addPageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة صفحة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_page">

                    <div class="mb-3">
                        <label for="title" class="form-label">عنوان الصفحة *</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>

                    <div class="mb-3">
                        <label for="slug" class="form-label">الرابط المختصر *</label>
                        <input type="text" class="form-control" id="slug" name="slug" required
                               placeholder="مثال: about-us">
                        <div class="form-text">سيكون الرابط: <?= SITE_URL ?>/pages/[الرابط المختصر].php</div>
                    </div>

                    <div class="mb-3">
                        <label for="meta_description" class="form-label">وصف الصفحة (SEO)</label>
                        <textarea class="form-control" id="meta_description" name="meta_description" rows="2"
                                  placeholder="وصف مختصر للصفحة يظهر في محركات البحث"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">محتوى الصفحة *</label>
                        <textarea class="form-control" id="content" name="content" rows="10" required></textarea>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">
                            صفحة نشطة
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة الصفحة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج تعديل الصفحة -->
<div class="modal fade" id="editPageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الصفحة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editPageForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit_page">
                    <input type="hidden" name="page_id" id="edit_page_id">

                    <div class="mb-3">
                        <label for="edit_title" class="form-label">عنوان الصفحة *</label>
                        <input type="text" class="form-control" id="edit_title" name="title" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_slug" class="form-label">الرابط المختصر *</label>
                        <input type="text" class="form-control" id="edit_slug" name="slug" required>
                        <div class="form-text">سيكون الرابط: <?= SITE_URL ?>/pages/[الرابط المختصر].php</div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_meta_description" class="form-label">وصف الصفحة (SEO)</label>
                        <textarea class="form-control" id="edit_meta_description" name="meta_description" rows="2"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="edit_content" class="form-label">محتوى الصفحة *</label>
                        <textarea class="form-control" id="edit_content" name="content" rows="10" required></textarea>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                        <label class="form-check-label" for="edit_is_active">
                            صفحة نشطة
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// تحويل العنوان إلى slug تلقائياً
document.getElementById('title').addEventListener('input', function() {
    const title = this.value;
    const slug = title.toLowerCase()
        .replace(/[أ-ي]/g, function(match) {
            const arabicToEnglish = {
                'أ': 'a', 'ب': 'b', 'ت': 't', 'ث': 'th', 'ج': 'j', 'ح': 'h', 'خ': 'kh',
                'د': 'd', 'ذ': 'dh', 'ر': 'r', 'ز': 'z', 'س': 's', 'ش': 'sh', 'ص': 's',
                'ض': 'd', 'ط': 't', 'ظ': 'z', 'ع': 'a', 'غ': 'gh', 'ف': 'f', 'ق': 'q',
                'ك': 'k', 'ل': 'l', 'م': 'm', 'ن': 'n', 'ه': 'h', 'و': 'w', 'ي': 'y'
            };
            return arabicToEnglish[match] || match;
        })
        .replace(/\s+/g, '-')
        .replace(/[^a-z0-9\-]/g, '');
    document.getElementById('slug').value = slug;
});

// تحميل بيانات الصفحة للتعديل
function editPage(pageId) {
    fetch(`get_page.php?id=${pageId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('edit_page_id').value = data.page.id;
                document.getElementById('edit_title').value = data.page.title;
                document.getElementById('edit_slug').value = data.page.slug;
                document.getElementById('edit_meta_description').value = data.page.meta_description || '';
                document.getElementById('edit_content').value = data.page.content;
                document.getElementById('edit_is_active').checked = data.page.is_active == 1;

                new bootstrap.Modal(document.getElementById('editPageModal')).show();
            } else {
                alert('خطأ في تحميل بيانات الصفحة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('خطأ في تحميل بيانات الصفحة');
        });
}
</script>

<?php include 'includes/footer.php'; ?>
