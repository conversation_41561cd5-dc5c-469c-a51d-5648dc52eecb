<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

try {
    require_once $root_path . '/config/database.php';
    require_once $root_path . '/config/config.php';
    require_once $root_path . '/includes/functions.php';
} catch (Exception $e) {
    die("خطأ في تحميل الملفات: " . $e->getMessage());
}

$page_title = 'الصفحة الرئيسية - حراجنا';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?></title>
    <meta name="description" content="موقع حراجنا للإعلانات المبوبة في المملكة العربية السعودية">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/simple-style.css" rel="stylesheet">
    <link href="../assets/css/car-brands.css" rel="stylesheet">
</head>
<body>

<!-- شريط التنقل -->
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
        <a class="navbar-brand" href="home.php">
            <i class="fas fa-store"></i> حراجنا
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link active" href="home.php">الرئيسية</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="categories.php">الفئات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="about.php">من نحن</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="contact.php">اتصل بنا</a>
                </li>
            </ul>

            <ul class="navbar-nav">
                <?php if (isset($_SESSION['user_id'])): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?= $_SESSION['full_name'] ?? 'المستخدم' ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="my-ads.php">إعلاناتي</a></li>
                            <li><a class="dropdown-item" href="favorites.php">المفضلة</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-success text-white ms-2" href="add-ad.php">
                            <i class="fas fa-plus"></i> أضف إعلان
                        </a>
                    </li>
                <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link" href="login.php">تسجيل الدخول</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-success text-white ms-2" href="register.php">
                            إنشاء حساب
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</nav>


    <!-- قسم السيارات الشهيرة -->
    <section class="car-brands-section">
        <div class="car-brands-container" id="car-brands-container">
            <h2 class="car-brands-title">
                <i class="fas fa-car me-2"></i>
                ابحث حسب ماركة السيارة
            </h2>
            <div class="car-brands-grid">
                <!-- مؤشر التحميل -->
                <div class="loading-spinner text-center" style="grid-column: 1 / -1;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل ماركات السيارات...</p>
                </div>
            </div>
            <button class="show-more-btn">
                <i class="fas fa-chevron-down me-2"></i>
                عرض المزيد
            </button>
        </div>
    </section>

    <!-- المحتوى الرئيسي -->
    <div class="container mt-4">
        <!-- الإعلانات العامة -->
        <?php
        $announcements = $db->fetchAll("
            SELECT * FROM announcements
            WHERE is_active = 1 AND show_on_homepage = 1
            ORDER BY created_at DESC
            LIMIT 3
        ");

        if (!empty($announcements)):
        ?>
        <div class="row mb-4">
            <div class="col-12">
                <?php foreach ($announcements as $announcement): ?>
                    <div class="alert alert-<?= $announcement['type'] ?> alert-dismissible fade show" role="alert">
                        <h6 class="alert-heading mb-2">
                            <i class="fas fa-bullhorn"></i> <?= $announcement['title'] ?>
                        </h6>
                        <p class="mb-0"><?= $announcement['content'] ?></p>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Hero Section -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center py-5">
                        <h1 class="display-4 mb-3">مرحباً بك في حراجنا</h1>
                        <p class="lead">منصة الإعلانات المبوبة الأولى في المملكة العربية السعودية</p>
                        <div class="mt-4">
                            <a href="categories.php" class="btn btn-light btn-lg me-3">
                                <i class="fas fa-search"></i> تصفح الإعلانات
                            </a>
                            <?php if (!isset($_SESSION['user_id'])): ?>
                                <a href="register.php" class="btn btn-outline-light btn-lg">
                                    <i class="fas fa-user-plus"></i> إنشاء حساب
                                </a>
                            <?php else: ?>
                                <a href="add-ad.php" class="btn btn-outline-light btn-lg">
                                    <i class="fas fa-plus"></i> أضف إعلان
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- البحث السريع -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <h4 class="text-center mb-4">البحث السريع</h4>
                        <form action="search.php" method="GET" class="row g-3">
                            <div class="col-md-4">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="search" name="q" placeholder="ابحث عن...">
                                    <label for="search">ابحث عن إعلان</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="city" name="city" placeholder="المدينة" list="cities">
                                    <label for="city">المدينة</label>
                                    <datalist id="cities">
                                        <option value="الرياض">
                                        <option value="جدة">
                                        <option value="الدمام">
                                        <option value="مكة المكرمة">
                                        <option value="المدينة المنورة">
                                        <option value="الطائف">
                                        <option value="الخبر">
                                        <option value="الظهران">
                                        <option value="القطيف">
                                        <option value="الأحساء">
                                        <option value="أبها">
                                        <option value="خميس مشيط">
                                        <option value="تبوك">
                                        <option value="بريدة">
                                        <option value="الجبيل">
                                    </datalist>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary h-100 w-100">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- الفئات الرئيسية -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-center mb-4">الفئات الرئيسية</h2>
                <div class="row">
                    <?php
                    // جلب الفئات الرئيسية من قاعدة البيانات
                    $main_categories = $db->fetchAll("
                        SELECT c.*, COUNT(a.id) as ads_count
                        FROM categories c
                        LEFT JOIN ads a ON c.id = a.category_id AND a.status = 'active'
                        WHERE c.parent_id IS NULL AND c.is_active = 1
                        GROUP BY c.id
                        ORDER BY c.sort_order, c.name
                        LIMIT 6
                    ");

                    $colors = ['primary', 'success', 'info', 'warning', 'danger', 'dark'];

                    foreach ($main_categories as $index => $category):
                        $color = $colors[$index % count($colors)];
                    ?>
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="category-ads.php?category=<?= $category['id'] ?>" class="text-decoration-none">
                                <div class="card text-center h-100 border-<?= $color ?>">
                                    <div class="card-body">
                                        <?php if ($category['image_path']): ?>
                                            <img src="../uploads/<?= $category['image_path'] ?>"
                                                 class="img-fluid mb-2"
                                                 style="width: 50px; height: 50px; object-fit: cover;">
                                        <?php else: ?>
                                            <i class="<?= $category['icon'] ?: 'fas fa-tag' ?> fa-2x text-<?= $color ?> mb-2"></i>
                                        <?php endif; ?>
                                        <h6 class="card-title"><?= $category['name'] ?></h6>
                                        <small class="text-muted"><?= $category['ads_count'] ?> إعلان</small>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- إحصائيات حقيقية -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-center mb-4">إحصائيات الموقع</h2>
                <div class="row">
                    <?php
                    // جلب الإحصائيات الحقيقية
                    $stats = [
                        'users' => $db->fetch("SELECT COUNT(*) as count FROM users WHERE user_type = 'user'")['count'] ?? 0,
                        'ads' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE status = 'active'")['count'] ?? 0,
                        'views' => $db->fetch("SELECT SUM(views_count) as total FROM ads")['total'] ?? 0,
                        'categories' => $db->fetch("SELECT COUNT(*) as count FROM categories WHERE is_active = 1")['count'] ?? 0
                    ];
                    ?>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3><?= number_format($stats['users']) ?>+</h3>
                                <p>مستخدم مسجل</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-success text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-bullhorn fa-2x mb-2"></i>
                                <h3><?= number_format($stats['ads']) ?>+</h3>
                                <p>إعلان نشط</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-info text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-eye fa-2x mb-2"></i>
                                <h3><?= number_format($stats['views']) ?>+</h3>
                                <p>مشاهدة إجمالية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-warning text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-th-large fa-2x mb-2"></i>
                                <h3><?= number_format($stats['categories']) ?>+</h3>
                                <p>فئة متنوعة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أحدث الإعلانات -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-center mb-4">أحدث الإعلانات</h2>
                <div class="row">
                    <?php
                    // جلب أحدث الإعلانات
                    $latest_ads = $db->fetchAll("
                        SELECT a.*, u.full_name, c.name as category_name,
                               (SELECT image_path FROM ad_images WHERE ad_id = a.id AND is_primary = 1 LIMIT 1) as primary_image
                        FROM ads a
                        JOIN users u ON a.user_id = u.id
                        JOIN categories c ON a.category_id = c.id
                        WHERE a.status = 'active'
                        ORDER BY a.created_at DESC
                        LIMIT 8
                    ");

                    if (!empty($latest_ads)):
                        foreach ($latest_ads as $ad):
                    ?>
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="position-relative">
                                    <?php if ($ad['primary_image']): ?>
                                        <img src="../uploads/<?= $ad['primary_image'] ?>"
                                             class="card-img-top"
                                             style="height: 200px; object-fit: cover;"
                                             alt="<?= $ad['title'] ?>">
                                    <?php else: ?>
                                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                                             style="height: 200px;">
                                            <i class="fas fa-image text-muted fa-3x"></i>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($ad['is_featured']): ?>
                                        <div class="position-absolute top-0 start-0 m-2">
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-star"></i> مميز
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title"><?= substr($ad['title'], 0, 40) ?><?= strlen($ad['title']) > 40 ? '...' : '' ?></h6>
                                    <div class="text-success fw-bold mb-2"><?= formatPrice($ad['price']) ?></div>
                                    <div class="text-muted small mb-2">
                                        <i class="fas fa-tag"></i> <?= $ad['category_name'] ?>
                                    </div>
                                    <div class="text-muted small mb-2">
                                        <i class="fas fa-map-marker-alt"></i> <?= $ad['city'] ?>
                                    </div>
                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between text-muted small">
                                            <span><i class="fas fa-eye"></i> <?= $ad['views_count'] ?></span>
                                            <span><?= timeAgo($ad['created_at']) ?></span>
                                        </div>
                                        <a href="ad-details.php?id=<?= $ad['id'] ?>" class="btn btn-primary btn-sm w-100 mt-2">
                                            عرض التفاصيل
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php
                        endforeach;
                    else:
                    ?>
                        <div class="col-12 text-center">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> لا توجد إعلانات متاحة حالياً
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <?php if (!empty($latest_ads)): ?>
                <div class="text-center mt-4">
                    <a href="categories.php" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-th-large"></i> عرض جميع الإعلانات
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- دعوة للعمل -->
        <div class="row">
            <div class="col-12">
                <div class="card bg-light">
                    <div class="card-body text-center py-5">
                        <h3>ابدأ رحلتك في البيع والشراء اليوم</h3>
                        <p class="lead text-muted">انضم إلى آلاف المستخدمين الذين يثقون بحراجنا</p>
                        <div class="mt-4">
                            <?php if (!isset($_SESSION['user_id'])): ?>
                                <a href="register.php" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-user-plus"></i> إنشاء حساب مجاني
                                </a>
                                <a href="login.php" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                                </a>
                            <?php else: ?>
                                <a href="add-ad.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-plus"></i> أضف إعلانك الأول
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/car-brands.js"></script>

<?php include 'includes/footer.php'; ?>
