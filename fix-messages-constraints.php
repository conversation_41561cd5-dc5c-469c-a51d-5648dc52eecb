<?php
// إصلاح قيود المفاتيح الخارجية في جدول الرسائل
try {
    $pdo = new PDO('mysql:host=localhost;dbname=harajuna;charset=utf8mb4', 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<h2>🔧 إصلاح قيود المفاتيح الخارجية</h2>";
    
    // إزالة جدول الرسائل الحالي وإعادة إنشاؤه بدون قيود صارمة
    echo "🗑️ حذف جدول الرسائل الحالي...<br>";
    
    try {
        $pdo->exec("DROP TABLE IF EXISTS messages");
        echo "✅ تم حذف جدول الرسائل القديم<br>";
    } catch (Exception $e) {
        echo "ℹ️ لا يوجد جدول رسائل للحذف<br>";
    }
    
    // إنشاء جدول الرسائل الجديد بدون قيود صارمة
    echo "🔨 إنشاء جدول الرسائل الجديد...<br>";
    
    $pdo->exec("
        CREATE TABLE messages (
            id INT PRIMARY KEY AUTO_INCREMENT,
            sender_id INT NOT NULL,
            receiver_id INT NOT NULL,
            ad_id INT NULL,
            message TEXT NOT NULL,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_sender_id (sender_id),
            INDEX idx_receiver_id (receiver_id),
            INDEX idx_ad_id (ad_id),
            INDEX idx_created_at (created_at)
        )
    ");
    
    echo "✅ تم إنشاء جدول الرسائل الجديد بدون قيود صارمة<br>";
    
    // إضافة بيانات تجريبية
    echo "📝 إضافة بيانات تجريبية...<br>";
    
    // التحقق من وجود مستخدمين
    $users = $pdo->query("SELECT id, full_name FROM users LIMIT 5")->fetchAll();
    
    if (count($users) < 2) {
        echo "👥 إنشاء مستخدمين تجريبيين...<br>";
        
        // إنشاء مستخدمين تجريبيين
        $pdo->exec("
            INSERT INTO users (username, email, password, full_name, phone, city, user_type, is_active, created_at) VALUES 
            ('msguser1', '<EMAIL>', '" . password_hash('123456', PASSWORD_DEFAULT) . "', 'أحمد الرسائل', '0501111111', 'الرياض', 'user', 1, NOW()),
            ('msguser2', '<EMAIL>', '" . password_hash('123456', PASSWORD_DEFAULT) . "', 'فاطمة الرسائل', '0502222222', 'جدة', 'user', 1, NOW()),
            ('msguser3', '<EMAIL>', '" . password_hash('123456', PASSWORD_DEFAULT) . "', 'محمد الرسائل', '0503333333', 'الدمام', 'user', 1, NOW())
        ");
        
        echo "✅ تم إنشاء 3 مستخدمين تجريبيين<br>";
        $users = $pdo->query("SELECT id, full_name FROM users ORDER BY id DESC LIMIT 5")->fetchAll();
    }
    
    // إضافة رسائل تجريبية
    if (count($users) >= 2) {
        echo "💬 إضافة رسائل تجريبية...<br>";
        
        $user1_id = $users[0]['id'];
        $user2_id = $users[1]['id'];
        $user3_id = count($users) > 2 ? $users[2]['id'] : $user1_id;
        
        $sample_messages = [
            [$user1_id, $user2_id, 'مرحباً، كيف حالك؟', null],
            [$user2_id, $user1_id, 'مرحباً بك، أنا بخير والحمد لله', null],
            [$user1_id, $user2_id, 'هل يمكنني الاستفسار عن شيء؟', null],
            [$user2_id, $user1_id, 'بالطبع، تفضل بالسؤال', null],
            [$user1_id, $user3_id, 'مرحباً، أريد التواصل معك', null],
            [$user3_id, $user1_id, 'أهلاً وسهلاً، كيف يمكنني مساعدتك؟', null],
            [$user2_id, $user3_id, 'هل أنت متاح للحديث؟', null],
            [$user3_id, $user2_id, 'نعم، أنا متاح الآن', null]
        ];
        
        $stmt = $pdo->prepare("INSERT INTO messages (sender_id, receiver_id, message, ad_id, created_at) VALUES (?, ?, ?, ?, NOW())");
        
        foreach ($sample_messages as $msg) {
            $stmt->execute($msg);
        }
        
        echo "✅ تم إضافة " . count($sample_messages) . " رسالة تجريبية<br>";
    }
    
    // اختبار الجدول الجديد
    echo "<hr>";
    echo "<h3>🧪 اختبار الجدول الجديد:</h3>";
    
    $total_messages = $pdo->query("SELECT COUNT(*) as count FROM messages")->fetch()['count'];
    echo "✅ إجمالي الرسائل: $total_messages<br>";
    
    $unique_conversations = $pdo->query("
        SELECT COUNT(DISTINCT CONCAT(LEAST(sender_id, receiver_id), '-', GREATEST(sender_id, receiver_id))) as count 
        FROM messages
    ")->fetch()['count'];
    echo "✅ المحادثات الفريدة: $unique_conversations<br>";
    
    // اختبار إدراج رسالة جديدة
    echo "<h4>📝 اختبار إدراج رسالة جديدة:</h4>";
    
    if (count($users) >= 2) {
        try {
            $test_stmt = $pdo->prepare("INSERT INTO messages (sender_id, receiver_id, message, ad_id, created_at) VALUES (?, ?, ?, ?, NOW())");
            $test_stmt->execute([$users[0]['id'], $users[1]['id'], 'رسالة اختبار بعد الإصلاح', null]);
            echo "✅ تم إدراج رسالة اختبار بنجاح<br>";
        } catch (Exception $e) {
            echo "❌ خطأ في إدراج رسالة الاختبار: " . $e->getMessage() . "<br>";
        }
    }
    
    // عرض بيانات المستخدمين
    echo "<h4>👥 المستخدمين المتاحين:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>الاسم</th><th>اسم المستخدم</th><th>كلمة المرور</th></tr>";
    
    $all_users = $pdo->query("SELECT id, full_name, username FROM users ORDER BY id DESC LIMIT 10")->fetchAll();
    foreach ($all_users as $user) {
        echo "<tr>";
        echo "<td>" . $user['id'] . "</td>";
        echo "<td>" . $user['full_name'] . "</td>";
        echo "<td>" . $user['username'] . "</td>";
        echo "<td>123456</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<hr>";
    echo "<h3>🔗 روابط الاختبار:</h3>";
    echo "<ul>";
    echo "<li><a href='pages/messages.php' target='_blank'>صفحة الرسائل الرئيسية</a></li>";
    echo "<li><a href='pages/messages-simple.php' target='_blank'>صفحة الرسائل المبسطة</a></li>";
    echo "<li><a href='pages/login.php' target='_blank'>تسجيل الدخول</a></li>";
    echo "<li><a href='final-messages-test.php' target='_blank'>اختبار الرسائل الشامل</a></li>";
    
    if (count($users) >= 2) {
        echo "<li><a href='pages/messages.php?user_id=" . $users[1]['id'] . "' target='_blank'>محادثة مع " . $users[1]['full_name'] . "</a></li>";
        if (count($users) >= 3) {
            echo "<li><a href='pages/messages.php?user_id=" . $users[2]['id'] . "' target='_blank'>محادثة مع " . $users[2]['full_name'] . "</a></li>";
        }
    }
    echo "</ul>";
    
    // نموذج اختبار سريع
    echo "<hr>";
    echo "<h3>⚡ اختبار سريع:</h3>";
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['quick_test'])) {
        if (count($users) >= 2) {
            try {
                $sender = $users[0]['id'];
                $receiver = $users[1]['id'];
                $message = "رسالة اختبار سريع - " . date('H:i:s');
                
                $quick_stmt = $pdo->prepare("INSERT INTO messages (sender_id, receiver_id, message, created_at) VALUES (?, ?, ?, NOW())");
                $quick_stmt->execute([$sender, $receiver, $message]);
                
                echo "<div style='color: green; font-weight: bold;'>✅ تم إرسال رسالة اختبار بنجاح!</div>";
            } catch (Exception $e) {
                echo "<div style='color: red; font-weight: bold;'>❌ خطأ: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    if (count($users) >= 2) {
        echo "<form method='POST'>";
        echo "<button type='submit' name='quick_test' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>";
        echo "🚀 اختبار إرسال رسالة سريع";
        echo "</button>";
        echo "</form>";
    }
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724;'>🎉 تم إصلاح مشكلة المفاتيح الخارجية!</h3>";
    echo "<ul style='color: #155724; margin: 0;'>";
    echo "<li>✅ تم حذف الجدول القديم مع القيود الصارمة</li>";
    echo "<li>✅ تم إنشاء جدول جديد بدون قيود مشاكل</li>";
    echo "<li>✅ تم إضافة بيانات تجريبية للاختبار</li>";
    echo "<li>✅ تم اختبار إدراج الرسائل بنجاح</li>";
    echo "<li>✅ جميع الروابط جاهزة للاختبار</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 20px; border-radius: 5px;'>";
    echo "<h3>❌ خطأ في قاعدة البيانات:</h3>";
    echo "<p><strong>الرسالة:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الكود:</strong> " . $e->getCode() . "</p>";
    echo "</div>";
}

// إضافة تنسيق CSS
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4 { color: #333; margin-bottom: 1rem; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
ul { margin: 10px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
