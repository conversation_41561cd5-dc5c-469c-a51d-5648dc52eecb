<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// إنشاء كائن المصادقة
$auth = new Auth($db);

// التحقق من تسجيل الدخول
requireLogin();

$page_title = 'الرسائل - حراجنا';
$page_description = 'إدارة رسائلك ومحادثاتك على موقع حراجنا';

$error = '';
$success = '';

// معالجة إرسال رسالة جديدة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_message'])) {
    try {
        $receiver_id = (int)($_POST['receiver_id'] ?? 0);
        $message = sanitize($_POST['message'] ?? '');

        if (!empty($message) && $receiver_id > 0) {
            $db->query("INSERT INTO messages (sender_id, receiver_id, ad_id, message, created_at) VALUES (?, ?, ?, ?, NOW())",
                      [$_SESSION['user_id'], $receiver_id, null, $message]);
            $success = 'تم إرسال الرسالة بنجاح';

            // إعادة توجيه لتجنب إعادة الإرسال
            header("Location: messages.php?user_id=$receiver_id");
            exit();
        } else {
            $error = 'يجب كتابة نص الرسالة';
        }
    } catch (Exception $e) {
        $error = 'خطأ في إرسال الرسالة: ' . $e->getMessage();
    }
}

// جلب المحادثات - منطق مبسط
$conversations = [];

// جلب جميع المستخدمين الذين تم التراسل معهم
$users_sql = "
    SELECT DISTINCT
        CASE
            WHEN sender_id = ? THEN receiver_id
            ELSE sender_id
        END as other_user_id
    FROM messages
    WHERE sender_id = ? OR receiver_id = ?
";

try {
    $users_result = $db->fetchAll($users_sql, [$_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id']]);

    foreach ($users_result as $user_row) {
        $other_user_id = $user_row['other_user_id'];

        // جلب معلومات المستخدم
        $user = $db->fetch("SELECT id, full_name, username FROM users WHERE id = ?", [$other_user_id]);

        if ($user) {
            // جلب آخر رسالة
            $last_message = $db->fetch("
                SELECT message, created_at, sender_id
                FROM messages
                WHERE (sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)
                ORDER BY created_at DESC
                LIMIT 1
            ", [$_SESSION['user_id'], $other_user_id, $other_user_id, $_SESSION['user_id']]);

            // عدد الرسائل غير المقروءة
            $unread_count = $db->fetch("
                SELECT COUNT(*) as count
                FROM messages
                WHERE sender_id = ? AND receiver_id = ? AND is_read = 0
            ", [$other_user_id, $_SESSION['user_id']])['count'];

            $conversations[] = [
                'other_user_id' => $other_user_id,
                'full_name' => $user['full_name'],
                'username' => $user['username'],
                'last_message' => $last_message['message'] ?? '',
                'last_message_time' => $last_message['created_at'] ?? '',
                'unread_count' => $unread_count
            ];
        }
    }
} catch (Exception $e) {
    $error = 'خطأ في جلب المحادثات: ' . $e->getMessage();
}

// جلب الرسائل للمحادثة المحددة
$selected_user_id = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;
$messages = [];
$selected_user = null;

if ($selected_user_id > 0) {
    try {
        // جلب معلومات المستخدم المحدد
        $selected_user = $db->fetch("SELECT id, full_name, username FROM users WHERE id = ?", [$selected_user_id]);

        if ($selected_user) {
            // جلب الرسائل بشكل مبسط
            $messages = $db->fetchAll("
                SELECT m.*,
                       CASE WHEN m.sender_id = ? THEN 'sent' ELSE 'received' END as message_type
                FROM messages m
                WHERE (m.sender_id = ? AND m.receiver_id = ?) OR (m.sender_id = ? AND m.receiver_id = ?)
                ORDER BY m.created_at ASC
            ", [$_SESSION['user_id'], $_SESSION['user_id'], $selected_user_id, $selected_user_id, $_SESSION['user_id']]);

            // تحديد الرسائل كمقروءة
            $db->query("UPDATE messages SET is_read = 1 WHERE sender_id = ? AND receiver_id = ?",
                       [$selected_user_id, $_SESSION['user_id']]);
        }
    } catch (Exception $e) {
        $error = 'خطأ في جلب الرسائل: ' . $e->getMessage();
    }
}

include 'includes/header.php';
?>

<div class="container mt-4">
    <!-- عرض الرسائل -->
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle"></i> <?= $error ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?= $success ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-12">
            <h2><i class="fas fa-envelope"></i> الرسائل</h2>
        </div>
    </div>
    
    <div class="row">
        <!-- قائمة المحادثات -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">المحادثات</h5>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($conversations)): ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($conversations as $conversation): ?>
                                <a href="?user_id=<?= $conversation['other_user_id'] ?>" 
                                   class="list-group-item list-group-item-action <?= $selected_user_id == $conversation['other_user_id'] ? 'active' : '' ?>">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">
                                            <?= $conversation['full_name'] ?>
                                            <?php if ($conversation['unread_count'] > 0): ?>
                                                <span class="badge bg-danger"><?= $conversation['unread_count'] ?></span>
                                            <?php endif; ?>
                                        </h6>
                                        <small><?= timeAgo($conversation['last_message_time']) ?></small>
                                    </div>
                                    <p class="mb-1 text-truncate"><?= substr($conversation['last_message'], 0, 50) ?>...</p>
                                    <small>@<?= $conversation['username'] ?></small>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد محادثات بعد</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- منطقة الرسائل -->
        <div class="col-md-8">
            <?php if ($selected_user): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user"></i> <?= $selected_user['full_name'] ?>
                            <small class="text-muted">(@<?= $selected_user['username'] ?>)</small>
                        </h5>
                    </div>
                    <div class="card-body" style="height: 400px; overflow-y: auto;" id="messagesContainer">
                        <?php if (!empty($messages)): ?>
                            <?php foreach ($messages as $message): ?>
                                <div class="mb-3 <?= $message['sender_id'] == $_SESSION['user_id'] ? 'text-end' : '' ?>">
                                    <div class="d-inline-block p-2 rounded <?= $message['sender_id'] == $_SESSION['user_id'] ? 'bg-primary text-white' : 'bg-light' ?>" 
                                         style="max-width: 70%;">
                                        <?php if ($message['ad_title']): ?>
                                            <div class="small mb-1">
                                                <i class="fas fa-bullhorn"></i> بخصوص: <?= $message['ad_title'] ?>
                                            </div>
                                        <?php endif; ?>
                                        <div><?= nl2br(htmlspecialchars($message['message'])) ?></div>
                                        <div class="small mt-1 opacity-75">
                                            <?= date('H:i', strtotime($message['created_at'])) ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center text-muted">
                                <i class="fas fa-comments fa-3x mb-3"></i>
                                <p>ابدأ محادثة جديدة</p>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer">
                        <form method="POST">
                            <input type="hidden" name="receiver_id" value="<?= $selected_user['id'] ?>">
                            <div class="input-group">
                                <textarea class="form-control" name="message" placeholder="اكتب رسالتك..." 
                                          rows="2" required></textarea>
                                <button type="submit" name="send_message" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i> إرسال
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-comments fa-4x text-muted mb-3"></i>
                        <h4>اختر محادثة لعرضها</h4>
                        <p class="text-muted">اختر محادثة من القائمة الجانبية لبدء المراسلة</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// التمرير التلقائي لأسفل منطقة الرسائل
document.addEventListener('DOMContentLoaded', function() {
    const messagesContainer = document.getElementById('messagesContainer');
    if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
});
</script>

<?php include 'includes/footer.php'; ?>
