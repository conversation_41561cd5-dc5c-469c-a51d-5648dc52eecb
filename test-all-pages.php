<?php
// اختبار جميع صفحات الموقع

$pages_to_test = [
    'الصفحات العامة' => [
        'pages/home.php' => 'الصفحة الرئيسية',
        'pages/login.php' => 'تسجيل الدخول',
        'pages/register.php' => 'إنشاء حساب',
        'pages/categories.php' => 'الفئات',
        'pages/about.php' => 'من نحن',
        'pages/contact.php' => 'اتصل بنا',
    ],
    'صفحات المستخدمين' => [
        'pages/add-ad.php' => 'إضافة إعلان',
        'pages/my-ads.php' => 'إعلاناتي',
        'pages/profile.php' => 'الملف الشخصي',
        'pages/favorites.php' => 'المفضلة',
    ],
    'لوحة الإدارة' => [
        'admin/index.php' => 'الصفحة الرئيسية',
        'admin/ads.php' => 'إدارة الإعلانات',
        'admin/users.php' => 'إدارة المستخدمين',
        'admin/categories.php' => 'إدارة الفئات',
    ]
];

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار جميع صفحات الموقع</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-4'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-check-circle text-success'></i> اختبار جميع صفحات الموقع</h1>";

function testPage($url) {
    $full_url = "http://localhost:8081/" . $url;
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'ignore_errors' => true
        ]
    ]);
    
    $headers = @get_headers($full_url, 1, $context);
    
    if ($headers && strpos($headers[0], '200') !== false) {
        return ['status' => 'success', 'message' => 'يعمل بشكل صحيح'];
    } elseif ($headers && strpos($headers[0], '500') !== false) {
        return ['status' => 'error', 'message' => 'خطأ في الخادم (500)'];
    } elseif ($headers && strpos($headers[0], '404') !== false) {
        return ['status' => 'warning', 'message' => 'الصفحة غير موجودة (404)'];
    } else {
        return ['status' => 'error', 'message' => 'لا يمكن الوصول للصفحة'];
    }
}

foreach ($pages_to_test as $section => $pages) {
    echo "<div class='card mb-4'>";
    echo "<div class='card-header'>";
    echo "<h3 class='mb-0'><i class='fas fa-folder'></i> $section</h3>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<div class='row'>";
    
    foreach ($pages as $url => $title) {
        $result = testPage($url);
        
        $badge_class = '';
        $icon = '';
        
        switch ($result['status']) {
            case 'success':
                $badge_class = 'bg-success';
                $icon = 'fas fa-check-circle';
                break;
            case 'warning':
                $badge_class = 'bg-warning';
                $icon = 'fas fa-exclamation-triangle';
                break;
            case 'error':
                $badge_class = 'bg-danger';
                $icon = 'fas fa-times-circle';
                break;
        }
        
        echo "<div class='col-md-6 mb-3'>";
        echo "<div class='card h-100'>";
        echo "<div class='card-body'>";
        echo "<h5 class='card-title'>$title</h5>";
        echo "<p class='card-text'>";
        echo "<span class='badge $badge_class'>";
        echo "<i class='$icon'></i> " . $result['message'];
        echo "</span>";
        echo "</p>";
        echo "<div class='d-grid gap-2'>";
        echo "<a href='$url' class='btn btn-primary btn-sm' target='_blank'>";
        echo "<i class='fas fa-external-link-alt'></i> فتح الصفحة";
        echo "</a>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
    echo "</div>";
}

// إحصائيات سريعة
$total_pages = 0;
$working_pages = 0;

foreach ($pages_to_test as $pages) {
    foreach ($pages as $url => $title) {
        $total_pages++;
        $result = testPage($url);
        if ($result['status'] === 'success') {
            $working_pages++;
        }
    }
}

echo "<div class='alert alert-info'>";
echo "<h4><i class='fas fa-chart-bar'></i> الإحصائيات</h4>";
echo "<p><strong>إجمالي الصفحات:</strong> $total_pages</p>";
echo "<p><strong>الصفحات التي تعمل:</strong> $working_pages</p>";
echo "<p><strong>معدل النجاح:</strong> " . round(($working_pages / $total_pages) * 100, 1) . "%</p>";
echo "</div>";

echo "<div class='text-center mt-4'>";
echo "<a href='/' class='btn btn-primary btn-lg'>";
echo "<i class='fas fa-home'></i> العودة للصفحة الرئيسية";
echo "</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
