<?php
// اختبار قاعدة البيانات SQLite
echo "<h1>🧪 اختبار قاعدة البيانات SQLite</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

try {
    echo "<p>🔄 محاولة الاتصال بقاعدة البيانات...</p>";

    // اختبار مباشر لـ SQLite
    $dbfile = 'database/harajuna.db';
    $dbDir = dirname($dbfile);

    echo "<p>📁 مجلد قاعدة البيانات: $dbDir</p>";

    if (!is_dir($dbDir)) {
        mkdir($dbDir, 0755, true);
        echo "<p class='info'>✅ تم إنشاء مجلد قاعدة البيانات</p>";
    }

    $dsn = "sqlite:$dbfile";
    $pdo = new PDO($dsn);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<p class='success'>✅ تم الاتصال بـ SQLite بنجاح!</p>";

    // إنشاء جدول بسيط للاختبار
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS test_users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) NOT NULL,
            email VARCHAR(100) NOT NULL,
            user_type VARCHAR(10) DEFAULT 'user',
            is_active INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");

    echo "<p class='success'>✅ تم إنشاء جدول الاختبار</p>";

    // إدراج بيانات تجريبية
    $pdo->exec("INSERT OR IGNORE INTO test_users (username, email, user_type) VALUES ('admin', '<EMAIL>', 'admin')");
    $pdo->exec("INSERT OR IGNORE INTO test_users (username, email, user_type) VALUES ('user1', '<EMAIL>', 'user')");

    echo "<p class='success'>✅ تم إدراج البيانات التجريبية</p>";

    // استعلام البيانات
    $stmt = $pdo->query("SELECT * FROM test_users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<h3>👥 المستخدمون في قاعدة البيانات:</h3>";
    echo "<pre>" . print_r($users, true) . "</pre>";

    echo "<div class='info' style='margin-top: 20px; padding: 15px; border: 1px solid #007bff; border-radius: 5px;'>
        <h3>🎉 SQLite يعمل بشكل مثالي!</h3>
        <p>✅ تم إنشاء قاعدة البيانات بنجاح</p>
        <p>✅ تم إنشاء الجداول بنجاح</p>
        <p>✅ تم إدراج واستعلام البيانات بنجاح</p>
        <br>
        <a href='pages/home.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 الذهاب للصفحة الرئيسية</a>
    </div>";

} catch (Exception $e) {
    echo "<div class='error' style='padding: 15px; border: 1px solid red; border-radius: 5px;'>
        <h3>❌ خطأ في قاعدة البيانات:</h3>
        <p><strong>الرسالة:</strong> " . $e->getMessage() . "</p>
        <p><strong>الملف:</strong> " . $e->getFile() . "</p>
        <p><strong>السطر:</strong> " . $e->getLine() . "</p>
    </div>";
}
?>
