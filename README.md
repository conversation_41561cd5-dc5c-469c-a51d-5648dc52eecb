# 🏪 موقع حراجنا - نظام الإعلانات المبوبة

موقع حراجنا هو نظام إعلانات مبوبة متكامل مشابه لموقع حراج، مطور بـ PHP مع تصميم حصري وواجهة مستخدم متقدمة.

## ✨ المميزات

### 🎯 المميزات الأساسية
- **نظام إعلانات متكامل** - إضافة وتعديل وحذف الإعلانات
- **إدارة المستخدمين** - تسجيل وتسجيل دخول وملفات شخصية
- **نظام فئات متقدم** - فئات رئيسية وفرعية
- **بحث وفلترة قوية** - بحث بالعنوان والوصف والموقع والسعر
- **رفع الصور** - دعم رفع متعدد الصور مع معاينة
- **نظام رسائل** - تواصل بين المستخدمين
- **المفضلة** - حفظ الإعلانات المفضلة
- **التقييمات** - تقييم المستخدمين والبائعين

### 🛡️ الأمان والحماية
- **تشفير كلمات المرور** - باستخدام password_hash
- **حماية CSRF** - رموز أمان للنماذج
- **تنظيف البيانات** - sanitization لجميع المدخلات
- **جلسات آمنة** - إدارة متقدمة للجلسات
- **رفع آمن للملفات** - فحص أنواع وأحجام الملفات

### 🎨 التصميم والواجهة
- **تصميم حصري** - واجهة مستخدم فريدة ومتميزة
- **متجاوب بالكامل** - يعمل على جميع الأجهزة
- **تأثيرات متقدمة** - رسوم متحركة وتأثيرات CSS3
- **ألوان متدرجة** - نظام ألوان عصري وجذاب
- **خطوط عربية** - دعم كامل للغة العربية

### 🔧 لوحة التحكم
- **إحصائيات شاملة** - تقارير مفصلة عن النشاط
- **إدارة الإعلانات** - مراجعة وموافقة الإعلانات
- **إدارة المستخدمين** - تفعيل وإلغاء تفعيل الحسابات
- **إدارة الفئات** - إضافة وتعديل الفئات
- **إدارة التقارير** - مراجعة الشكاوى والتقارير
- **إعدادات الموقع** - تخصيص إعدادات النظام

## 🚀 التثبيت والتشغيل

### المتطلبات
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx (اختياري للتطوير)
- مساحة تخزين للصور

### خطوات التثبيت

#### 1. تحميل الملفات
```bash
git clone https://github.com/your-repo/harajuna.git
cd harajuna
```

#### 2. تشغيل الخادم المحلي
```bash
# على Windows
start-server.bat

# على Linux/Mac
php server.php
```

#### 3. زيارة صفحة التثبيت
افتح المتصفح وانتقل إلى:
```
http://localhost:8081/install/install.php
```

#### 4. اتباع معالج التثبيت
1. **التحقق من المتطلبات** - فحص PHP والإضافات المطلوبة
2. **إعداد قاعدة البيانات** - إدخال بيانات الاتصال
3. **إنشاء حساب المدير** - إعداد حساب الإدارة
4. **اكتمال التثبيت** - بدء استخدام الموقع

## 🔗 الروابط المهمة

بعد التثبيت، يمكنك الوصول إلى:

- **الصفحة الرئيسية**: http://localhost:8081/pages/home.php
- **لوحة التحكم**: http://localhost:8081/admin/index.php
- **تسجيل الدخول**: http://localhost:8081/pages/login.php
- **إنشاء حساب**: http://localhost:8081/pages/register.php
- **إضافة إعلان**: http://localhost:8081/pages/add-ad.php

## 📊 قاعدة البيانات

### الجداول الرئيسية
- `users` - بيانات المستخدمين
- `ads` - الإعلانات
- `categories` - الفئات
- `ad_images` - صور الإعلانات
- `messages` - الرسائل
- `favorites` - المفضلة
- `ratings` - التقييمات
- `reports` - التقارير
- `notifications` - الإشعارات
- `settings` - إعدادات الموقع

### بيانات الاختبار
يتم إنشاء بيانات أولية تشمل:
- حساب مدير افتراضي (admin/password)
- فئات رئيسية وفرعية
- إعدادات أساسية للموقع

## 🛠️ التطوير والتخصيص

### هيكل المشروع
```
harajuna/
├── admin/              # لوحة التحكم
├── assets/             # الملفات الثابتة (CSS, JS, Images)
├── config/             # ملفات التكوين
├── includes/           # الملفات المشتركة
├── install/            # معالج التثبيت
├── pages/              # صفحات الموقع
├── uploads/            # الملفات المرفوعة
└── logs/               # ملفات السجلات
```

### إضافة مميزات جديدة
1. إنشاء الجداول المطلوبة في `install/database.sql`
2. إضافة الدوال في `includes/functions.php`
3. إنشاء الصفحات في `pages/` أو `admin/`
4. تحديث التصميم في `assets/css/style.css`

### تخصيص التصميم
- الألوان الأساسية في `:root` في ملف CSS
- الخطوط في Google Fonts
- الأيقونات من Font Awesome
- الصور في مجلد `assets/images/`

## 🔧 الإعدادات

### إعدادات قاعدة البيانات
في `config/database.php`:
```php
private $host = 'srv1513.hstgr.io';
private $dbname = 'u302460181_h';
private $username = 'u302460181_h';
private $password = '10743211uU@';
```

### إعدادات الموقع
في `config/config.php`:
- اسم الموقع ووصفه
- إعدادات رفع الملفات
- المناطق والمدن
- الفئات الرئيسية

## 🐛 استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في الاتصال بقاعدة البيانات**
   - تحقق من بيانات الاتصال في `config/database.php`
   - تأكد من تشغيل MySQL

2. **مشكلة في رفع الصور**
   - تحقق من صلاحيات مجلد `uploads/`
   - تأكد من إعدادات PHP للرفع

3. **مشكلة في الجلسات**
   - تحقق من إعدادات session في PHP
   - امسح ملفات الجلسات المؤقتة

### ملفات السجلات
- `logs/server.log` - سجل الخادم
- `logs/error.log` - سجل الأخطاء
- `logs/activity.log` - سجل النشاطات

## 📱 التوافق

### المتصفحات المدعومة
- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+

### الأجهزة
- أجهزة سطح المكتب
- الأجهزة اللوحية
- الهواتف الذكية

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

للحصول على الدعم:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني
- مراجعة الوثائق

## 🎉 شكر خاص

شكر خاص لجميع المساهمين والمطورين الذين ساعدوا في تطوير هذا المشروع.

---

**حراجنا** - موقع الإعلانات المبوبة الأول في المملكة العربية السعودية 🇸🇦
