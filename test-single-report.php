<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// تسجيل دخول تلقائي للاختبار
if (!isLoggedIn()) {
    $user = $db->fetch("SELECT * FROM users LIMIT 1");
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['logged_in'] = true;
    }
}

$test_id = $_GET['test_id'] ?? rand(1, 1000);

try {
    if (!isLoggedIn()) {
        throw new Exception("المستخدم غير مسجل الدخول");
    }
    
    // جلب إعلان للاختبار
    $test_ad = $db->fetch("SELECT id FROM ads LIMIT 1");
    if (!$test_ad) {
        throw new Exception("لا توجد إعلانات للاختبار");
    }
    
    $ad_id = $test_ad['id'];
    $user_id = $_SESSION['user_id'];
    $reason = 'test';
    $description = "اختبار بلاغ رقم $test_id - " . date('Y-m-d H:i:s');
    
    // محاولة إدراج البلاغ
    $db->query("INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))",
              [$user_id, $user_id, $ad_id, $reason, $description]);
    
    // التحقق من الإدراج
    $inserted = $db->fetch("SELECT id FROM reports WHERE reason = ? ORDER BY id DESC LIMIT 1", [$description]);
    
    if ($inserted) {
        echo "✅ نجح إدراج البلاغ رقم $test_id (ID: {$inserted['id']})";
        
        // حذف البيانات التجريبية
        $db->query("DELETE FROM reports WHERE id = ?", [$inserted['id']]);
    } else {
        echo "❌ فشل في العثور على البلاغ المُدرج";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في اختبار البلاغ رقم $test_id: " . $e->getMessage();
}
?>
