<?php
// سكريبت لإصلاح مسارات الملفات في جميع صفحات الموقع

$pages_dir = 'pages/';
$admin_dir = 'admin/';

// قائمة الملفات التي تحتاج إصلاح
$files_to_fix = [
    'pages/add-ad.php',
    'pages/ad-details.php',
    'pages/my-ads.php',
    'pages/profile.php',
    'pages/favorites.php',
    'pages/categories.php',
    'pages/about.php',
    'pages/contact.php',
    'pages/edit-ad.php',
    'admin/index.php',
    'admin/ads.php',
    'admin/users.php',
    'admin/categories.php'
];

$old_includes = [
    "require_once '../config/database.php';",
    "require_once '../config/config.php';",
    "require_once '../includes/functions.php';",
    "require_once '../includes/auth.php';"
];

$new_includes_pages = [
    "require_once \$root_path . '/config/database.php';",
    "require_once \$root_path . '/config/config.php';",
    "require_once \$root_path . '/includes/functions.php';",
    "require_once \$root_path . '/includes/auth.php';"
];

$new_includes_admin = [
    "require_once \$root_path . '/config/database.php';",
    "require_once \$root_path . '/config/config.php';",
    "require_once \$root_path . '/includes/functions.php';",
    "require_once \$root_path . '/includes/auth.php';"
];

echo "<h2>🔧 إصلاح مسارات الملفات</h2>";

foreach ($files_to_fix as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $original_content = $content;
        
        // تحديد نوع المجلد
        $is_admin = strpos($file, 'admin/') === 0;
        $root_path_line = $is_admin ? 
            "\$root_path = dirname(__DIR__);" : 
            "\$root_path = dirname(__DIR__);";
        
        // إضافة تعيين مسار الجذر بعد session_start()
        if (strpos($content, '$root_path = dirname(__DIR__);') === false) {
            $content = str_replace(
                "session_start();",
                "session_start();\n\n// تعيين مسار الجذر\n$root_path_line\n",
                $content
            );
        }
        
        // استبدال المسارات القديمة
        foreach ($old_includes as $i => $old_include) {
            $new_include = $is_admin ? $new_includes_admin[$i] : $new_includes_pages[$i];
            $content = str_replace($old_include, $new_include, $content);
        }
        
        // حفظ الملف إذا تم تغييره
        if ($content !== $original_content) {
            file_put_contents($file, $content);
            echo "✅ تم إصلاح: $file<br>";
        } else {
            echo "ℹ️ لا يحتاج إصلاح: $file<br>";
        }
    } else {
        echo "❌ الملف غير موجود: $file<br>";
    }
}

echo "<hr>";
echo "<h3>🎉 تم الانتهاء من إصلاح المسارات!</h3>";
echo "<p>يمكنك الآن اختبار الصفحات:</p>";
echo "<ul>";
echo "<li><a href='pages/home.php' target='_blank'>الصفحة الرئيسية</a></li>";
echo "<li><a href='pages/login.php' target='_blank'>تسجيل الدخول</a></li>";
echo "<li><a href='pages/register.php' target='_blank'>إنشاء حساب</a></li>";
echo "<li><a href='admin/index.php' target='_blank'>لوحة الإدارة</a></li>";
echo "</ul>";
?>
