<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// معاملات البحث
$search_query = isset($_GET['q']) ? sanitize($_GET['q']) : '';
$brand = isset($_GET['brand']) ? sanitize($_GET['brand']) : '';
$category_id = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$city = isset($_GET['city']) ? sanitize($_GET['city']) : '';
$min_price = isset($_GET['min_price']) ? (int)$_GET['min_price'] : 0;
$max_price = isset($_GET['max_price']) ? (int)$_GET['max_price'] : 0;
$sort = isset($_GET['sort']) ? sanitize($_GET['sort']) : 'newest';

// ترقيم الصفحات
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$items_per_page = 20;
$offset = ($page - 1) * $items_per_page;

// بناء استعلام البحث (تضمين الإعلانات المباعة التي لم تخفى بعد)
$where_conditions = ["(a.status = 'active' OR (a.status = 'sold_hidden' AND a.hide_sold_at > NOW()))"];
$params = [];

if (!empty($search_query)) {
    $where_conditions[] = "(a.title LIKE ? OR a.description LIKE ?)";
    $params[] = "%$search_query%";
    $params[] = "%$search_query%";
}

if (!empty($brand)) {
    $where_conditions[] = "(a.title LIKE ? OR a.description LIKE ? OR cb.name LIKE ? OR cb.arabic_name LIKE ?)";
    $params[] = "%$brand%";
    $params[] = "%$brand%";
    $params[] = "%$brand%";
    $params[] = "%$brand%";
}

if ($category_id > 0) {
    $where_conditions[] = "a.category_id = ?";
    $params[] = $category_id;
}

if (!empty($city)) {
    $where_conditions[] = "a.city LIKE ?";
    $params[] = "%$city%";
}

if ($min_price > 0) {
    $where_conditions[] = "a.price >= ?";
    $params[] = $min_price;
}

if ($max_price > 0) {
    $where_conditions[] = "a.price <= ?";
    $params[] = $max_price;
}

$where_clause = implode(' AND ', $where_conditions);

// ترتيب النتائج
$order_clause = match($sort) {
    'oldest' => 'a.created_at ASC',
    'price_low' => 'a.price ASC',
    'price_high' => 'a.price DESC',
    'title' => 'a.title ASC',
    default => 'a.created_at DESC'
};

// جلب النتائج
try {
    $sql = "SELECT a.*, c.name as category_name, u.username, u.full_name, cb.name as brand_name, cb.arabic_name as brand_arabic,
                   (SELECT image_path FROM ad_images WHERE ad_id = a.id ORDER BY is_primary DESC, id ASC LIMIT 1) as main_image
            FROM ads a
            LEFT JOIN categories c ON a.category_id = c.id
            LEFT JOIN users u ON a.user_id = u.id
            LEFT JOIN car_brands cb ON a.car_brand_id = cb.id
            WHERE $where_clause
            ORDER BY a.is_sold ASC, $order_clause
            LIMIT $items_per_page OFFSET $offset";
    
    $ads = $db->fetchAll($sql, $params);
    
    // عدد النتائج الإجمالي
    $count_sql = "SELECT COUNT(*) as total FROM ads a LEFT JOIN car_brands cb ON a.car_brand_id = cb.id WHERE $where_clause";
    $total_results = $db->fetch($count_sql, $params)['total'];
    $total_pages = ceil($total_results / $items_per_page);
    
    // جلب الفئات للفلتر
    $categories = $db->fetchAll("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");
    
    // جلب المدن الشائعة
    $popular_cities = $db->fetchAll("
        SELECT city, COUNT(*) as count 
        FROM ads 
        WHERE status = 'active' AND city IS NOT NULL AND city != '' 
        GROUP BY city 
        ORDER BY count DESC 
        LIMIT 10
    ");
    
} catch (Exception $e) {
    $ads = [];
    $total_results = 0;
    $total_pages = 0;
    $categories = [];
    $popular_cities = [];
}

$page_title = 'البحث في الإعلانات';
if (!empty($brand)) {
    $page_title = "إعلانات $brand";
}
include 'includes/header.php';
?>

<style>
.ad-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.ad-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.ad-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.ad-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.ad-card:hover .ad-image img {
    transform: scale(1.05);
}

.no-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f8f9fa;
    color: #6c757d;
}

.featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.sold-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.ad-card.sold {
    opacity: 0.8;
    position: relative;
}

.ad-card.sold::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(40, 167, 69, 0.1);
    pointer-events: none;
}

.ad-content {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.ad-title {
    margin-bottom: 1rem;
    flex-grow: 1;
}

.ad-title a {
    color: #2c3e50;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.ad-title a:hover {
    color: #667eea;
}

.ad-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 1rem;
}

.ad-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.ad-category {
    font-size: 0.9rem;
    color: #6c757d;
}

.ad-category i {
    margin-left: 5px;
}
</style>

<div class="container mt-4">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3">
                <?php if (!empty($brand)): ?>
                    <i class="fas fa-car me-2"></i>
                    إعلانات <?= htmlspecialchars($brand) ?>
                <?php else: ?>
                    <i class="fas fa-search me-2"></i>
                    البحث في الإعلانات
                <?php endif; ?>
            </h1>
            <?php if ($total_results > 0): ?>
                <p class="text-muted">تم العثور على <?= number_format($total_results) ?> إعلان</p>
            <?php endif; ?>
        </div>
    </div>

    <!-- نموذج البحث المتقدم -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>
                البحث المتقدم
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="q" class="form-label">كلمة البحث</label>
                    <input type="text" class="form-control" id="q" name="q" 
                           value="<?= htmlspecialchars($search_query) ?>" 
                           placeholder="ابحث في العنوان والوصف">
                </div>
                
                <div class="col-md-4">
                    <label for="brand" class="form-label">ماركة السيارة</label>
                    <input type="text" class="form-control" id="brand" name="brand" 
                           value="<?= htmlspecialchars($brand) ?>" 
                           placeholder="مثال: تويوتا، نيسان، هوندا">
                </div>
                
                <div class="col-md-4">
                    <label for="category" class="form-label">الفئة</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">جميع الفئات</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?= $cat['id'] ?>" <?= $category_id == $cat['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($cat['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="city" class="form-label">المدينة</label>
                    <input type="text" class="form-control" id="city" name="city" 
                           value="<?= htmlspecialchars($city) ?>" 
                           placeholder="اختر المدينة" list="cities">
                    <datalist id="cities">
                        <?php foreach ($popular_cities as $popular_city): ?>
                            <option value="<?= htmlspecialchars($popular_city['city']) ?>">
                        <?php endforeach; ?>
                    </datalist>
                </div>
                
                <div class="col-md-3">
                    <label for="min_price" class="form-label">السعر من</label>
                    <input type="number" class="form-control" id="min_price" name="min_price" 
                           value="<?= $min_price > 0 ? $min_price : '' ?>" 
                           placeholder="الحد الأدنى">
                </div>
                
                <div class="col-md-3">
                    <label for="max_price" class="form-label">السعر إلى</label>
                    <input type="number" class="form-control" id="max_price" name="max_price" 
                           value="<?= $max_price > 0 ? $max_price : '' ?>" 
                           placeholder="الحد الأقصى">
                </div>
                
                <div class="col-md-3">
                    <label for="sort" class="form-label">ترتيب النتائج</label>
                    <select class="form-select" id="sort" name="sort">
                        <option value="newest" <?= $sort === 'newest' ? 'selected' : '' ?>>الأحدث</option>
                        <option value="oldest" <?= $sort === 'oldest' ? 'selected' : '' ?>>الأقدم</option>
                        <option value="price_low" <?= $sort === 'price_low' ? 'selected' : '' ?>>السعر: من الأقل للأعلى</option>
                        <option value="price_high" <?= $sort === 'price_high' ? 'selected' : '' ?>>السعر: من الأعلى للأقل</option>
                        <option value="title" <?= $sort === 'title' ? 'selected' : '' ?>>العنوان</option>
                    </select>
                </div>
                
                <div class="col-12">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <a href="search.php" class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-1"></i>
                        إعادة تعيين
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- النتائج -->
    <?php if (empty($ads)): ?>
        <div class="text-center py-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h4>لا توجد نتائج</h4>
            <p class="text-muted">لم يتم العثور على إعلانات تطابق معايير البحث</p>
            <a href="search.php" class="btn btn-primary">
                <i class="fas fa-undo me-1"></i>
                بحث جديد
            </a>
        </div>
    <?php else: ?>
        <div class="row">
            <?php foreach ($ads as $ad): ?>
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="ad-card <?= $ad['is_sold'] ? 'sold' : '' ?>">
                        <div class="ad-image">
                            <?php if ($ad['main_image']): ?>
                                <img src="../<?= $ad['main_image'] ?>" alt="<?= htmlspecialchars($ad['title']) ?>">
                            <?php else: ?>
                                <div class="no-image">
                                    <i class="fas fa-image"></i>
                                    <span>لا توجد صورة</span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($ad['is_featured']): ?>
                                <span class="featured-badge">مميز</span>
                            <?php endif; ?>

                            <?php if ($ad['is_sold']): ?>
                                <span class="sold-badge">مباع</span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="ad-content">
                            <h5 class="ad-title">
                                <a href="ad-details.php?id=<?= $ad['id'] ?>">
                                    <?= htmlspecialchars($ad['title']) ?>
                                </a>
                            </h5>
                            
                            <div class="ad-price">
                                <?= number_format($ad['price']) ?> ريال
                            </div>
                            
                            <div class="ad-meta">
                                <span>
                                    <i class="fas fa-map-marker-alt"></i>
                                    <?= htmlspecialchars($ad['city']) ?>
                                </span>
                                <span>
                                    <i class="fas fa-calendar"></i>
                                    <?= timeAgo($ad['created_at']) ?>
                                </span>
                            </div>
                            
                            <div class="ad-category">
                                <i class="fas fa-tag"></i>
                                <?= htmlspecialchars($ad['category_name']) ?>
                                <?php if ($ad['brand_name']): ?>
                                    <span class="ms-2">
                                        <i class="fas fa-car"></i>
                                        <?= htmlspecialchars($ad['brand_name']) ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- ترقيم الصفحات -->
        <?php if ($total_pages > 1): ?>
            <nav aria-label="ترقيم الصفحات" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php
                    $query_params = $_GET;
                    unset($query_params['page']);
                    $base_url = 'search.php?' . http_build_query($query_params);
                    ?>
                    
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="<?= $base_url ?>&page=<?= $page - 1 ?>">السابق</a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                            <a class="page-link" href="<?= $base_url ?>&page=<?= $i ?>"><?= $i ?></a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="<?= $base_url ?>&page=<?= $page + 1 ?>">التالي</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>
