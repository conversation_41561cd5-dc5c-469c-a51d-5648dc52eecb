<?php
session_start();

echo "<h2>🧪 اختبار نظام الرسائل</h2>";

try {
    // تعيين مسار الجذر
    $root_path = __DIR__;

    require_once $root_path . '/config/database.php';
    require_once $root_path . '/config/config.php';
    require_once $root_path . '/includes/functions.php';
    require_once $root_path . '/includes/auth.php';

    echo "✅ تم تحميل جميع الملفات المطلوبة<br>";

    // اختبار الاتصال بقاعدة البيانات
    if (isset($db)) {
        echo "✅ الاتصال بقاعدة البيانات يعمل<br>";
    } else {
        echo "❌ مشكلة في الاتصال بقاعدة البيانات<br>";
        exit();
    }

    // اختبار جدول الرسائل
    try {
        $messages_count = $db->fetch("SELECT COUNT(*) as count FROM messages")['count'];
        echo "✅ جدول الرسائل يعمل - عدد الرسائل: $messages_count<br>";
    } catch (Exception $e) {
        echo "❌ مشكلة في جدول الرسائل: " . $e->getMessage() . "<br>";
        
        // إنشاء جدول الرسائل
        echo "🔧 إنشاء جدول الرسائل...<br>";
        $db->query("
            CREATE TABLE IF NOT EXISTS messages (
                id INT PRIMARY KEY AUTO_INCREMENT,
                sender_id INT NOT NULL,
                receiver_id INT NOT NULL,
                ad_id INT NULL,
                message TEXT NOT NULL,
                is_read BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_sender_id (sender_id),
                INDEX idx_receiver_id (receiver_id),
                INDEX idx_ad_id (ad_id),
                INDEX idx_created_at (created_at),
                FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE SET NULL
            )
        ");
        echo "✅ تم إنشاء جدول الرسائل<br>";
    }

    // اختبار المستخدمين
    $users = $db->fetchAll("SELECT id, full_name, username FROM users LIMIT 5");
    echo "✅ عدد المستخدمين: " . count($users) . "<br>";

    if (count($users) < 2) {
        echo "🔧 إنشاء مستخدمين تجريبيين...<br>";
        
        // إنشاء مستخدم 1
        $db->query("INSERT INTO users (username, email, password, full_name, phone, city, user_type, is_active, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())", 
                  ['user1', '<EMAIL>', password_hash('123456', PASSWORD_DEFAULT), 'المستخدم الأول', '0501111111', 'الرياض', 'user', 1]);
        
        // إنشاء مستخدم 2
        $db->query("INSERT INTO users (username, email, password, full_name, phone, city, user_type, is_active, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())", 
                  ['user2', '<EMAIL>', password_hash('123456', PASSWORD_DEFAULT), 'المستخدم الثاني', '0502222222', 'جدة', 'user', 1]);
        
        echo "✅ تم إنشاء مستخدمين تجريبيين<br>";
        
        $users = $db->fetchAll("SELECT id, full_name, username FROM users LIMIT 5");
    }

    // اختبار إرسال رسالة تجريبية
    if (count($users) >= 2) {
        $sender_id = $users[0]['id'];
        $receiver_id = $users[1]['id'];
        
        // التحقق من وجود رسائل
        $existing_messages = $db->fetch("SELECT COUNT(*) as count FROM messages WHERE sender_id = ? AND receiver_id = ?", [$sender_id, $receiver_id])['count'];
        
        if ($existing_messages == 0) {
            echo "🔧 إنشاء رسائل تجريبية...<br>";
            
            // إرسال رسالة من المستخدم الأول للثاني
            $db->query("INSERT INTO messages (sender_id, receiver_id, message, created_at) VALUES (?, ?, ?, NOW())", 
                      [$sender_id, $receiver_id, 'مرحباً، هذه رسالة تجريبية']);
            
            // إرسال رد من المستخدم الثاني للأول
            $db->query("INSERT INTO messages (sender_id, receiver_id, message, created_at) VALUES (?, ?, ?, NOW())", 
                      [$receiver_id, $sender_id, 'مرحباً بك، شكراً لك على الرسالة']);
            
            echo "✅ تم إنشاء رسائل تجريبية<br>";
        }
    }

    // اختبار الجلسة
    echo "<h3>📊 معلومات الجلسة:</h3>";
    if (isset($_SESSION['user_id'])) {
        $current_user = $db->fetch("SELECT full_name, username FROM users WHERE id = ?", [$_SESSION['user_id']]);
        echo "✅ مسجل الدخول: " . $current_user['full_name'] . " (@" . $current_user['username'] . ")<br>";
        
        // جلب المحادثات للمستخدم الحالي
        $conversations = $db->fetchAll("
            SELECT DISTINCT 
                CASE 
                    WHEN sender_id = ? THEN receiver_id 
                    ELSE sender_id 
                END as other_user_id
            FROM messages 
            WHERE sender_id = ? OR receiver_id = ?
        ", [$_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id']]);
        
        echo "✅ عدد المحادثات: " . count($conversations) . "<br>";
        
    } else {
        echo "⚠️ غير مسجل الدخول<br>";
        echo "📝 يمكنك تسجيل الدخول بـ:<br>";
        echo "- اسم المستخدم: user1 أو user2<br>";
        echo "- كلمة المرور: 123456<br>";
    }

    // اختبار استعلامات الرسائل
    echo "<h3>📈 إحصائيات الرسائل:</h3>";
    
    $total_messages = $db->fetch("SELECT COUNT(*) as count FROM messages")['count'];
    $unread_messages = $db->fetch("SELECT COUNT(*) as count FROM messages WHERE is_read = 0")['count'];
    $unique_conversations = $db->fetch("
        SELECT COUNT(DISTINCT CONCAT(LEAST(sender_id, receiver_id), '-', GREATEST(sender_id, receiver_id))) as count 
        FROM messages
    ")['count'];
    
    echo "✅ إجمالي الرسائل: $total_messages<br>";
    echo "✅ الرسائل غير المقروءة: $unread_messages<br>";
    echo "✅ المحادثات الفريدة: $unique_conversations<br>";

    echo "<hr>";
    echo "<h3>🔗 روابط الاختبار:</h3>";
    echo "<ul>";
    echo "<li><a href='pages/messages.php' target='_blank'>صفحة الرسائل الرئيسية</a></li>";
    
    if (count($users) >= 2) {
        echo "<li><a href='pages/messages.php?user_id=" . $users[1]['id'] . "' target='_blank'>محادثة مع " . $users[1]['full_name'] . "</a></li>";
        echo "<li><a href='pages/messages.php?user_id=" . $users[0]['id'] . "' target='_blank'>محادثة مع " . $users[0]['full_name'] . "</a></li>";
    }
    
    echo "<li><a href='pages/login.php' target='_blank'>تسجيل الدخول</a></li>";
    echo "<li><a href='pages/ad-details.php?id=1' target='_blank'>اختبار إرسال رسالة من الإعلان</a></li>";
    echo "</ul>";

    // اختبار POST request
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_message'])) {
        echo "<h3>🧪 اختبار إرسال رسالة:</h3>";
        
        if (isset($_SESSION['user_id']) && count($users) >= 2) {
            $receiver_id = $_POST['receiver_id'];
            $message = $_POST['message'];
            
            try {
                $db->query("INSERT INTO messages (sender_id, receiver_id, message, created_at) VALUES (?, ?, ?, NOW())", 
                          [$_SESSION['user_id'], $receiver_id, $message]);
                echo "✅ تم إرسال الرسالة بنجاح!<br>";
            } catch (Exception $e) {
                echo "❌ خطأ في إرسال الرسالة: " . $e->getMessage() . "<br>";
            }
        } else {
            echo "❌ يجب تسجيل الدخول أولاً<br>";
        }
    }

    // نموذج اختبار إرسال رسالة
    if (isset($_SESSION['user_id']) && count($users) >= 2) {
        echo "<hr>";
        echo "<h3>📝 اختبار إرسال رسالة:</h3>";
        echo "<form method='POST'>";
        echo "<div class='mb-3'>";
        echo "<label>المستقبل:</label>";
        echo "<select name='receiver_id' class='form-select'>";
        foreach ($users as $user) {
            if ($user['id'] != $_SESSION['user_id']) {
                echo "<option value='" . $user['id'] . "'>" . $user['full_name'] . "</option>";
            }
        }
        echo "</select>";
        echo "</div>";
        echo "<div class='mb-3'>";
        echo "<label>الرسالة:</label>";
        echo "<textarea name='message' class='form-control' required>رسالة اختبار من صفحة الاختبار</textarea>";
        echo "</div>";
        echo "<button type='submit' name='test_message' class='btn btn-primary'>إرسال رسالة تجريبية</button>";
        echo "</form>";
    }

    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724;'>🎉 اختبار نظام الرسائل مكتمل!</h3>";
    echo "<p style='color: #155724;'>يمكنك الآن اختبار جميع ميزات الرسائل باستخدام الروابط أعلاه.</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ: " . $e->getMessage() . "</div>";
    echo "<div style='color: red;'>📍 الملف: " . $e->getFile() . "</div>";
    echo "<div style='color: red;'>📍 السطر: " . $e->getLine() . "</div>";
}

// إضافة Bootstrap للتنسيق
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
ul { margin: 10px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
.form-select, .form-control, .btn { margin: 5px 0; }
</style>";
?>
