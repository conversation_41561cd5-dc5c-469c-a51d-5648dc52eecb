-- إن<PERSON>اء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS harajuna CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE harajuna;

-- جدول المستخدمين
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    city VARCHAR(50),
    region VARCHAR(50),
    user_type ENUM('user', 'admin') DEFAULT 'user',
    profile_image VARCHAR(255),
    is_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    reset_token VARCHAR(255),
    reset_expires DATETIME,
    last_login DATETIME,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الفئات الرئيسية
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    image_path VARCHAR(255),
    parent_id INT DEFAULT NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- جدول الإعلانات
CREATE TABLE ads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    category_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    price DECIMAL(10,2),
    price_type ENUM('fixed', 'negotiable', 'free') DEFAULT 'fixed',
    condition_type ENUM('new', 'used', 'refurbished') DEFAULT 'used',
    city VARCHAR(50) NOT NULL,
    region VARCHAR(50) NOT NULL,
    contact_phone VARCHAR(20),
    contact_email VARCHAR(100),
    whatsapp VARCHAR(20),
    status ENUM('pending', 'active', 'sold', 'expired', 'rejected') DEFAULT 'pending',
    is_featured BOOLEAN DEFAULT FALSE,
    views_count INT DEFAULT 0,
    favorites_count INT DEFAULT 0,
    expires_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT,
    INDEX idx_status (status),
    INDEX idx_category (category_id),
    INDEX idx_city (city),
    INDEX idx_created (created_at),
    INDEX idx_price (price)
);

-- جدول صور الإعلانات
CREATE TABLE ad_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ad_id INT NOT NULL,
    image_path VARCHAR(255) NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE
);

-- جدول المفضلة
CREATE TABLE favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ad_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE,
    UNIQUE KEY unique_favorite (user_id, ad_id)
);

-- جدول الرسائل
CREATE TABLE messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ad_id INT NOT NULL,
    sender_id INT NOT NULL,
    receiver_id INT NOT NULL,
    subject VARCHAR(200),
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول التقييمات
CREATE TABLE ratings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    rated_user_id INT NOT NULL,
    rater_user_id INT NOT NULL,
    ad_id INT,
    rating TINYINT CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (rated_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (rater_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE SET NULL,
    UNIQUE KEY unique_rating (rated_user_id, rater_user_id, ad_id)
);

-- جدول التقارير
CREATE TABLE reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    reporter_id INT NOT NULL,
    reported_ad_id INT,
    reported_user_id INT,
    reason ENUM('spam', 'inappropriate', 'fake', 'duplicate', 'other') NOT NULL,
    description TEXT,
    status ENUM('pending', 'reviewed', 'resolved', 'dismissed') DEFAULT 'pending',
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (reporter_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reported_ad_id) REFERENCES ads(id) ON DELETE CASCADE,
    FOREIGN KEY (reported_user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول إعدادات الموقع
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول سجل النشاطات
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50),
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول الصفحات الثابتة
CREATE TABLE pages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    meta_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الإحصائيات اليومية
CREATE TABLE daily_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE UNIQUE NOT NULL,
    new_users INT DEFAULT 0,
    new_ads INT DEFAULT 0,
    total_views INT DEFAULT 0,
    total_messages INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول رسائل الاتصال
CREATE TABLE IF NOT EXISTS contact_messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    subject VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    status ENUM('new', 'read', 'replied', 'closed') DEFAULT 'new',
    ip_address VARCHAR(45),
    user_agent TEXT,
    admin_reply TEXT,
    replied_at TIMESTAMP NULL,
    replied_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (replied_by) REFERENCES users(id) ON DELETE SET NULL
);

-- إنشاء جدول المستخدمين المحظورين
CREATE TABLE IF NOT EXISTS banned_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    ip_address VARCHAR(45),
    username VARCHAR(50),
    email VARCHAR(100),
    reason TEXT NOT NULL,
    banned_by INT NOT NULL,
    ban_type ENUM('temporary', 'permanent') DEFAULT 'permanent',
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_ip_address (ip_address),
    INDEX idx_username (username),
    INDEX idx_email (email),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (banned_by) REFERENCES users(id) ON DELETE CASCADE
);

-- إنشاء جدول تتبع الموقع الجغرافي
CREATE TABLE IF NOT EXISTS user_locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    ip_address VARCHAR(45) NOT NULL,
    country VARCHAR(100),
    city VARCHAR(100),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    is_vpn BOOLEAN DEFAULT FALSE,
    is_proxy BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_ip_address (ip_address),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- إنشاء جدول سجل الأنشطة المشبوهة
CREATE TABLE IF NOT EXISTS suspicious_activities (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    ip_address VARCHAR(45),
    activity_type ENUM('multiple_accounts', 'vpn_detected', 'suspicious_location', 'rapid_posting', 'fake_info') NOT NULL,
    description TEXT,
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    status ENUM('pending', 'reviewed', 'resolved', 'ignored') DEFAULT 'pending',
    reviewed_by INT,
    reviewed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_ip_address (ip_address),
    INDEX idx_activity_type (activity_type),
    INDEX idx_status (status),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- إنشاء جدول التقارير والشكاوى
CREATE TABLE IF NOT EXISTS reports (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    ad_id INT NOT NULL,
    reason VARCHAR(100) NOT NULL,
    description TEXT,
    status ENUM('pending', 'reviewed', 'resolved', 'rejected') DEFAULT 'pending',
    reviewed_by INT,
    reviewed_at TIMESTAMP NULL,
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_ad_id (ad_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- إدراج البيانات الأولية

-- إنشاء مستخدم الإدمن الافتراضي
INSERT INTO users (username, email, password, full_name, phone, city, region, user_type, is_verified, is_active) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير الموقع', '0501234567', 'الرياض', 'الرياض', 'admin', TRUE, TRUE);

-- إدراج الفئات الرئيسية
INSERT INTO categories (name, slug, description, icon, sort_order) VALUES
('سيارات', 'cars', 'سيارات ومركبات', 'fas fa-car', 1),
('عقارات', 'real-estate', 'عقارات للبيع والإيجار', 'fas fa-home', 2),
('أجهزة إلكترونية', 'electronics', 'أجهزة إلكترونية ومعدات', 'fas fa-laptop', 3),
('أثاث ومنزل', 'furniture', 'أثاث ومستلزمات منزلية', 'fas fa-couch', 4),
('أزياء وموضة', 'fashion', 'ملابس وإكسسوارات', 'fas fa-tshirt', 5),
('وظائف', 'jobs', 'فرص عمل ووظائف', 'fas fa-briefcase', 6),
('خدمات', 'services', 'خدمات متنوعة', 'fas fa-tools', 7),
('حيوانات وطيور', 'animals', 'حيوانات أليفة وطيور', 'fas fa-paw', 8),
('رياضة وترفيه', 'sports', 'معدات رياضية وترفيهية', 'fas fa-futbol', 9),
('أخرى', 'others', 'فئات متنوعة', 'fas fa-th', 10);

-- إدراج الفئات الفرعية للسيارات
INSERT INTO categories (name, slug, description, parent_id, sort_order) VALUES
('سيارات للبيع', 'cars-for-sale', 'سيارات مستعملة وجديدة للبيع', 1, 1),
('قطع غيار', 'car-parts', 'قطع غيار السيارات', 1, 2),
('دراجات نارية', 'motorcycles', 'دراجات نارية ومعدات', 1, 3),
('قوارب', 'boats', 'قوارب ومعدات بحرية', 1, 4),
('معدات ثقيلة', 'heavy-equipment', 'معدات ثقيلة وآليات', 1, 5);

-- إدراج الفئات الفرعية للعقارات
INSERT INTO categories (name, slug, description, parent_id, sort_order) VALUES
('شقق للبيع', 'apartments-sale', 'شقق سكنية للبيع', 2, 1),
('فلل للبيع', 'villas-sale', 'فلل وقصور للبيع', 2, 2),
('أراضي للبيع', 'lands-sale', 'أراضي سكنية وتجارية', 2, 3),
('شقق للإيجار', 'apartments-rent', 'شقق للإيجار', 2, 4),
('فلل للإيجار', 'villas-rent', 'فلل للإيجار', 2, 5),
('محلات ومكاتب', 'commercial', 'عقارات تجارية', 2, 6);

-- إدراج إعدادات الموقع الافتراضية
INSERT INTO settings (setting_key, setting_value, description) VALUES
('site_name', 'حراجنا', 'اسم الموقع'),
('site_description', 'موقع حراجنا للإعلانات المبوبة', 'وصف الموقع'),
('contact_email', '<EMAIL>', 'بريد التواصل'),
('contact_phone', '0501234567', 'رقم التواصل'),
('ads_per_page', '12', 'عدد الإعلانات في الصفحة'),
('max_images_per_ad', '5', 'أقصى عدد صور للإعلان'),
('ad_expiry_days', '30', 'مدة انتهاء الإعلان بالأيام'),
('featured_ad_price', '50', 'سعر الإعلان المميز'),
('allow_registration', '1', 'السماح بالتسجيل'),
('require_email_verification', '1', 'طلب تأكيد البريد الإلكتروني');

-- إدراج صفحات ثابتة
INSERT INTO pages (title, slug, content, meta_description) VALUES
('من نحن', 'about', '<h2>من نحن</h2><p>حراجنا هو موقع إعلانات مبوبة يهدف إلى ربط البائعين والمشترين في المملكة العربية السعودية.</p>', 'تعرف على موقع حراجنا'),
('شروط الاستخدام', 'terms', '<h2>شروط الاستخدام</h2><p>يرجى قراءة شروط الاستخدام بعناية قبل استخدام الموقع.</p>', 'شروط استخدام موقع حراجنا'),
('سياسة الخصوصية', 'privacy', '<h2>سياسة الخصوصية</h2><p>نحن نحترم خصوصيتك ونحمي بياناتك الشخصية.</p>', 'سياسة الخصوصية'),
('اتصل بنا', 'contact', '<h2>اتصل بنا</h2><p>للتواصل معنا يرجى استخدام المعلومات التالية:</p>', 'تواصل معنا');
