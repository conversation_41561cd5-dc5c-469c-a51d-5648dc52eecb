<?php
session_start();

// التحقق من وجود ملف التكوين
if (file_exists('../config/installed.lock')) {
    die('الموقع مثبت بالفعل. إذا كنت تريد إعادة التثبيت، احذف ملف config/installed.lock');
}

$error = '';
$success = '';
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step === 1) {
        // التحقق من متطلبات النظام
        $requirements = checkRequirements();
        if ($requirements['all_passed']) {
            header('Location: install.php?step=2');
            exit();
        } else {
            $error = 'يرجى تلبية جميع المتطلبات قبل المتابعة';
        }
    } elseif ($step === 2) {
        // إعداد قاعدة البيانات
        $dbConfig = [
            'host' => $_POST['db_host'] ?? 'localhost',
            'name' => $_POST['db_name'] ?? 'harajuna',
            'user' => $_POST['db_user'] ?? 'root',
            'pass' => $_POST['db_pass'] ?? '',
        ];
        
        $result = setupDatabase($dbConfig);
        if ($result['success']) {
            $_SESSION['db_config'] = $dbConfig;
            header('Location: install.php?step=3');
            exit();
        } else {
            $error = $result['error'];
        }
    } elseif ($step === 3) {
        // إنشاء مستخدم الإدمن
        $adminData = [
            'username' => $_POST['admin_username'] ?? 'admin',
            'email' => $_POST['admin_email'] ?? '<EMAIL>',
            'password' => $_POST['admin_password'] ?? '',
            'full_name' => $_POST['admin_name'] ?? 'مدير الموقع',
        ];
        
        $result = createAdmin($adminData, $_SESSION['db_config']);
        if ($result['success']) {
            // إنشاء ملف التثبيت
            file_put_contents('../config/installed.lock', date('Y-m-d H:i:s'));
            header('Location: install.php?step=4');
            exit();
        } else {
            $error = $result['error'];
        }
    }
}

function checkRequirements() {
    $requirements = [
        'php_version' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'pdo_mysql' => extension_loaded('pdo_mysql'),
        'gd' => extension_loaded('gd'),
        'mbstring' => extension_loaded('mbstring'),
        'openssl' => extension_loaded('openssl'),
        'uploads_writable' => is_writable('../uploads') || mkdir('../uploads', 0755, true),
        'config_writable' => is_writable('../config'),
    ];
    
    $requirements['all_passed'] = !in_array(false, $requirements);
    return $requirements;
}

function setupDatabase($config) {
    try {
        // الاتصال بقاعدة البيانات
        $dsn = "mysql:host={$config['host']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['user'], $config['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        ]);
        
        // إنشاء قاعدة البيانات إذا لم تكن موجودة
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `{$config['name']}`");
        
        // تنفيذ ملف SQL
        $sql = file_get_contents('database.sql');
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        return ['success' => true];
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()];
    }
}

function createAdmin($adminData, $dbConfig) {
    try {
        $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['name']};charset=utf8mb4";
        $pdo = new PDO($dsn, $dbConfig['user'], $dbConfig['pass']);
        
        // التحقق من صحة البيانات
        if (empty($adminData['username']) || empty($adminData['email']) || empty($adminData['password'])) {
            return ['success' => false, 'error' => 'جميع الحقول مطلوبة'];
        }
        
        if (strlen($adminData['password']) < 6) {
            return ['success' => false, 'error' => 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'];
        }
        
        // تحديث بيانات الإدمن
        $hashedPassword = password_hash($adminData['password'], PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET username = ?, email = ?, password = ?, full_name = ? WHERE user_type = 'admin'");
        $stmt->execute([
            $adminData['username'],
            $adminData['email'],
            $hashedPassword,
            $adminData['full_name']
        ]);
        
        return ['success' => true];
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'خطأ في إنشاء المدير: ' . $e->getMessage()];
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت موقع حراجنا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .install-container { max-width: 600px; margin: 50px auto; }
        .install-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); overflow: hidden; }
        .install-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .install-body { padding: 40px; }
        .step-indicator { display: flex; justify-content: center; margin-bottom: 30px; }
        .step { width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 10px; }
        .step.active { background: #667eea; color: white; }
        .step.completed { background: #28a745; color: white; }
        .step.pending { background: #e9ecef; color: #6c757d; }
        .requirement { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .requirement.passed { background: #d4edda; color: #155724; }
        .requirement.failed { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-card">
            <div class="install-header">
                <h1><i class="fas fa-cog"></i> تثبيت موقع حراجنا</h1>
                <p>مرحباً بك في معالج تثبيت موقع حراجنا</p>
            </div>
            
            <div class="install-body">
                <div class="step-indicator">
                    <div class="step <?= $step >= 1 ? ($step > 1 ? 'completed' : 'active') : 'pending' ?>">1</div>
                    <div class="step <?= $step >= 2 ? ($step > 2 ? 'completed' : 'active') : 'pending' ?>">2</div>
                    <div class="step <?= $step >= 3 ? ($step > 3 ? 'completed' : 'active') : 'pending' ?>">3</div>
                    <div class="step <?= $step >= 4 ? 'completed' : 'pending' ?>">4</div>
                </div>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> <?= $error ?></div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success"><i class="fas fa-check"></i> <?= $success ?></div>
                <?php endif; ?>
                
                <?php if ($step === 1): ?>
                    <h3>الخطوة 1: التحقق من المتطلبات</h3>
                    <?php $requirements = checkRequirements(); ?>
                    
                    <div class="requirement <?= $requirements['php_version'] ? 'passed' : 'failed' ?>">
                        <i class="fas <?= $requirements['php_version'] ? 'fa-check' : 'fa-times' ?>"></i>
                        PHP 7.4+ (الحالي: <?= PHP_VERSION ?>)
                    </div>
                    
                    <div class="requirement <?= $requirements['pdo_mysql'] ? 'passed' : 'failed' ?>">
                        <i class="fas <?= $requirements['pdo_mysql'] ? 'fa-check' : 'fa-times' ?>"></i>
                        PDO MySQL Extension
                    </div>
                    
                    <div class="requirement <?= $requirements['gd'] ? 'passed' : 'failed' ?>">
                        <i class="fas <?= $requirements['gd'] ? 'fa-check' : 'fa-times' ?>"></i>
                        GD Extension (لمعالجة الصور)
                    </div>
                    
                    <div class="requirement <?= $requirements['mbstring'] ? 'passed' : 'failed' ?>">
                        <i class="fas <?= $requirements['mbstring'] ? 'fa-check' : 'fa-times' ?>"></i>
                        Mbstring Extension
                    </div>
                    
                    <div class="requirement <?= $requirements['uploads_writable'] ? 'passed' : 'failed' ?>">
                        <i class="fas <?= $requirements['uploads_writable'] ? 'fa-check' : 'fa-times' ?>"></i>
                        مجلد uploads قابل للكتابة
                    </div>
                    
                    <div class="requirement <?= $requirements['config_writable'] ? 'passed' : 'failed' ?>">
                        <i class="fas <?= $requirements['config_writable'] ? 'fa-check' : 'fa-times' ?>"></i>
                        مجلد config قابل للكتابة
                    </div>
                    
                    <?php if ($requirements['all_passed']): ?>
                        <form method="post" class="mt-4">
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-arrow-left"></i> متابعة إلى الخطوة التالية
                            </button>
                        </form>
                    <?php else: ?>
                        <div class="alert alert-warning mt-4">
                            يرجى حل المشاكل المذكورة أعلاه قبل المتابعة
                        </div>
                    <?php endif; ?>
                    
                <?php elseif ($step === 2): ?>
                    <h3>الخطوة 2: إعداد قاعدة البيانات</h3>
                    <form method="post">
                        <div class="mb-3">
                            <label class="form-label">خادم قاعدة البيانات</label>
                            <input type="text" name="db_host" class="form-control" value="localhost" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">اسم قاعدة البيانات</label>
                            <input type="text" name="db_name" class="form-control" value="harajuna" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" name="db_user" class="form-control" value="root" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" name="db_pass" class="form-control">
                        </div>
                        <button type="submit" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-database"></i> إنشاء قاعدة البيانات
                        </button>
                    </form>
                    
                <?php elseif ($step === 3): ?>
                    <h3>الخطوة 3: إنشاء حساب المدير</h3>
                    <form method="post">
                        <div class="mb-3">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" name="admin_username" class="form-control" value="admin" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" name="admin_email" class="form-control" value="<EMAIL>" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" name="admin_password" class="form-control" required minlength="6">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الاسم الكامل</label>
                            <input type="text" name="admin_name" class="form-control" value="مدير الموقع" required>
                        </div>
                        <button type="submit" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-user-shield"></i> إنشاء حساب المدير
                        </button>
                    </form>
                    
                <?php elseif ($step === 4): ?>
                    <div class="text-center">
                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                        <h3 class="mt-3">تم التثبيت بنجاح!</h3>
                        <p class="text-muted">تم تثبيت موقع حراجنا بنجاح. يمكنك الآن البدء في استخدام الموقع.</p>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <a href="../pages/home.php" class="btn btn-success btn-lg w-100">
                                    <i class="fas fa-home"></i> الصفحة الرئيسية
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="../admin/index.php" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-cogs"></i> لوحة التحكم
                                </a>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-4">
                            <strong>ملاحظة:</strong> لأسباب أمنية، يرجى حذف مجلد install بعد انتهاء التثبيت.
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
