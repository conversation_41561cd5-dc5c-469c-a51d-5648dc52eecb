<?php
// صفحة ترحيب موقع حراجنا
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مرحباً بك في حراجنا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .welcome-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .btn-custom {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .btn-custom:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            transform: translateY(-2px);
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            background: #28a745;
            border-radius: 50%;
            margin-left: 10px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="welcome-card">
        <h1 class="mb-4">
            <i class="fas fa-store"></i> مرحباً بك في حراجنا
        </h1>
        <p class="lead mb-4">
            موقع الإعلانات المبوبة الأول في المملكة العربية السعودية
        </p>

        <div class="mb-4">
            <span class="status-indicator"></span>
            <strong>الخادم يعمل بنجاح على المنفذ 8081</strong>
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <a href="install/install.php" class="btn-custom">
                    <i class="fas fa-cog"></i> تثبيت الموقع
                </a>
            </div>
            <div class="col-md-6 mb-3">
                <a href="pages/home.php" class="btn-custom">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
            </div>
            <div class="col-md-6 mb-3">
                <a href="admin/index.php" class="btn-custom">
                    <i class="fas fa-user-shield"></i> لوحة الإدارة
                </a>
            </div>
            <div class="col-md-6 mb-3">
                <a href="pages/login.php" class="btn-custom">
                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                </a>
            </div>
        </div>

        <hr class="my-4" style="border-color: rgba(255,255,255,0.3);">

        <div class="row text-start">
            <div class="col-md-6">
                <h6><i class="fas fa-info-circle"></i> معلومات النظام:</h6>
                <ul class="list-unstyled small">
                    <li>📅 التاريخ: <?= date('Y-m-d H:i:s') ?></li>
                    <li>🐘 PHP: <?= PHP_VERSION ?></li>
                    <li>🌐 الخادم: localhost:8081</li>
                    <li>📁 المجلد: <?= __DIR__ ?></li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-check-circle"></i> حالة الملفات:</h6>
                <ul class="list-unstyled small">
                    <li><?= file_exists('config/database.php') ? '✅' : '❌' ?> config/database.php</li>
                    <li><?= file_exists('includes/functions.php') ? '✅' : '❌' ?> includes/functions.php</li>
                    <li><?= file_exists('pages/home.php') ? '✅' : '❌' ?> pages/home.php</li>
                    <li><?= file_exists('admin/index.php') ? '✅' : '❌' ?> admin/index.php</li>
                </ul>
            </div>
        </div>

        <div class="mt-4">
            <small class="text-muted">
                تم تطوير الموقع بـ ❤️ باستخدام PHP و Bootstrap
            </small>
        </div>
    </div>
</body>
</html>
