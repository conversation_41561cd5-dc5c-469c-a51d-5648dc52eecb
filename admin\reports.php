<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدمن
requireAdmin();

$error = '';
$success = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $action = $_POST['action'];
        $report_id = (int)$_POST['report_id'];
        
        switch ($action) {
            case 'mark_reviewed':
                $db->query("UPDATE reports SET status = 'reviewed', reviewed_by = ?, reviewed_at = NOW() WHERE id = ?", 
                          [$_SESSION['user_id'], $report_id]);
                $success = 'تم تحديد التقرير كمراجع';
                break;
                
            case 'resolve':
                $admin_notes = isset($_POST['admin_notes']) ? sanitize($_POST['admin_notes']) : '';
                $db->query("UPDATE reports SET status = 'resolved', reviewed_by = ?, reviewed_at = NOW(), admin_notes = ? WHERE id = ?", 
                          [$_SESSION['user_id'], $admin_notes, $report_id]);
                $success = 'تم حل التقرير';
                break;
                
            case 'reject':
                $admin_notes = isset($_POST['admin_notes']) ? sanitize($_POST['admin_notes']) : '';
                $db->query("UPDATE reports SET status = 'rejected', reviewed_by = ?, reviewed_at = NOW(), admin_notes = ? WHERE id = ?", 
                          [$_SESSION['user_id'], $admin_notes, $report_id]);
                $success = 'تم رفض التقرير';
                break;
                
            case 'delete':
                $db->query("DELETE FROM reports WHERE id = ?", [$report_id]);
                $success = 'تم حذف التقرير';
                break;
        }
        
        // إعادة توجيه لتجنب إعادة الإرسال
        header('Location: reports.php');
        exit();
        
    } catch (Exception $e) {
        $error = 'حدث خطأ: ' . $e->getMessage();
    }
}

// جلب التقارير
try {
    $search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
    $status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : 'all';
    $type_filter = isset($_GET['type']) ? sanitize($_GET['type']) : 'all';
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $items_per_page = 20;
    $offset = ($page - 1) * $items_per_page;

    $where_conditions = [];
    $params = [];

    if (!empty($search)) {
        $where_conditions[] = "(r.reason LIKE ? OR u.username LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    if ($status_filter !== 'all') {
        $where_conditions[] = "r.status = ?";
        $params[] = $status_filter;
    }

    if ($type_filter !== 'all') {
        $where_conditions[] = "r.report_type = ?";
        $params[] = $type_filter;
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    $sql = "SELECT r.*, 
                   u.username as reporter_username,
                   a.title as ad_title,
                   ru.username as reported_username,
                   admin.username as reviewed_by_username
            FROM reports r 
            LEFT JOIN users u ON r.reporter_id = u.id 
            LEFT JOIN ads a ON r.reported_ad_id = a.id 
            LEFT JOIN users ru ON r.reported_user_id = ru.id 
            LEFT JOIN users admin ON r.reviewed_by = admin.id 
            $where_clause 
            ORDER BY r.created_at DESC 
            LIMIT $items_per_page OFFSET $offset";

    $reports = $db->fetchAll($sql, $params);

    // عدد النتائج الإجمالي
    $count_sql = "SELECT COUNT(*) as total FROM reports r LEFT JOIN users u ON r.reporter_id = u.id $where_clause";
    $total_reports_count = $db->fetch($count_sql, $params)['total'];
    $total_pages = ceil($total_reports_count / $items_per_page);

    // إحصائيات
    $stats = [
        'total' => $db->fetch("SELECT COUNT(*) as count FROM reports")['count'],
        'pending' => $db->fetch("SELECT COUNT(*) as count FROM reports WHERE status = 'pending'")['count'],
        'reviewed' => $db->fetch("SELECT COUNT(*) as count FROM reports WHERE status = 'reviewed'")['count'],
        'resolved' => $db->fetch("SELECT COUNT(*) as count FROM reports WHERE status = 'resolved'")['count'],
        'rejected' => $db->fetch("SELECT COUNT(*) as count FROM reports WHERE status = 'rejected'")['count'],
    ];

} catch (Exception $e) {
    $error = 'خطأ في جلب البيانات: ' . $e->getMessage();
    $reports = [];
    $stats = ['total' => 0, 'pending' => 0, 'reviewed' => 0, 'resolved' => 0, 'rejected' => 0];
    $total_pages = 0;
}

$page_title = 'إدارة التقارير';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة التقارير والشكاوى</h1>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?= $error ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?= $success ?>
                </div>
            <?php endif; ?>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-2 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4><?= number_format($stats['total']) ?></h4>
                            <p class="mb-0">إجمالي التقارير</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h4><?= number_format($stats['pending']) ?></h4>
                            <p class="mb-0">معلقة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h4><?= number_format($stats['reviewed']) ?></h4>
                            <p class="mb-0">قيد المراجعة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4><?= number_format($stats['resolved']) ?></h4>
                            <p class="mb-0">محلولة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h4><?= number_format($stats['rejected']) ?></h4>
                            <p class="mb-0">مرفوضة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?= htmlspecialchars($search) ?>" placeholder="ابحث في السبب أو اسم المبلغ">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="all" <?= $status_filter === 'all' ? 'selected' : '' ?>>جميع الحالات</option>
                                <option value="pending" <?= $status_filter === 'pending' ? 'selected' : '' ?>>معلقة</option>
                                <option value="reviewed" <?= $status_filter === 'reviewed' ? 'selected' : '' ?>>قيد المراجعة</option>
                                <option value="resolved" <?= $status_filter === 'resolved' ? 'selected' : '' ?>>محلولة</option>
                                <option value="rejected" <?= $status_filter === 'rejected' ? 'selected' : '' ?>>مرفوضة</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="type" class="form-label">نوع التقرير</label>
                            <select class="form-select" id="type" name="type">
                                <option value="all" <?= $type_filter === 'all' ? 'selected' : '' ?>>جميع الأنواع</option>
                                <option value="spam" <?= $type_filter === 'spam' ? 'selected' : '' ?>>رسائل مزعجة</option>
                                <option value="inappropriate" <?= $type_filter === 'inappropriate' ? 'selected' : '' ?>>محتوى غير مناسب</option>
                                <option value="fake" <?= $type_filter === 'fake' ? 'selected' : '' ?>>محتوى مزيف</option>
                                <option value="fraud" <?= $type_filter === 'fraud' ? 'selected' : '' ?>>احتيال</option>
                                <option value="other" <?= $type_filter === 'other' ? 'selected' : '' ?>>أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block w-100">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول التقارير -->
            <div class="card">
                <div class="card-body">
                    <?php if (empty($reports)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-flag fa-3x text-muted mb-3"></i>
                            <h5>لا توجد تقارير</h5>
                            <p class="text-muted">لم يتم العثور على أي تقارير</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>المبلغ</th>
                                        <th>النوع</th>
                                        <th>السبب</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($reports as $report): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($report['reporter_username']) ?></strong>
                                                <?php if ($report['ad_title']): ?>
                                                    <br><small class="text-muted">إعلان: <?= htmlspecialchars($report['ad_title']) ?></small>
                                                <?php endif; ?>
                                                <?php if ($report['reported_username']): ?>
                                                    <br><small class="text-muted">مستخدم: <?= htmlspecialchars($report['reported_username']) ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $type_labels = [
                                                    'spam' => 'رسائل مزعجة',
                                                    'inappropriate' => 'محتوى غير مناسب',
                                                    'fake' => 'محتوى مزيف',
                                                    'fraud' => 'احتيال',
                                                    'other' => 'أخرى'
                                                ];
                                                echo $type_labels[$report['report_type']] ?? $report['report_type'];
                                                ?>
                                            </td>
                                            <td><?= htmlspecialchars(substr($report['reason'], 0, 100)) ?>...</td>
                                            <td>
                                                <?php
                                                $status_classes = [
                                                    'pending' => 'warning',
                                                    'reviewed' => 'info',
                                                    'resolved' => 'success',
                                                    'rejected' => 'danger'
                                                ];
                                                $status_labels = [
                                                    'pending' => 'معلقة',
                                                    'reviewed' => 'قيد المراجعة',
                                                    'resolved' => 'محلولة',
                                                    'rejected' => 'مرفوضة'
                                                ];
                                                ?>
                                                <span class="badge bg-<?= $status_classes[$report['status']] ?>">
                                                    <?= $status_labels[$report['status']] ?>
                                                </span>
                                            </td>
                                            <td><?= date('Y-m-d H:i', strtotime($report['created_at'])) ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <?php if ($report['status'] === 'pending'): ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="mark_reviewed">
                                                            <input type="hidden" name="report_id" value="<?= $report['id'] ?>">
                                                            <button type="submit" class="btn btn-sm btn-outline-info" title="مراجعة">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>

                                                    <?php if (in_array($report['status'], ['pending', 'reviewed'])): ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="resolve">
                                                            <input type="hidden" name="report_id" value="<?= $report['id'] ?>">
                                                            <input type="hidden" name="admin_notes" value="تم الحل">
                                                            <button type="submit" class="btn btn-sm btn-outline-success" title="حل">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        </form>

                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="reject">
                                                            <input type="hidden" name="report_id" value="<?= $report['id'] ?>">
                                                            <input type="hidden" name="admin_notes" value="تم الرفض">
                                                            <button type="submit" class="btn btn-sm btn-outline-warning" title="رفض">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>

                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="action" value="delete">
                                                        <input type="hidden" name="report_id" value="<?= $report['id'] ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                                onclick="return confirm('هل أنت متأكد من حذف هذا التقرير؟')" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- ترقيم الصفحات -->
                        <?php if ($total_pages > 1): ?>
                            <nav aria-label="ترقيم الصفحات">
                                <ul class="pagination justify-content-center">
                                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                            <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= $status_filter ?>&type=<?= $type_filter ?>">
                                                <?= $i ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
