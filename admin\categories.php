<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// إنشاء كائن المصادقة
$auth = new Auth($db);

// التحقق من صلاحيات الإدمن
requireAdmin();

$error = '';
$success = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        switch ($action) {
            case 'add_category':
                $name = sanitize($_POST['name']);
                $slug = sanitize($_POST['slug']);
                $description = sanitize($_POST['description']);
                $icon = sanitize($_POST['icon']);
                $parent_id = !empty($_POST['parent_id']) ? (int)$_POST['parent_id'] : null;
                $sort_order = (int)$_POST['sort_order'];
                
                // رفع الصورة إذا تم اختيارها
                $image_path = null;
                if (!empty($_FILES['image']['name'])) {
                    $image_path = uploadImage($_FILES['image'], 'categories');
                    if (!$image_path) {
                        $error = 'فشل في رفع الصورة';
                        break;
                    }
                }
                
                if (!empty($name)) {
                    $sql = "INSERT INTO categories (name, slug, description, icon, image_path, parent_id, sort_order, is_active) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, 1)";
                    $db->query($sql, [$name, $slug, $description, $icon, $image_path, $parent_id, $sort_order]);
                    $success = 'تم إضافة الفئة بنجاح';
                } else {
                    $error = 'اسم الفئة مطلوب';
                }
                break;
                
            case 'edit_category':
                $id = (int)$_POST['id'];
                $name = sanitize($_POST['name']);
                $slug = sanitize($_POST['slug']);
                $description = sanitize($_POST['description']);
                $icon = sanitize($_POST['icon']);
                $parent_id = !empty($_POST['parent_id']) ? (int)$_POST['parent_id'] : null;
                $sort_order = (int)$_POST['sort_order'];
                $is_active = isset($_POST['is_active']) ? 1 : 0;
                
                // رفع صورة جديدة إذا تم اختيارها
                $image_path = $_POST['current_image'];
                if (!empty($_FILES['image']['name'])) {
                    $new_image = uploadImage($_FILES['image'], 'categories');
                    if ($new_image) {
                        // حذف الصورة القديمة
                        if ($image_path) {
                            deleteImage($image_path);
                        }
                        $image_path = $new_image;
                    }
                }
                
                if (!empty($name)) {
                    $sql = "UPDATE categories SET name = ?, slug = ?, description = ?, icon = ?, image_path = ?, 
                            parent_id = ?, sort_order = ?, is_active = ? WHERE id = ?";
                    $db->query($sql, [$name, $slug, $description, $icon, $image_path, $parent_id, $sort_order, $is_active, $id]);
                    $success = 'تم تحديث الفئة بنجاح';
                } else {
                    $error = 'اسم الفئة مطلوب';
                }
                break;
                
            case 'delete_category':
                $id = (int)$_POST['id'];
                
                // التحقق من وجود إعلانات في هذه الفئة
                $ads_count = $db->fetch("SELECT COUNT(*) as count FROM ads WHERE category_id = ?", [$id])['count'];
                if ($ads_count > 0) {
                    $error = 'لا يمكن حذف الفئة لأنها تحتوي على إعلانات';
                } else {
                    // حذف الصورة
                    $category = $db->fetch("SELECT image_path FROM categories WHERE id = ?", [$id]);
                    if ($category && $category['image_path']) {
                        deleteImage($category['image_path']);
                    }
                    
                    $db->query("DELETE FROM categories WHERE id = ?", [$id]);
                    $success = 'تم حذف الفئة بنجاح';
                }
                break;
        }
    }
}

// جلب الفئات
$categories = $db->fetchAll("
    SELECT c.*, p.name as parent_name,
           (SELECT COUNT(*) FROM ads WHERE category_id = c.id) as ads_count,
           (SELECT COUNT(*) FROM categories WHERE parent_id = c.id) as subcategories_count
    FROM categories c
    LEFT JOIN categories p ON c.parent_id = p.id
    ORDER BY c.parent_id IS NULL DESC, c.parent_id, c.sort_order, c.name
");

// جلب الفئات الرئيسية للقائمة المنسدلة
$parent_categories = $db->fetchAll("SELECT * FROM categories WHERE parent_id IS NULL ORDER BY sort_order, name");

$page_title = 'إدارة الفئات';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة الفئات</h1>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                    <i class="fas fa-plus"></i> إضافة فئة جديدة
                </button>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?= $error ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?= $success ?>
                </div>
            <?php endif; ?>

            <!-- جدول الفئات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">قائمة الفئات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الصورة</th>
                                    <th>الاسم</th>
                                    <th>الفئة الرئيسية</th>
                                    <th>الأيقونة</th>
                                    <th>عدد الإعلانات</th>
                                    <th>الترتيب</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($categories as $category): ?>
                                    <tr>
                                        <td>
                                            <?php if ($category['image_path']): ?>
                                                <img src="../uploads/<?= $category['image_path'] ?>" 
                                                     class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-light d-flex align-items-center justify-content-center" 
                                                     style="width: 50px; height: 50px;">
                                                    <i class="<?= $category['icon'] ?: 'fas fa-tag' ?> text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="fw-bold"><?= $category['name'] ?></div>
                                            <?php if ($category['description']): ?>
                                                <small class="text-muted"><?= substr($category['description'], 0, 50) ?>...</small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= $category['parent_name'] ?: 'فئة رئيسية' ?></td>
                                        <td>
                                            <?php if ($category['icon']): ?>
                                                <i class="<?= $category['icon'] ?> fa-lg"></i>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?= $category['ads_count'] ?></span>
                                            <?php if ($category['subcategories_count'] > 0): ?>
                                                <br><small class="text-muted"><?= $category['subcategories_count'] ?> فئة فرعية</small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= $category['sort_order'] ?></td>
                                        <td>
                                            <span class="badge bg-<?= $category['is_active'] ? 'success' : 'secondary' ?>">
                                                <?= $category['is_active'] ? 'نشط' : 'غير نشط' ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary edit-category-btn" 
                                                        data-category='<?= json_encode($category) ?>'>
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-danger delete-category-btn" 
                                                        data-id="<?= $category['id'] ?>" 
                                                        data-name="<?= $category['name'] ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Modal إضافة فئة -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة فئة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <input type="hidden" name="action" value="add_category">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="name" name="name" required>
                                <label for="name">اسم الفئة *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="slug" name="slug">
                                <label for="slug">الرابط المختصر</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-floating mb-3">
                        <textarea class="form-control" id="description" name="description" style="height: 100px"></textarea>
                        <label for="description">الوصف</label>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="parent_id" name="parent_id">
                                    <option value="">فئة رئيسية</option>
                                    <?php foreach ($parent_categories as $parent): ?>
                                        <option value="<?= $parent['id'] ?>"><?= $parent['name'] ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <label for="parent_id">الفئة الرئيسية</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="number" class="form-control" id="sort_order" name="sort_order" value="0">
                                <label for="sort_order">ترتيب العرض</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="icon" name="icon" placeholder="fas fa-tag">
                                <label for="icon">الأيقونة (Font Awesome)</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="image" class="form-label">صورة الفئة</label>
                                <input type="file" class="form-control" id="image" name="image" accept="image/*">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة الفئة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تعديل فئة -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الفئة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data" id="editCategoryForm">
                <input type="hidden" name="action" value="edit_category">
                <input type="hidden" name="id" id="edit_id">
                <input type="hidden" name="current_image" id="edit_current_image">
                <div class="modal-body">
                    <!-- نفس الحقول مع بادئة edit_ -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تعديل فئة
    document.querySelectorAll('.edit-category-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const category = JSON.parse(this.getAttribute('data-category'));
            // ملء النموذج بالبيانات
            document.getElementById('edit_id').value = category.id;
            // ... باقي الحقول
            
            const modal = new bootstrap.Modal(document.getElementById('editCategoryModal'));
            modal.show();
        });
    });
    
    // حذف فئة
    document.querySelectorAll('.delete-category-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            
            if (confirm(`هل تريد حذف الفئة "${name}"؟`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_category">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
