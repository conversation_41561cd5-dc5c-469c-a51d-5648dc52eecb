<?php
// إصلاح جدول البلاغات
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h2>🔧 إصلاح جدول البلاغات</h2>";

try {
    // فحص الجدول الحالي
    echo "<h3>1. فحص الجدول الحالي:</h3>";
    $current_structure = $db->fetchAll("PRAGMA table_info(reports)");
    
    if ($current_structure) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>اسم العمود</th><th>النوع</th><th>NOT NULL</th><th>القيمة الافتراضية</th></tr>";
        foreach ($current_structure as $col) {
            echo "<tr>";
            echo "<td>{$col['name']}</td>";
            echo "<td>{$col['type']}</td>";
            echo "<td>" . ($col['notnull'] ? 'نعم' : 'لا') . "</td>";
            echo "<td>{$col['dflt_value']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // التحقق من وجود العمود المطلوب
        $has_ad_id = false;
        $has_reported_ad_id = false;
        
        foreach ($current_structure as $col) {
            if ($col['name'] === 'ad_id') {
                $has_ad_id = true;
            }
            if ($col['name'] === 'reported_ad_id') {
                $has_reported_ad_id = true;
            }
        }
        
        echo "<h3>2. تحليل المشكلة:</h3>";
        echo "<p>العمود ad_id موجود: " . ($has_ad_id ? "✅ نعم" : "❌ لا") . "</p>";
        echo "<p>العمود reported_ad_id موجود: " . ($has_reported_ad_id ? "✅ نعم" : "❌ لا") . "</p>";
        
        if ($has_ad_id && !$has_reported_ad_id) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>⚠️ المشكلة محددة:</h4>";
            echo "<p>الجدول يحتوي على عمود <code>ad_id</code> لكن الكود يحاول الإدراج في <code>reported_ad_id</code></p>";
            echo "<p>الحل: إما تغيير الكود أو تعديل الجدول</p>";
            echo "</div>";
            
            echo "<h3>3. تطبيق الحل:</h3>";
            
            // الحل الأول: إضافة العمود الجديد ونسخ البيانات
            echo "<h4>الخطوة 1: إضافة العمود الجديد</h4>";
            try {
                $db->query("ALTER TABLE reports ADD COLUMN reported_ad_id INTEGER");
                echo "✅ تم إضافة العمود reported_ad_id<br>";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'duplicate column name') !== false) {
                    echo "ℹ️ العمود reported_ad_id موجود بالفعل<br>";
                } else {
                    echo "❌ خطأ في إضافة العمود: " . $e->getMessage() . "<br>";
                }
            }
            
            echo "<h4>الخطوة 2: نسخ البيانات من ad_id إلى reported_ad_id</h4>";
            try {
                $db->query("UPDATE reports SET reported_ad_id = ad_id WHERE reported_ad_id IS NULL");
                echo "✅ تم نسخ البيانات<br>";
            } catch (Exception $e) {
                echo "❌ خطأ في نسخ البيانات: " . $e->getMessage() . "<br>";
            }
            
            echo "<h4>الخطوة 3: إنشاء جدول جديد بالهيكل الصحيح</h4>";
            try {
                // إنشاء جدول مؤقت بالهيكل الصحيح
                $db->query("
                    CREATE TABLE reports_new (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        reporter_id INTEGER NOT NULL,
                        reported_ad_id INTEGER NOT NULL,
                        report_type TEXT NOT NULL,
                        reason TEXT,
                        status TEXT DEFAULT 'pending',
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(id),
                        FOREIGN KEY (reporter_id) REFERENCES users(id),
                        FOREIGN KEY (reported_ad_id) REFERENCES ads(id)
                    )
                ");
                echo "✅ تم إنشاء الجدول الجديد<br>";
                
                // نسخ البيانات
                $db->query("
                    INSERT INTO reports_new (id, user_id, reporter_id, reported_ad_id, report_type, reason, status, created_at)
                    SELECT id, user_id, reporter_id, 
                           COALESCE(reported_ad_id, ad_id) as reported_ad_id, 
                           report_type, reason, status, created_at
                    FROM reports
                ");
                echo "✅ تم نسخ البيانات للجدول الجديد<br>";
                
                // حذف الجدول القديم
                $db->query("DROP TABLE reports");
                echo "✅ تم حذف الجدول القديم<br>";
                
                // إعادة تسمية الجدول الجديد
                $db->query("ALTER TABLE reports_new RENAME TO reports");
                echo "✅ تم إعادة تسمية الجدول<br>";
                
            } catch (Exception $e) {
                echo "❌ خطأ في إعادة إنشاء الجدول: " . $e->getMessage() . "<br>";
            }
            
        } elseif ($has_reported_ad_id && !$has_ad_id) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>✅ الجدول صحيح:</h4>";
            echo "<p>الجدول يحتوي على العمود الصحيح <code>reported_ad_id</code></p>";
            echo "</div>";
        } elseif ($has_ad_id && $has_reported_ad_id) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>⚠️ الجدول يحتوي على العمودين:</h4>";
            echo "<p>سيتم استخدام <code>reported_ad_id</code> وحذف <code>ad_id</code></p>";
            echo "</div>";
        }
        
        // فحص الجدول بعد الإصلاح
        echo "<h3>4. فحص الجدول بعد الإصلاح:</h3>";
        $new_structure = $db->fetchAll("PRAGMA table_info(reports)");
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>اسم العمود</th><th>النوع</th><th>NOT NULL</th></tr>";
        foreach ($new_structure as $col) {
            $highlight = ($col['name'] === 'reported_ad_id') ? 'background: #d4edda;' : '';
            echo "<tr style='$highlight'>";
            echo "<td>{$col['name']}</td>";
            echo "<td>{$col['type']}</td>";
            echo "<td>" . ($col['notnull'] ? 'نعم' : 'لا') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // اختبار الإدراج
        echo "<h3>5. اختبار الإدراج:</h3>";
        try {
            // جلب أول مستخدم وإعلان للاختبار
            $user = $db->fetch("SELECT id FROM users LIMIT 1");
            $ad = $db->fetch("SELECT id FROM ads LIMIT 1");
            
            if ($user && $ad) {
                $test_insert = $db->query("
                    INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, created_at) 
                    VALUES (?, ?, ?, ?, ?, datetime('now'))
                ", [$user['id'], $user['id'], $ad['id'], 'test', 'اختبار الإصلاح']);
                
                echo "✅ تم اختبار الإدراج بنجاح<br>";
                
                // حذف البيانات التجريبية
                $db->query("DELETE FROM reports WHERE reason = 'اختبار الإصلاح'");
                echo "✅ تم حذف البيانات التجريبية<br>";
                
            } else {
                echo "⚠️ لا توجد بيانات مستخدمين أو إعلانات للاختبار<br>";
            }
        } catch (Exception $e) {
            echo "❌ خطأ في اختبار الإدراج: " . $e->getMessage() . "<br>";
        }
        
    } else {
        echo "❌ جدول reports غير موجود<br>";
        
        // إنشاء الجدول من الصفر
        echo "<h3>إنشاء جدول reports:</h3>";
        try {
            $db->query("
                CREATE TABLE reports (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    reporter_id INTEGER NOT NULL,
                    reported_ad_id INTEGER NOT NULL,
                    report_type TEXT NOT NULL,
                    reason TEXT,
                    status TEXT DEFAULT 'pending',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    FOREIGN KEY (reporter_id) REFERENCES users(id),
                    FOREIGN KEY (reported_ad_id) REFERENCES ads(id)
                )
            ");
            echo "✅ تم إنشاء جدول reports بنجاح<br>";
        } catch (Exception $e) {
            echo "❌ خطأ في إنشاء الجدول: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ تم الانتهاء من الإصلاح!</h4>";
    echo "<p>يمكنك الآن اختبار البلاغات من صفحة تفاصيل الإعلان</p>";
    echo "<a href='pages/ad-details.php?id=4' target='_blank' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>اختبار البلاغ</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ خطأ عام:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<a href='debug.php'>العودة للتشخيص</a> | ";
echo "<a href='test-reports.php'>اختبار البلاغات</a> | ";
echo "<a href='fixes-summary.php'>ملخص الإصلاحات</a>";
?>
