<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// إنشاء كائن المصادقة
$auth = new Auth($db);

// جلب معرف الإعلان
$ad_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($ad_id <= 0) {
    header('Location: home.php');
    exit();
}

$error = '';
$success = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            $action = $_POST['action'];

            switch ($action) {
                case 'report_ad':
                    if (isLoggedIn()) {
                        $reason = sanitize($_POST['reason'] ?? '');
                        $description = sanitize($_POST['description'] ?? '');

                        if (!empty($reason)) {
                            // التحقق من وجود جدول reports
                            try {
                                $db->query("INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, created_at) VALUES (?, ?, ?, ?, ?, NOW())",
                                          [$_SESSION['user_id'], $_SESSION['user_id'], $ad_id, $reason, $description]);
                                $success = 'تم إرسال البلاغ بنجاح';
                            } catch (Exception $e) {
                                $error = 'خطأ في إرسال البلاغ: ' . $e->getMessage();
                            }
                        } else {
                            $error = 'يجب اختيار سبب البلاغ';
                        }
                    } else {
                        $error = 'يجب تسجيل الدخول أولاً';
                    }
                    break;

                case 'send_message':
                    if (isLoggedIn()) {
                        $receiver_id = (int)($_POST['receiver_id'] ?? 0);
                        $message = sanitize($_POST['message'] ?? '');

                        if (!empty($message) && $receiver_id > 0) {
                            try {
                                $db->query("INSERT INTO messages (sender_id, receiver_id, ad_id, message, created_at) VALUES (?, ?, ?, ?, NOW())",
                                          [$_SESSION['user_id'], $receiver_id, $ad_id, $message]);
                                $success = 'تم إرسال الرسالة بنجاح';
                            } catch (Exception $e) {
                                $error = 'خطأ في إرسال الرسالة: ' . $e->getMessage();
                            }
                        } else {
                            $error = 'يجب كتابة نص الرسالة';
                        }
                    } else {
                        $error = 'يجب تسجيل الدخول أولاً';
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        $error = 'حدث خطأ: ' . $e->getMessage();
    }
}

// جلب تفاصيل الإعلان
$sql = "
    SELECT a.*, u.username, u.full_name, u.phone as user_phone, u.city as user_city, u.email as user_email,
           c.name as category_name, c.slug as category_slug,
           (SELECT COUNT(*) FROM favorites WHERE ad_id = a.id) as favorites_count
    FROM ads a
    JOIN users u ON a.user_id = u.id
    JOIN categories c ON a.category_id = c.id
    WHERE a.id = ? AND a.status = 'active'
";

$ad = $db->fetch($sql, [$ad_id]);

if (!$ad) {
    header('Location: home.php');
    exit();
}

// تحديث عدد المشاهدات
$db->query("UPDATE ads SET views_count = views_count + 1 WHERE id = ?", [$ad_id]);

// التحقق من المفضلة للمستخدم الحالي
$is_favorite = false;
if (isLoggedIn()) {
    $favorite_check = $db->fetch("SELECT id FROM favorites WHERE user_id = ? AND ad_id = ?", [$_SESSION['user_id'], $ad_id]);
    $is_favorite = !empty($favorite_check);
}

// جلب صور الإعلان
$images = $db->fetchAll("SELECT * FROM ad_images WHERE ad_id = ? ORDER BY is_primary DESC, sort_order", [$ad_id]);

// جلب إعلانات مشابهة
$similar_ads = $db->fetchAll("
    SELECT a.*, u.username, c.name as category_name,
           (SELECT image_path FROM ad_images WHERE ad_id = a.id AND is_primary = 1 LIMIT 1) as primary_image
    FROM ads a
    JOIN users u ON a.user_id = u.id
    JOIN categories c ON a.category_id = c.id
    WHERE a.category_id = ? AND a.id != ? AND a.status = 'active'
    ORDER BY a.created_at DESC
    LIMIT 6
", [$ad['category_id'], $ad_id]);

// التحقق من المفضلة
$is_favorite = false;
if (isLoggedIn()) {
    $favorite = $db->fetch("SELECT id FROM favorites WHERE user_id = ? AND ad_id = ?", [$_SESSION['user_id'], $ad_id]);
    $is_favorite = $favorite !== false;
}

$page_title = $ad['title'];
$page_description = substr($ad['description'], 0, 160);
include 'includes/header.php';
?>

<div class="container mt-4">
    <!-- عرض الرسائل -->
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle"></i> <?= $error ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?= $success ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="home.php">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="home.php?category=<?= $ad['category_id'] ?>"><?= $ad['category_name'] ?></a></li>
            <li class="breadcrumb-item active"><?= $ad['title'] ?></li>
        </ol>
    </nav>

    <div class="row">
        <!-- تفاصيل الإعلان -->
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-body">
                    <!-- عنوان الإعلان -->
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h1 class="h3 mb-0"><?= $ad['title'] ?></h1>
                        <?php if ($ad['is_featured']): ?>
                            <span class="badge bg-warning text-dark">
                                <i class="fas fa-star"></i> مميز
                            </span>
                        <?php endif; ?>
                    </div>

                    <!-- معلومات أساسية -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-tag text-primary me-2"></i>
                                <span class="fw-bold">السعر:</span>
                                <span class="ms-2 text-success fs-4 fw-bold"><?= formatPrice($ad['price']) ?></span>
                                <?php if ($ad['price_type'] === 'negotiable'): ?>
                                    <small class="text-muted ms-1">(قابل للتفاوض)</small>
                                <?php elseif ($ad['price_type'] === 'free'): ?>
                                    <small class="text-success ms-1">(مجاني)</small>
                                <?php endif; ?>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                <span class="fw-bold">الموقع:</span>
                                <span class="ms-2"><?= $ad['city'] ?>, <?= $ad['region'] ?></span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-clock text-primary me-2"></i>
                                <span class="fw-bold">تاريخ النشر:</span>
                                <span class="ms-2"><?= formatDateArabic($ad['created_at']) ?></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-info-circle text-primary me-2"></i>
                                <span class="fw-bold">الحالة:</span>
                                <span class="ms-2">
                                    <?php
                                    $condition_labels = [
                                        'new' => 'جديد',
                                        'used' => 'مستعمل',
                                        'refurbished' => 'مجدد'
                                    ];
                                    echo $condition_labels[$ad['condition_type']] ?? $ad['condition_type'];
                                    ?>
                                </span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-eye text-primary me-2"></i>
                                <span class="fw-bold">المشاهدات:</span>
                                <span class="ms-2"><?= number_format($ad['views_count']) ?></span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-heart text-primary me-2"></i>
                                <span class="fw-bold">المفضلة:</span>
                                <span class="ms-2"><?= number_format($ad['favorites_count']) ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- معرض الصور -->
                    <?php if (!empty($images)): ?>
                    <div class="mb-4">
                        <h5>الصور</h5>
                        <div class="row">
                            <?php foreach ($images as $index => $image): ?>
                            <div class="col-md-4 mb-3">
                                <img src="../uploads/<?= $image['image_path'] ?>" 
                                     class="img-fluid rounded shadow-sm" 
                                     alt="صورة الإعلان"
                                     style="height: 200px; object-fit: cover; width: 100%; cursor: pointer;"
                                     data-bs-toggle="modal" 
                                     data-bs-target="#imageModal"
                                     data-bs-image="../uploads/<?= $image['image_path'] ?>">
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- الوصف -->
                    <div class="mb-4">
                        <h5>الوصف</h5>
                        <div class="bg-light p-3 rounded">
                            <?= nl2br(htmlspecialchars($ad['description'])) ?>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="d-flex gap-2 flex-wrap">
                        <?php if (isLoggedIn()): ?>
                            <button class="btn <?= $is_favorite ? 'btn-danger' : 'btn-outline-danger' ?> add-to-favorites" 
                                    data-ad-id="<?= $ad_id ?>">
                                <i class="<?= $is_favorite ? 'fas' : 'far' ?> fa-heart"></i>
                                <?= $is_favorite ? 'مضاف للمفضلة' : 'أضف للمفضلة' ?>
                            </button>
                        <?php endif; ?>
                        
                        <button class="btn btn-outline-primary share-ad">
                            <i class="fas fa-share-alt"></i> مشاركة
                        </button>
                        
                        <button class="btn btn-outline-warning report-ad" data-ad-id="<?= $ad_id ?>">
                            <i class="fas fa-flag"></i> إبلاغ
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات البائع -->
        <div class="col-lg-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user"></i> معلومات البائع</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                             style="width: 60px; height: 60px; font-size: 24px;">
                            <i class="fas fa-user"></i>
                        </div>
                        <h6 class="mt-2 mb-0"><?= $ad['full_name'] ?></h6>
                        <small class="text-muted">@<?= $ad['username'] ?></small>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">من:</small>
                        <div><?= $ad['user_city'] ?></div>
                    </div>

                    <!-- معلومات التواصل -->
                    <?php if ($ad['contact_phone'] || $ad['contact_email'] || $ad['whatsapp']): ?>
                    <div class="border-top pt-3">
                        <h6>معلومات التواصل</h6>
                        
                        <?php if ($ad['contact_phone']): ?>
                        <div class="mb-2">
                            <a href="tel:<?= $ad['contact_phone'] ?>" class="btn btn-outline-primary btn-sm w-100">
                                <i class="fas fa-phone"></i> <?= $ad['contact_phone'] ?>
                            </a>
                        </div>
                        <?php endif; ?>

                        <?php if ($ad['whatsapp']): ?>
                        <div class="mb-2">
                            <a href="https://wa.me/<?= preg_replace('/[^0-9]/', '', $ad['whatsapp']) ?>" 
                               target="_blank" class="btn btn-success btn-sm w-100">
                                <i class="fab fa-whatsapp"></i> واتساب
                            </a>
                        </div>
                        <?php endif; ?>

                        <?php if ($ad['contact_email']): ?>
                        <div class="mb-2">
                            <a href="mailto:<?= $ad['contact_email'] ?>?subject=استفسار عن إعلان: <?= urlencode($ad['title']) ?>&body=مرحباً، أود الاستفسار عن الإعلان: <?= urlencode($ad['title']) ?>"
                               class="btn btn-outline-secondary btn-sm w-100">
                                <i class="fas fa-envelope"></i> بريد إلكتروني
                            </a>
                        </div>
                        <?php elseif ($ad['user_email']): ?>
                        <div class="mb-2">
                            <a href="mailto:<?= $ad['user_email'] ?>?subject=استفسار عن إعلان: <?= urlencode($ad['title']) ?>&body=مرحباً، أود الاستفسار عن الإعلان: <?= urlencode($ad['title']) ?>"
                               class="btn btn-outline-secondary btn-sm w-100">
                                <i class="fas fa-envelope"></i> راسل البائع
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>

                    <?php if (isLoggedIn() && $_SESSION['user_id'] != $ad['user_id']): ?>
                    <div class="border-top pt-3">
                        <button class="btn btn-primary w-100 mb-2" data-bs-toggle="modal" data-bs-target="#messageModal">
                            <i class="fas fa-comment"></i> راسل البائع
                        </button>

                        <div class="row">
                            <div class="col-6">
                                <button class="btn <?= $is_favorite ? 'btn-danger' : 'btn-outline-danger' ?> w-100 add-to-favorites" data-ad-id="<?= $ad_id ?>">
                                    <i class="<?= $is_favorite ? 'fas' : 'far' ?> fa-heart"></i>
                                    <?= $is_favorite ? 'مضاف للمفضلة' : 'أضف للمفضلة' ?>
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-warning w-100 report-ad" data-ad-id="<?= $ad_id ?>">
                                    <i class="fas fa-flag"></i> إبلاغ
                                </button>
                            </div>
                        </div>
                    </div>
                    <?php elseif (!isLoggedIn()): ?>
                    <div class="border-top pt-3">
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i>
                            <a href="login.php">سجل الدخول</a> للتواصل مع البائع وإضافة الإعلان للمفضلة
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- إعلانات مشابهة -->
            <?php if (!empty($similar_ads)): ?>
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-th-large"></i> إعلانات مشابهة</h5>
                </div>
                <div class="card-body">
                    <?php foreach ($similar_ads as $similar): ?>
                    <div class="d-flex mb-3 pb-3 border-bottom">
                        <div class="flex-shrink-0 me-3">
                            <?php if ($similar['primary_image']): ?>
                                <img src="../uploads/<?= $similar['primary_image'] ?>" 
                                     class="rounded" style="width: 60px; height: 60px; object-fit: cover;" 
                                     alt="<?= $similar['title'] ?>">
                            <?php else: ?>
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                     style="width: 60px; height: 60px;">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">
                                <a href="ad-details.php?id=<?= $similar['id'] ?>" class="text-decoration-none">
                                    <?= substr($similar['title'], 0, 50) ?>...
                                </a>
                            </h6>
                            <div class="text-success fw-bold"><?= formatPrice($similar['price']) ?></div>
                            <small class="text-muted"><?= timeAgo($similar['created_at']) ?></small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modal للصور -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">صورة الإعلان</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid" alt="صورة الإعلان">
            </div>
        </div>
    </div>
</div>

<!-- Modal للرسائل -->
<div class="modal fade" id="messageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إرسال رسالة للبائع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="send_message">
                <input type="hidden" name="receiver_id" value="<?= $ad['user_id'] ?>">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        ستقوم بإرسال رسالة إلى <strong><?= $ad['full_name'] ?></strong> بخصوص إعلان <strong><?= $ad['title'] ?></strong>
                    </div>
                    <div class="form-floating">
                        <textarea class="form-control" name="message" style="height: 120px" required placeholder="اكتب رسالتك هنا..."></textarea>
                        <label>نص الرسالة</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إرسال الرسالة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal للإبلاغ -->
<div class="modal fade" id="reportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إبلاغ عن الإعلان</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="report_ad">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reason" class="form-label">سبب الإبلاغ</label>
                        <select class="form-select" name="reason" required>
                            <option value="">اختر السبب</option>
                            <option value="spam">رسائل مزعجة</option>
                            <option value="inappropriate">محتوى غير مناسب</option>
                            <option value="fake">إعلان مزيف</option>
                            <option value="fraud">احتيال</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">تفاصيل إضافية (اختياري)</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">إرسال البلاغ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // معرض الصور
    const imageModal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');

    imageModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const imageSrc = button.getAttribute('data-bs-image');
        modalImage.src = imageSrc;
    });

    // إضافة/إزالة من المفضلة
    document.querySelectorAll('.add-to-favorites').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const adId = this.getAttribute('data-ad-id');

            fetch('ajax/toggle-favorite.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ad_id: adId})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const icon = this.querySelector('i');
                    if (data.is_favorite) {
                        this.className = 'btn btn-danger add-to-favorites';
                        icon.className = 'fas fa-heart';
                        this.innerHTML = '<i class="fas fa-heart"></i> مضاف للمفضلة';
                    } else {
                        this.className = 'btn btn-outline-danger add-to-favorites';
                        icon.className = 'far fa-heart';
                        this.innerHTML = '<i class="far fa-heart"></i> أضف للمفضلة';
                    }

                    // إظهار رسالة
                    const alert = document.createElement('div');
                    alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
                    alert.style.top = '20px';
                    alert.style.right = '20px';
                    alert.style.zIndex = '9999';
                    alert.innerHTML = `
                        ${data.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(alert);

                    setTimeout(() => {
                        alert.remove();
                    }, 3000);
                } else {
                    alert('خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء العملية');
            });
        });
    });

    // إبلاغ عن الإعلان
    document.querySelectorAll('.report-ad').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const modal = new bootstrap.Modal(document.getElementById('reportModal'));
            modal.show();
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
