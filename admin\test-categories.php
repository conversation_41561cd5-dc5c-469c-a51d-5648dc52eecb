<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

echo "<h2>🔍 اختبار إدارة الفئات</h2>";

try {
    require_once $root_path . '/config/database.php';
    echo "✅ تم تحميل database.php<br>";
    
    require_once $root_path . '/config/config.php';
    echo "✅ تم تحميل config.php<br>";
    
    require_once $root_path . '/includes/functions.php';
    echo "✅ تم تحميل functions.php<br>";
    
    require_once $root_path . '/includes/auth.php';
    echo "✅ تم تحميل auth.php<br>";
    
    // اختبار قاعدة البيانات
    if (isset($db)) {
        echo "✅ كائن قاعدة البيانات موجود<br>";
        
        // اختبار جدول الفئات
        $categories = $db->fetchAll("SELECT * FROM categories LIMIT 5");
        echo "✅ جدول الفئات يعمل - عدد الفئات: " . count($categories) . "<br>";
        
        // اختبار دالة uploadImage
        if (function_exists('uploadImage')) {
            echo "✅ دالة uploadImage موجودة<br>";
        } else {
            echo "❌ دالة uploadImage غير موجودة<br>";
        }
        
        // اختبار مجلد uploads
        $uploads_dir = $root_path . '/uploads/categories/';
        if (!is_dir($uploads_dir)) {
            if (mkdir($uploads_dir, 0755, true)) {
                echo "✅ تم إنشاء مجلد uploads/categories/<br>";
            } else {
                echo "❌ فشل في إنشاء مجلد uploads/categories/<br>";
            }
        } else {
            echo "✅ مجلد uploads/categories/ موجود<br>";
        }
        
        // اختبار الصلاحيات
        if (is_writable($uploads_dir)) {
            echo "✅ مجلد uploads/categories/ قابل للكتابة<br>";
        } else {
            echo "❌ مجلد uploads/categories/ غير قابل للكتابة<br>";
        }
        
    } else {
        echo "❌ كائن قاعدة البيانات غير موجود<br>";
    }
    
    // اختبار الجلسة
    if (isset($_SESSION['user_id'])) {
        echo "✅ المستخدم مسجل الدخول - ID: " . $_SESSION['user_id'] . "<br>";
        
        if (isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'admin') {
            echo "✅ المستخدم إدمن<br>";
        } else {
            echo "❌ المستخدم ليس إدمن<br>";
        }
    } else {
        echo "❌ المستخدم غير مسجل الدخول<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h3>🔗 روابط الاختبار:</h3>";
echo "<ul>";
echo "<li><a href='index.php'>لوحة الإدارة الرئيسية</a></li>";
echo "<li><a href='categories.php'>إدارة الفئات</a></li>";
echo "<li><a href='../pages/login.php'>تسجيل الدخول</a></li>";
echo "</ul>";

echo "<h3>📝 معلومات النظام:</h3>";
echo "<ul>";
echo "<li><strong>PHP Version:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>Upload Max Filesize:</strong> " . ini_get('upload_max_filesize') . "</li>";
echo "<li><strong>Post Max Size:</strong> " . ini_get('post_max_size') . "</li>";
echo "<li><strong>Memory Limit:</strong> " . ini_get('memory_limit') . "</li>";
echo "</ul>";
?>
