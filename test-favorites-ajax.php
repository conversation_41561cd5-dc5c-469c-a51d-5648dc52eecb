<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

// تسجيل دخول تلقائي
$user = $db->fetch("SELECT * FROM users LIMIT 1");
if ($user) {
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['logged_in'] = true;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المفضلة AJAX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1><i class="fas fa-heart text-danger"></i> اختبار المفضلة AJAX</h1>
        
        <?php if (isset($_SESSION['user_id'])): ?>
            <div class="alert alert-info">
                <i class="fas fa-user"></i> مسجل الدخول كـ: <strong><?= $_SESSION['username'] ?></strong>
            </div>
            
            <?php
            $test_ads = $db->fetchAll("SELECT id, title FROM ads LIMIT 4");
            if (empty($test_ads)):
            ?>
                <div class="alert alert-danger">لا توجد إعلانات للاختبار</div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($test_ads as $ad): ?>
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title"><?= htmlspecialchars($ad['title']) ?></h5>
                                    <p class="card-text">إعلان رقم: <?= $ad['id'] ?></p>
                                    
                                    <button class="btn btn-outline-danger" onclick="toggleFavorite(<?= $ad['id'] ?>, this)">
                                        <i class="fas fa-heart"></i> إضافة/إزالة من المفضلة
                                    </button>
                                    
                                    <div id="result-<?= $ad['id'] ?>" class="mt-2"></div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-list"></i> سجل الاختبارات</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-log" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;"></div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <button class="btn btn-success" onclick="runAllTests()">
                        <i class="fas fa-play"></i> تشغيل جميع الاختبارات
                    </button>
                    <button class="btn btn-warning" onclick="clearLog()">
                        <i class="fas fa-trash"></i> مسح السجل
                    </button>
                </div>
            <?php endif; ?>
            
        <?php else: ?>
            <div class="alert alert-danger">
                <h4>خطأ في تسجيل الدخول</h4>
                <p>لا يمكن اختبار المفضلة بدون تسجيل الدخول</p>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                'success': '#28a745',
                'error': '#dc3545',
                'info': '#17a2b8',
                'warning': '#ffc107'
            };
            
            logDiv.innerHTML += `<div style="color: ${colors[type]}; margin: 5px 0;">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }
        
        function toggleFavorite(adId, button) {
            const resultDiv = document.getElementById(`result-${adId}`);
            
            log(`بدء اختبار المفضلة للإعلان ${adId}`, 'info');
            
            // تعطيل الزر أثناء الطلب
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
            
            // إرسال طلب AJAX
            fetch('pages/ajax/toggle-favorite.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    ad_id: adId
                })
            })
            .then(response => {
                log(`استجابة الخادم: ${response.status} ${response.statusText}`, 'info');
                return response.text();
            })
            .then(data => {
                log(`البيانات المستلمة: ${data}`, 'info');
                
                try {
                    const jsonData = JSON.parse(data);
                    
                    if (jsonData.success) {
                        resultDiv.innerHTML = `<div class="alert alert-success">✅ ${jsonData.message}</div>`;
                        log(`نجح اختبار الإعلان ${adId}: ${jsonData.message}`, 'success');
                    } else {
                        resultDiv.innerHTML = `<div class="alert alert-danger">❌ ${jsonData.message}</div>`;
                        log(`فشل اختبار الإعلان ${adId}: ${jsonData.message}`, 'error');
                    }
                } catch (e) {
                    resultDiv.innerHTML = `<div class="alert alert-warning">⚠️ استجابة غير صحيحة: ${data}</div>`;
                    log(`خطأ في تحليل JSON للإعلان ${adId}: ${e.message}`, 'error');
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger">❌ خطأ في الشبكة: ${error.message}</div>`;
                log(`خطأ في الشبكة للإعلان ${adId}: ${error.message}`, 'error');
            })
            .finally(() => {
                // إعادة تفعيل الزر
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-heart"></i> إضافة/إزالة من المفضلة';
            });
        }
        
        function runAllTests() {
            log('بدء تشغيل جميع الاختبارات...', 'info');
            
            const buttons = document.querySelectorAll('button[onclick^="toggleFavorite"]');
            let delay = 0;
            
            buttons.forEach((button, index) => {
                setTimeout(() => {
                    button.click();
                }, delay);
                delay += 2000; // تأخير ثانيتين بين كل اختبار
            });
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل صفحة اختبار المفضلة', 'info');
            log('جاهز لبدء الاختبارات', 'success');
        });
    </script>
</body>
</html>
