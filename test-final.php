<?php
// اختبار نهائي لقاعدة البيانات
echo "<h1>🧪 اختبار نهائي لقاعدة البيانات</h1>";
echo "<style>
    body{font-family:Arial;margin:20px;background:#f5f5f5;} 
    .card{background:white;padding:20px;margin:10px 0;border-radius:8px;box-shadow:0 2px 4px rgba(0,0,0,0.1);}
    .success{color:green;} .error{color:red;} .info{color:blue;}
    table{width:100%;border-collapse:collapse;margin:10px 0;}
    th,td{border:1px solid #ddd;padding:8px;text-align:right;}
    th{background:#f8f9fa;}
</style>";

try {
    echo "<div class='card'>";
    echo "<h2>🔄 تحميل إعدادات قاعدة البيانات...</h2>";
    
    require_once 'config/database.php';
    
    echo "<p class='success'>✅ تم تحميل ملف قاعدة البيانات بنجاح!</p>";
    echo "<p class='success'>✅ تم إنشاء الاتصال بنجاح!</p>";
    echo "</div>";
    
    // اختبار جدول المستخدمين
    echo "<div class='card'>";
    echo "<h3>👥 جدول المستخدمين:</h3>";
    $users = $db->fetchAll("SELECT id, username, email, user_type, is_active, created_at FROM users");
    if (!empty($users)) {
        echo "<table>";
        echo "<tr><th>ID</th><th>اسم المستخدم</th><th>البريد</th><th>النوع</th><th>نشط</th><th>تاريخ الإنشاء</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>{$user['user_type']}</td>";
            echo "<td>" . ($user['is_active'] ? 'نعم' : 'لا') . "</td>";
            echo "<td>{$user['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p class='success'>✅ تم العثور على " . count($users) . " مستخدم</p>";
    } else {
        echo "<p class='info'>ℹ️ لا توجد مستخدمين حالياً</p>";
    }
    echo "</div>";
    
    // اختبار جدول الفئات
    echo "<div class='card'>";
    echo "<h3>📂 جدول الفئات:</h3>";
    $categories = $db->fetchAll("SELECT id, name, icon, is_active, sort_order FROM categories");
    if (!empty($categories)) {
        echo "<table>";
        echo "<tr><th>ID</th><th>الاسم</th><th>الأيقونة</th><th>نشط</th><th>الترتيب</th></tr>";
        foreach ($categories as $category) {
            echo "<tr>";
            echo "<td>{$category['id']}</td>";
            echo "<td>{$category['name']}</td>";
            echo "<td>{$category['icon']}</td>";
            echo "<td>" . ($category['is_active'] ? 'نعم' : 'لا') . "</td>";
            echo "<td>{$category['sort_order']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p class='success'>✅ تم العثور على " . count($categories) . " فئة</p>";
    } else {
        echo "<p class='info'>ℹ️ لا توجد فئات حالياً</p>";
    }
    echo "</div>";
    
    // اختبار جدول الإعلانات
    echo "<div class='card'>";
    echo "<h3>📢 جدول الإعلانات:</h3>";
    $ads = $db->fetchAll("SELECT COUNT(*) as count FROM ads");
    echo "<p class='info'>📊 عدد الإعلانات: " . $ads[0]['count'] . "</p>";
    echo "</div>";
    
    // اختبار تسجيل الدخول
    echo "<div class='card'>";
    echo "<h3>🔐 اختبار تسجيل الدخول:</h3>";
    $admin = $db->fetch("SELECT * FROM users WHERE username = ? AND user_type = ?", ['admin', 'admin']);
    if ($admin) {
        echo "<p class='success'>✅ تم العثور على حساب المدير</p>";
        echo "<p><strong>اسم المستخدم:</strong> admin</p>";
        echo "<p><strong>كلمة المرور:</strong> password</p>";
        echo "<p><strong>البريد:</strong> {$admin['email']}</p>";
    } else {
        echo "<p class='error'>❌ لم يتم العثور على حساب المدير</p>";
    }
    echo "</div>";
    
    echo "<div class='card' style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;'>";
    echo "<h2>🎉 تم الاختبار بنجاح!</h2>";
    echo "<p>✅ قاعدة البيانات SQLite تعمل بشكل مثالي</p>";
    echo "<p>✅ جميع الجداول تم إنشاؤها بنجاح</p>";
    echo "<p>✅ البيانات الأساسية تم إدراجها</p>";
    echo "<p>✅ يمكنك الآن استخدام الموقع</p>";
    echo "<br>";
    echo "<a href='pages/home.php' style='background: white; color: #667eea; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 الصفحة الرئيسية</a>";
    echo "<a href='pages/login.php' style='background: white; color: #667eea; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔐 تسجيل الدخول</a>";
    echo "<a href='admin/index.php' style='background: white; color: #667eea; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>👨‍💼 لوحة الإدارة</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='card' style='border: 2px solid red;'>";
    echo "<h3 class='error'>❌ خطأ في قاعدة البيانات:</h3>";
    echo "<p><strong>الرسالة:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>التتبع:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}
?>
