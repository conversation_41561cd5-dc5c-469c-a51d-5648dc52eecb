<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

echo "<h2>🔍 اختبار الاتصال بقاعدة البيانات</h2>";

try {
    echo "📁 مسار الجذر: " . $root_path . "<br>";
    
    // اختبار تحميل ملف الإعدادات
    $config_file = $root_path . '/config/config.php';
    if (file_exists($config_file)) {
        require_once $config_file;
        echo "✅ تم تحميل config.php<br>";
    } else {
        echo "❌ ملف config.php غير موجود في: $config_file<br>";
    }
    
    // اختبار تحميل ملف قاعدة البيانات
    $db_file = $root_path . '/config/database.php';
    if (file_exists($db_file)) {
        require_once $db_file;
        echo "✅ تم تحميل database.php<br>";
    } else {
        echo "❌ ملف database.php غير موجود في: $db_file<br>";
    }
    
    // اختبار الاتصال بقاعدة البيانات
    if (isset($db)) {
        echo "✅ تم إنشاء كائن قاعدة البيانات<br>";
        
        // اختبار استعلام بسيط
        $result = $db->query("SELECT 1 as test");
        if ($result) {
            echo "✅ الاتصال بقاعدة البيانات يعمل<br>";
            
            // اختبار جدول المستخدمين
            try {
                $users_count = $db->fetch("SELECT COUNT(*) as count FROM users");
                echo "✅ جدول المستخدمين موجود - عدد المستخدمين: " . $users_count['count'] . "<br>";
            } catch (Exception $e) {
                echo "❌ مشكلة في جدول المستخدمين: " . $e->getMessage() . "<br>";
            }
            
            // اختبار جدول الفئات
            try {
                $categories_count = $db->fetch("SELECT COUNT(*) as count FROM categories");
                echo "✅ جدول الفئات موجود - عدد الفئات: " . $categories_count['count'] . "<br>";
            } catch (Exception $e) {
                echo "❌ مشكلة في جدول الفئات: " . $e->getMessage() . "<br>";
            }
            
        } else {
            echo "❌ فشل في تنفيذ الاستعلام<br>";
        }
    } else {
        echo "❌ لم يتم إنشاء كائن قاعدة البيانات<br>";
    }
    
    // اختبار تحميل الدوال
    $functions_file = $root_path . '/includes/functions.php';
    if (file_exists($functions_file)) {
        require_once $functions_file;
        echo "✅ تم تحميل functions.php<br>";
        
        // اختبار دالة
        if (function_exists('sanitize')) {
            echo "✅ دالة sanitize متاحة<br>";
        } else {
            echo "❌ دالة sanitize غير متاحة<br>";
        }
    } else {
        echo "❌ ملف functions.php غير موجود<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h3>🔗 روابط الاختبار:</h3>";
echo "<ul>";
echo "<li><a href='home.php'>الصفحة الرئيسية</a></li>";
echo "<li><a href='login.php'>تسجيل الدخول</a></li>";
echo "<li><a href='register.php'>إنشاء حساب</a></li>";
echo "<li><a href='../admin/index.php'>لوحة الإدارة</a></li>";
echo "</ul>";
?>
