<?php
session_start();
require_once '../../config/database.php';
require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول']);
    exit();
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit();
}

// قراءة البيانات
$input = json_decode(file_get_contents('php://input'), true);
$image_id = isset($input['image_id']) ? (int)$input['image_id'] : 0;

if ($image_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف الصورة غير صحيح']);
    exit();
}

try {
    // جلب معلومات الصورة والتحقق من الملكية
    $image = $db->fetch("
        SELECT ai.*, a.user_id 
        FROM ad_images ai 
        JOIN ads a ON ai.ad_id = a.id 
        WHERE ai.id = ?
    ", [$image_id]);
    
    if (!$image) {
        echo json_encode(['success' => false, 'message' => 'الصورة غير موجودة']);
        exit();
    }
    
    // التحقق من ملكية الإعلان
    if ($image['user_id'] != $_SESSION['user_id']) {
        echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية لحذف هذه الصورة']);
        exit();
    }
    
    // حذف الصورة من الخادم
    $image_path = '../../uploads/' . $image['image_path'];
    if (file_exists($image_path)) {
        unlink($image_path);
    }
    
    // حذف الصورة من قاعدة البيانات
    $db->query("DELETE FROM ad_images WHERE id = ?", [$image_id]);
    
    // إذا كانت الصورة المحذوفة هي الرئيسية، جعل أول صورة متبقية رئيسية
    if ($image['is_primary']) {
        $db->query("
            UPDATE ad_images 
            SET is_primary = 1 
            WHERE ad_id = ? 
            ORDER BY sort_order 
            LIMIT 1
        ", [$image['ad_id']]);
    }
    
    echo json_encode(['success' => true, 'message' => 'تم حذف الصورة بنجاح']);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء حذف الصورة']);
}
?>
