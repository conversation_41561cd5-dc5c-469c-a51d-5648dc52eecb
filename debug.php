<?php
// تشخيص المشاكل
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 تشخيص المشاكل</h2>";

// اختبار PHP
echo "<h3>1. اختب<PERSON>ر PHP</h3>";
echo "✅ PHP يعمل - الإصدار: " . PHP_VERSION . "<br>";

// اختبار قاعدة البيانات
echo "<h3>2. اختبار قاعدة البيانات</h3>";
try {
    // تحميل إعدادات قاعدة البيانات
    require_once 'config/database.php';

    if (isset($db)) {
        echo "✅ الاتصال بقاعدة البيانات يعمل<br>";

        // فحص الجداول الموجودة
        $tables = $db->fetchAll("SELECT name FROM sqlite_master WHERE type='table'");
        echo "📋 الجداول الموجودة: ";
        foreach ($tables as $table) {
            echo $table['name'] . " ";
        }
        echo "<br>";

        // فحص جدول reports
        echo "<h4>فحص جدول reports:</h4>";
        try {
            $reports_columns = $db->fetchAll("PRAGMA table_info(reports)");
            if ($reports_columns) {
                echo "✅ جدول reports موجود<br>";
                echo "<table border='1'><tr><th>اسم العمود</th><th>النوع</th><th>NOT NULL</th></tr>";
                foreach ($reports_columns as $col) {
                    echo "<tr><td>{$col['name']}</td><td>{$col['type']}</td><td>" . ($col['notnull'] ? 'نعم' : 'لا') . "</td></tr>";
                }
                echo "</table>";
            } else {
                echo "❌ جدول reports غير موجود<br>";
            }
        } catch (Exception $e) {
            echo "❌ خطأ في فحص جدول reports: " . $e->getMessage() . "<br>";
        }

        // فحص جدول favorites
        echo "<h4>فحص جدول favorites:</h4>";
        try {
            $favorites_columns = $db->fetchAll("PRAGMA table_info(favorites)");
            if ($favorites_columns) {
                echo "✅ جدول favorites موجود<br>";
                echo "<table border='1'><tr><th>اسم العمود</th><th>النوع</th><th>NOT NULL</th></tr>";
                foreach ($favorites_columns as $col) {
                    echo "<tr><td>{$col['name']}</td><td>{$col['type']}</td><td>" . ($col['notnull'] ? 'نعم' : 'لا') . "</td></tr>";
                }
                echo "</table>";
            } else {
                echo "❌ جدول favorites غير موجود<br>";
            }
        } catch (Exception $e) {
            echo "❌ خطأ في فحص جدول favorites: " . $e->getMessage() . "<br>";
        }

    } else {
        echo "❌ لم يتم إنشاء كائن قاعدة البيانات<br>";
    }

} catch (Exception $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}

// اختبار الملفات
echo "<h3>3. اختبار الملفات</h3>";
$files_to_check = [
    'config/config.php',
    'config/database.php',
    'includes/functions.php',
    'includes/auth.php',
    'pages/includes/header.php',
    'pages/includes/footer.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✅ $file موجود<br>";
    } else {
        echo "❌ $file غير موجود<br>";
    }
}

// اختبار تحميل الملفات
echo "<h3>4. اختبار تحميل الملفات</h3>";
try {
    require_once 'config/config.php';
    echo "✅ تم تحميل config.php<br>";
} catch (Exception $e) {
    echo "❌ خطأ في config.php: " . $e->getMessage() . "<br>";
}

try {
    require_once 'config/database.php';
    echo "✅ تم تحميل database.php<br>";
    
    if (isset($db)) {
        echo "✅ تم إنشاء كائن قاعدة البيانات<br>";
    } else {
        echo "❌ لم يتم إنشاء كائن قاعدة البيانات<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في database.php: " . $e->getMessage() . "<br>";
}

try {
    require_once 'includes/functions.php';
    echo "✅ تم تحميل functions.php<br>";
} catch (Exception $e) {
    echo "❌ خطأ في functions.php: " . $e->getMessage() . "<br>";
}

// اختبار صفحة بسيطة
echo "<h3>5. اختبار صفحة بسيطة</h3>";
$simple_page_content = '<?php
session_start();
$root_path = dirname(__DIR__);
require_once $root_path . "/config/database.php";
echo "تعمل الصفحة بشكل صحيح!";
?>';

file_put_contents('pages/simple-test-page.php', $simple_page_content);
echo "✅ تم إنشاء صفحة اختبار بسيطة: <a href='pages/simple-test-page.php' target='_blank'>pages/simple-test-page.php</a><br>";

echo "<hr>";
echo "<h3>🔗 روابط الاختبار:</h3>";
echo "<ul>";
echo "<li><a href='pages/simple-test-page.php' target='_blank'>صفحة اختبار بسيطة</a></li>";
echo "<li><a href='create-database.php' target='_blank'>إنشاء قاعدة البيانات</a></li>";
echo "<li><a href='pages/login.php' target='_blank'>تسجيل الدخول</a></li>";
echo "</ul>";
?>
