<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

$error = '';
$success = '';

// معالجة إرسال الرسالة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = [
        'name' => sanitize($_POST['name'] ?? ''),
        'email' => sanitize($_POST['email'] ?? ''),
        'subject' => sanitize($_POST['subject'] ?? ''),
        'message' => sanitize($_POST['message'] ?? ''),
        'phone' => sanitize($_POST['phone'] ?? '')
    ];
    
    // التحقق من صحة البيانات
    if (empty($data['name']) || empty($data['email']) || empty($data['subject']) || empty($data['message'])) {
        $error = 'جميع الحقول المطلوبة يجب ملؤها';
    } elseif (!validateEmail($data['email'])) {
        $error = 'البريد الإلكتروني غير صحيح';
    } elseif (strlen($data['message']) < 10) {
        $error = 'الرسالة يجب أن تكون 10 أحرف على الأقل';
    } else {
        try {
            // حفظ الرسالة في قاعدة البيانات
            $sql = "INSERT INTO contact_messages (name, email, phone, subject, message, ip_address, user_agent, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $db->query($sql, [
                $data['name'],
                $data['email'],
                $data['phone'],
                $data['subject'],
                $data['message'],
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
            
            $success = 'تم إرسال رسالتك بنجاح. سنتواصل معك قريباً';
            
            // مسح البيانات بعد الإرسال الناجح
            $data = [];
            
        } catch (Exception $e) {
            $error = 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى';
        }
    }
}

$page_title = 'اتصل بنا';
$page_description = 'تواصل مع فريق حراجنا للاستفسارات والدعم الفني';
include 'includes/header.php';
?>

<div class="container mt-4">
    <!-- Hero Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body text-center py-5">
                    <h1 class="display-4 mb-3">اتصل بنا</h1>
                    <p class="lead">نحن هنا لمساعدتك. تواصل معنا في أي وقت</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- نموذج الاتصال -->
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h3 class="mb-0"><i class="fas fa-envelope"></i> أرسل لنا رسالة</h3>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i> <?= $error ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> <?= $success ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?= htmlspecialchars($data['name'] ?? '') ?>" required>
                                    <label for="name">الاسم الكامل *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?= htmlspecialchars($data['email'] ?? '') ?>" required>
                                    <label for="email">البريد الإلكتروني *</label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="<?= htmlspecialchars($data['phone'] ?? '') ?>">
                                    <label for="phone">رقم الهاتف (اختياري)</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="subject" name="subject" required>
                                        <option value="">اختر الموضوع</option>
                                        <option value="general" <?= ($data['subject'] ?? '') === 'general' ? 'selected' : '' ?>>استفسار عام</option>
                                        <option value="technical" <?= ($data['subject'] ?? '') === 'technical' ? 'selected' : '' ?>>مشكلة تقنية</option>
                                        <option value="account" <?= ($data['subject'] ?? '') === 'account' ? 'selected' : '' ?>>مشكلة في الحساب</option>
                                        <option value="ads" <?= ($data['subject'] ?? '') === 'ads' ? 'selected' : '' ?>>مشكلة في الإعلانات</option>
                                        <option value="payment" <?= ($data['subject'] ?? '') === 'payment' ? 'selected' : '' ?>>مشكلة في الدفع</option>
                                        <option value="suggestion" <?= ($data['subject'] ?? '') === 'suggestion' ? 'selected' : '' ?>>اقتراح أو تحسين</option>
                                        <option value="complaint" <?= ($data['subject'] ?? '') === 'complaint' ? 'selected' : '' ?>>شكوى</option>
                                        <option value="other" <?= ($data['subject'] ?? '') === 'other' ? 'selected' : '' ?>>أخرى</option>
                                    </select>
                                    <label for="subject">الموضوع *</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-floating mb-3">
                            <textarea class="form-control" id="message" name="message" 
                                      style="height: 150px" maxlength="1000" required><?= htmlspecialchars($data['message'] ?? '') ?></textarea>
                            <label for="message">الرسالة *</label>
                            <div class="form-text">الحد الأدنى 10 أحرف، الحد الأقصى 1000 حرف</div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane"></i> إرسال الرسالة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- معلومات الاتصال -->
        <div class="col-lg-4">
            <!-- معلومات التواصل -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> معلومات التواصل</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <i class="fas fa-envelope text-primary fa-2x"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">البريد الإلكتروني</h6>
                            <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                        </div>
                    </div>

                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <i class="fas fa-phone text-success fa-2x"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">الهاتف</h6>
                            <a href="tel:+966501234567" class="text-decoration-none">+966 50 123 4567</a>
                        </div>
                    </div>

                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <i class="fab fa-whatsapp text-success fa-2x"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">واتساب</h6>
                            <a href="https://wa.me/966501234567" target="_blank" class="text-decoration-none">+966 50 123 4567</a>
                        </div>
                    </div>

                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-map-marker-alt text-danger fa-2x"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">العنوان</h6>
                            <p class="mb-0 small">الرياض، المملكة العربية السعودية</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ساعات العمل -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-clock"></i> ساعات العمل</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <strong>الأحد - الخميس</strong>
                        </div>
                        <div class="col-6 text-end">
                            9:00 ص - 6:00 م
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <strong>الجمعة</strong>
                        </div>
                        <div class="col-6 text-end">
                            2:00 م - 6:00 م
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <strong>السبت</strong>
                        </div>
                        <div class="col-6 text-end">
                            مغلق
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3 mb-0">
                        <small>
                            <i class="fas fa-info-circle"></i>
                            الدعم الفني متاح 24/7 عبر الموقع
                        </small>
                    </div>
                </div>
            </div>

            <!-- وسائل التواصل الاجتماعي -->
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-share-alt"></i> تابعنا</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="#" class="btn btn-outline-primary">
                            <i class="fab fa-facebook-f"></i> فيسبوك
                        </a>
                        <a href="#" class="btn btn-outline-info">
                            <i class="fab fa-twitter"></i> تويتر
                        </a>
                        <a href="#" class="btn btn-outline-danger">
                            <i class="fab fa-instagram"></i> إنستغرام
                        </a>
                        <a href="#" class="btn btn-outline-primary">
                            <i class="fab fa-linkedin-in"></i> لينكد إن
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الأسئلة الشائعة -->
    <div class="row mt-5">
        <div class="col-12">
            <h2 class="text-center mb-4">الأسئلة الشائعة</h2>
            <div class="accordion" id="faqAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq1">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                            كيف يمكنني إضافة إعلان جديد؟
                        </button>
                    </h2>
                    <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            يمكنك إضافة إعلان جديد بسهولة من خلال النقر على زر "أضف إعلان" في أعلى الصفحة، ثم ملء النموذج بالمعلومات المطلوبة ورفع الصور.
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq2">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                            هل الموقع مجاني؟
                        </button>
                    </h2>
                    <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            نعم، إضافة الإعلانات وتصفح الموقع مجاني تماماً. نوفر أيضاً خدمات مميزة مدفوعة لزيادة ظهور إعلاناتك.
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq3">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                            كيف يمكنني التواصل مع البائع؟
                        </button>
                    </h2>
                    <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            يمكنك التواصل مع البائع من خلال معلومات الاتصال المتوفرة في الإعلان، أو عبر نظام الرسائل الداخلي في الموقع.
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq4">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
                            ماذا أفعل إذا واجهت مشكلة تقنية؟
                        </button>
                    </h2>
                    <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            يمكنك التواصل معنا عبر نموذج الاتصال أعلاه أو عبر البريد الإلكتروني، وسيقوم فريق الدعم الفني بمساعدتك في أسرع وقت.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // عداد الأحرف للرسالة
    const messageTextarea = document.getElementById('message');
    const maxLength = 1000;
    
    if (messageTextarea) {
        const counter = document.createElement('small');
        counter.className = 'text-muted';
        messageTextarea.parentElement.appendChild(counter);
        
        function updateCounter() {
            const currentLength = messageTextarea.value.length;
            counter.textContent = `${currentLength}/${maxLength} حرف`;
            
            if (currentLength > maxLength * 0.9) {
                counter.className = 'text-warning';
            } else {
                counter.className = 'text-muted';
            }
        }
        
        messageTextarea.addEventListener('input', updateCounter);
        updateCounter();
    }
    
    // التحقق من صحة النموذج
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const message = document.getElementById('message').value.trim();
        
        if (message.length < 10) {
            e.preventDefault();
            alert('الرسالة يجب أن تكون 10 أحرف على الأقل');
            return;
        }
        
        // إظهار مؤشر التحميل
        const submitButton = form.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
    });
});
</script>

<?php include 'includes/footer.php'; ?>
