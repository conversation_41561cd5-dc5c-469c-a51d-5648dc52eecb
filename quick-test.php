<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

// تسجيل دخول تلقائي
$user = $db->fetch("SELECT * FROM users LIMIT 1");
if ($user) {
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['logged_in'] = true;
}

echo "<h2>🚀 اختبار سريع للبلاغات</h2>";

if (!isset($_SESSION['user_id'])) {
    echo "<p style='color: red;'>❌ لا يمكن الاختبار بدون تسجيل الدخول</p>";
    exit;
}

echo "<p>✅ مسجل الدخول كـ: <strong>{$_SESSION['username']}</strong> (ID: {$_SESSION['user_id']})</p>";

// جلب إعلان للاختبار
$ad = $db->fetch("SELECT * FROM ads LIMIT 1");
if (!$ad) {
    echo "<p style='color: red;'>❌ لا توجد إعلانات للاختبار</p>";
    exit;
}

echo "<p>✅ سيتم اختبار البلاغ على الإعلان: <strong>{$ad['title']}</strong> (ID: {$ad['id']})</p>";

// فحص هيكل الجدول
echo "<h3>1. فحص هيكل جدول reports:</h3>";
$structure = $db->fetchAll("PRAGMA table_info(reports)");
$columns = array_column($structure, 'name');
echo "<p><strong>الأعمدة الموجودة:</strong> " . implode(', ', $columns) . "</p>";

$has_reported_ad_id = in_array('reported_ad_id', $columns);
$has_ad_id = in_array('ad_id', $columns);

if ($has_reported_ad_id && !$has_ad_id) {
    echo "<p style='color: green;'>✅ الجدول صحيح: يحتوي على reported_ad_id وليس ad_id</p>";
} elseif ($has_ad_id && !$has_reported_ad_id) {
    echo "<p style='color: red;'>❌ الجدول خطأ: يحتوي على ad_id وليس reported_ad_id</p>";
    echo "<p><a href='emergency-fix.php' style='background: red; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>إصلاح فوري</a></p>";
    exit;
} else {
    echo "<p style='color: orange;'>⚠️ الجدول يحتوي على مشكلة في الأعمدة</p>";
    exit;
}

// اختبار الإدراج
echo "<h3>2. اختبار إدراج البلاغ:</h3>";

$ad_id = $ad['id'];
$user_id = $_SESSION['user_id'];
$reason = 'test';
$description = 'اختبار سريع - ' . date('Y-m-d H:i:s');

echo "<p><strong>البيانات:</strong></p>";
echo "<ul>";
echo "<li>user_id: $user_id</li>";
echo "<li>reporter_id: $user_id</li>";
echo "<li>reported_ad_id: $ad_id</li>";
echo "<li>report_type: $reason</li>";
echo "<li>reason: $description</li>";
echo "</ul>";

try {
    // نفس الاستعلام المستخدم في ad-details.php
    $db->query("INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))",
              [$user_id, $user_id, $ad_id, $reason, $description]);
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>🎉 نجح إدراج البلاغ!</h4>";
    
    // التحقق من الإدراج
    $inserted = $db->fetch("SELECT * FROM reports WHERE reason = ? ORDER BY id DESC LIMIT 1", [$description]);
    if ($inserted) {
        echo "<p><strong>تفاصيل البلاغ المُدرج:</strong></p>";
        echo "<ul>";
        echo "<li>ID: {$inserted['id']}</li>";
        echo "<li>user_id: {$inserted['user_id']}</li>";
        echo "<li>reporter_id: {$inserted['reporter_id']}</li>";
        echo "<li>reported_ad_id: {$inserted['reported_ad_id']}</li>";
        echo "<li>report_type: {$inserted['report_type']}</li>";
        echo "<li>created_at: {$inserted['created_at']}</li>";
        echo "</ul>";
        
        // حذف البيانات التجريبية
        $db->query("DELETE FROM reports WHERE id = ?", [$inserted['id']]);
        echo "<p>✅ تم حذف البيانات التجريبية</p>";
        
        echo "<h3>🎯 النتيجة النهائية:</h3>";
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; text-align: center;'>";
        echo "<h2>✅ تم حل المشكلة بنجاح!</h2>";
        echo "<p>البلاغات تعمل الآن بشكل مثالي</p>";
        echo "<p>لا توجد أخطاء في قاعدة البيانات</p>";
        echo "</div>";
        
    } else {
        echo "<p style='color: red;'>❌ لم يتم العثور على البلاغ المُدرج</p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>❌ فشل إدراج البلاغ!</h4>";
    echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
    
    if (strpos($e->getMessage(), 'reports.ad_id') !== false) {
        echo "<p style='color: red;'><strong>المشكلة:</strong> الجدول لا يزال يحتوي على العمود الخطأ ad_id</p>";
        echo "<p><a href='emergency-fix.php' style='background: red; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>إصلاح فوري</a></p>";
    }
    echo "</div>";
}

echo "<hr>";
echo "<h3>🔗 اختبار حقيقي:</h3>";
echo "<div style='text-align: center;'>";
echo "<a href='pages/ad-details.php?id={$ad['id']}' target='_blank' style='margin: 5px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>اختبار البلاغ في الصفحة الحقيقية</a>";
echo "</div>";
?>
