<?php
// ملف لتنظيف الإعلانات المباعة (يتم تشغيله يومياً عبر cron job)

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';

try {
    // حذف الإعلانات المباعة التي مر عليها أسبوع
    $deleted_count = $db->query("
        DELETE FROM ads 
        WHERE is_sold = 1 
        AND sold_at IS NOT NULL 
        AND sold_at < DATE_SUB(NOW(), INTERVAL 7 DAY)
    ");
    
    echo "تم حذف {$deleted_count} إعلان مباع قديم (أكثر من أسبوع).\n";
    
    // تحديث حالة الإعلانات المباعة لإخفائها بعد 3 أيام
    $hidden_count = $db->query("
        UPDATE ads 
        SET status = 'sold_hidden' 
        WHERE is_sold = 1 
        AND status = 'active'
        AND hide_sold_at IS NOT NULL 
        AND hide_sold_at <= NOW()
    ");
    
    echo "تم إخفاء {$hidden_count} إعلان مباع (مر عليه 3 أيام).\n";
    
    // تسجيل النشاط
    if ($deleted_count > 0 || $hidden_count > 0) {
        try {
            $db->query(
                "INSERT INTO activity_logs (user_id, action_type, description, ip_address) VALUES (?, ?, ?, ?)",
                [null, 'admin', "تنظيف الإعلانات المباعة: حذف {$deleted_count}، إخفاء {$hidden_count}", 'system']
            );
        } catch (Exception $e) {
            // تجاهل أخطاء السجلات
        }
    }
    
} catch (Exception $e) {
    echo "خطأ في تنظيف الإعلانات المباعة: " . $e->getMessage() . "\n";
}
?>
