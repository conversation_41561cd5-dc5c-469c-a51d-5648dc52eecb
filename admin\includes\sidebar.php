<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) === 'index.php' ? 'active' : '' ?>" href="index.php">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) === 'ads.php' ? 'active' : '' ?>" href="ads.php">
                    <i class="fas fa-bullhorn"></i>
                    إدارة الإعلانات
                    <?php
                    $pending_count = $db->fetch("SELECT COUNT(*) as count FROM ads WHERE status = 'pending'")['count'];
                    if ($pending_count > 0):
                    ?>
                    <span class="notification-badge"><?= $pending_count ?></span>
                    <?php endif; ?>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) === 'users.php' ? 'active' : '' ?>" href="users.php">
                    <i class="fas fa-users"></i>
                    إدارة المستخدمين
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) === 'categories.php' ? 'active' : '' ?>" href="categories.php">
                    <i class="fas fa-tags"></i>
                    إدارة الفئات
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) === 'car-brands.php' ? 'active' : '' ?>" href="car-brands.php">
                    <i class="fas fa-car"></i>
                    ماركات السيارات
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) === 'announcements.php' ? 'active' : '' ?>" href="announcements.php">
                    <i class="fas fa-bullhorn"></i>
                    الإعلانات العامة
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) === 'reports.php' ? 'active' : '' ?>" href="reports.php">
                    <i class="fas fa-flag"></i>
                    التقارير والشكاوى
                    <?php
                    $reports_count = $db->fetch("SELECT COUNT(*) as count FROM reports WHERE status = 'pending'")['count'];
                    if ($reports_count > 0):
                    ?>
                    <span class="notification-badge"><?= $reports_count ?></span>
                    <?php endif; ?>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) === 'logs.php' ? 'active' : '' ?>" href="logs.php">
                    <i class="fas fa-list-alt"></i>
                    سجلات النشاط
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) === 'messages.php' ? 'active' : '' ?>" href="messages.php">
                    <i class="fas fa-envelope"></i>
                    الرسائل
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) === 'pages.php' ? 'active' : '' ?>" href="pages.php">
                    <i class="fas fa-file-alt"></i>
                    إدارة الصفحات
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) === 'settings.php' ? 'active' : '' ?>" href="settings.php">
                    <i class="fas fa-cogs"></i>
                    إعدادات الموقع
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) === 'analytics.php' ? 'active' : '' ?>" href="analytics.php">
                    <i class="fas fa-chart-bar"></i>
                    التحليلات والإحصائيات
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= basename($_SERVER['PHP_SELF']) === 'backup.php' ? 'active' : '' ?>" href="backup.php">
                    <i class="fas fa-database"></i>
                    النسخ الاحتياطي
                </a>
            </li>
        </ul>

        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
            <span>أدوات إضافية</span>
        </h6>
        
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link" href="logs.php">
                    <i class="fas fa-history"></i>
                    سجل النشاطات
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="maintenance.php">
                    <i class="fas fa-tools"></i>
                    وضع الصيانة
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="../pages/home.php" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    عرض الموقع
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="../pages/logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </li>
        </ul>
        
        <!-- معلومات النظام -->
        <div class="px-3 mt-4">
            <div class="card bg-transparent border-light">
                <div class="card-body text-center text-white-50">
                    <small>
                        <i class="fas fa-server"></i><br>
                        إصدار النظام: 1.0<br>
                        PHP: <?= PHP_VERSION ?><br>
                        الذاكرة: <?= ini_get('memory_limit') ?>
                    </small>
                </div>
            </div>
        </div>
    </div>
</nav>
