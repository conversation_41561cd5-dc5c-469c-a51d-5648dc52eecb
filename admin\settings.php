<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدمن
requireAdmin();

$error = '';
$success = '';

// إنشاء جدول الإعدادات إذا لم يكن موجوداً
try {
    $db->query("
        CREATE TABLE IF NOT EXISTS settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            description TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // إضافة الإعدادات الافتراضية
    $default_settings = [
        ['site_name', 'حراجنا', 'اسم الموقع'],
        ['site_description', 'منصة الإعلانات المبوبة الأولى في المملكة العربية السعودية', 'وصف الموقع'],
        ['contact_email', '<EMAIL>', 'بريد التواصل'],
        ['contact_phone', '+966123456789', 'هاتف التواصل'],
        ['max_ads_per_user', '10', 'الحد الأقصى للإعلانات لكل مستخدم'],
        ['ad_expiry_days', '30', 'مدة انتهاء الإعلان بالأيام'],
        ['enable_registration', '1', 'تفعيل التسجيل الجديد'],
        ['enable_ad_posting', '1', 'تفعيل نشر الإعلانات'],
        ['maintenance_mode', '0', 'وضع الصيانة'],
        ['maintenance_message', 'الموقع تحت الصيانة، سنعود قريباً', 'رسالة الصيانة']
    ];
    
    foreach ($default_settings as $setting) {
        $existing = $db->fetch("SELECT id FROM settings WHERE setting_key = ?", [$setting[0]]);
        if (!$existing) {
            $db->query("INSERT INTO settings (setting_key, setting_value, description) VALUES (?, ?, ?)", $setting);
        }
    }
} catch (Exception $e) {
    $error = 'خطأ في إنشاء جدول الإعدادات: ' . $e->getMessage();
}

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    foreach ($_POST as $key => $value) {
        if ($key !== 'save_settings') {
            $db->query("UPDATE settings SET setting_value = ? WHERE setting_key = ?", [sanitize($value), $key]);
        }
    }
    $success = 'تم حفظ الإعدادات بنجاح';
}

// جلب الإعدادات
$settings = [];
$settings_data = $db->fetchAll("SELECT * FROM settings ORDER BY setting_key");
foreach ($settings_data as $setting) {
    $settings[$setting['setting_key']] = $setting;
}

$page_title = 'إعدادات الموقع';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إعدادات الموقع</h1>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?= $error ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?= $success ?>
                </div>
            <?php endif; ?>

            <form method="POST">
                <div class="row">
                    <!-- الإعدادات العامة -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-cog"></i> الإعدادات العامة</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" name="site_name" 
                                           value="<?= $settings['site_name']['setting_value'] ?? '' ?>">
                                    <label>اسم الموقع</label>
                                </div>
                                
                                <div class="form-floating mb-3">
                                    <textarea class="form-control" name="site_description" style="height: 100px"><?= $settings['site_description']['setting_value'] ?? '' ?></textarea>
                                    <label>وصف الموقع</label>
                                </div>
                                
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" name="contact_email" 
                                           value="<?= $settings['contact_email']['setting_value'] ?? '' ?>">
                                    <label>بريد التواصل</label>
                                </div>
                                
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" name="contact_phone" 
                                           value="<?= $settings['contact_phone']['setting_value'] ?? '' ?>">
                                    <label>هاتف التواصل</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- إعدادات الإعلانات -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-bullhorn"></i> إعدادات الإعلانات</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-floating mb-3">
                                    <input type="number" class="form-control" name="max_ads_per_user" 
                                           value="<?= $settings['max_ads_per_user']['setting_value'] ?? '10' ?>">
                                    <label>الحد الأقصى للإعلانات لكل مستخدم</label>
                                </div>
                                
                                <div class="form-floating mb-3">
                                    <input type="number" class="form-control" name="ad_expiry_days" 
                                           value="<?= $settings['ad_expiry_days']['setting_value'] ?? '30' ?>">
                                    <label>مدة انتهاء الإعلان (بالأيام)</label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="enable_registration" value="1"
                                           <?= ($settings['enable_registration']['setting_value'] ?? '1') == '1' ? 'checked' : '' ?>>
                                    <label class="form-check-label">
                                        تفعيل التسجيل الجديد
                                    </label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="enable_ad_posting" value="1"
                                           <?= ($settings['enable_ad_posting']['setting_value'] ?? '1') == '1' ? 'checked' : '' ?>>
                                    <label class="form-check-label">
                                        تفعيل نشر الإعلانات
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- وضع الصيانة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-tools"></i> وضع الصيانة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="maintenance_mode" value="1"
                                           <?= ($settings['maintenance_mode']['setting_value'] ?? '0') == '1' ? 'checked' : '' ?>>
                                    <label class="form-check-label">
                                        تفعيل وضع الصيانة
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="form-floating">
                                    <textarea class="form-control" name="maintenance_message" style="height: 80px"><?= $settings['maintenance_message']['setting_value'] ?? '' ?></textarea>
                                    <label>رسالة الصيانة</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <button type="submit" name="save_settings" class="btn btn-primary btn-lg">
                        <i class="fas fa-save"></i> حفظ الإعدادات
                    </button>
                </div>
            </form>
            
            <!-- معلومات النظام -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> معلومات النظام</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>إصدار PHP:</strong></td>
                                    <td><?= PHP_VERSION ?></td>
                                </tr>
                                <tr>
                                    <td><strong>خادم الويب:</strong></td>
                                    <td><?= $_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف' ?></td>
                                </tr>
                                <tr>
                                    <td><strong>نظام التشغيل:</strong></td>
                                    <td><?= PHP_OS ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>الحد الأقصى لرفع الملفات:</strong></td>
                                    <td><?= ini_get('upload_max_filesize') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>الحد الأقصى للذاكرة:</strong></td>
                                    <td><?= ini_get('memory_limit') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>المنطقة الزمنية:</strong></td>
                                    <td><?= date_default_timezone_get() ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
