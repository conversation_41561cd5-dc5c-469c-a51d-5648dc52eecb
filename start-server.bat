@echo off
chcp 65001 >nul
title خادم حراجنا المحلي

echo.
echo ========================================
echo        🚀 خادم حراجنا المحلي 🚀
echo ========================================
echo.

REM التحقق من وجود PHP
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PHP غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت PHP أولاً من: https://www.php.net/downloads
    pause
    exit /b 1
)

REM التحقق من وجود الملفات المطلوبة
if not exist "pages\home.php" (
    echo ❌ ملفات الموقع غير موجودة
    echo تأكد من وجودك في المجلد الصحيح
    pause
    exit /b 1
)

echo ✅ PHP متوفر
echo ✅ ملفات الموقع موجودة
echo.

REM إنشاء المجلدات المطلوبة
if not exist "uploads" mkdir uploads
if not exist "uploads\ads" mkdir uploads\ads
if not exist "uploads\users" mkdir uploads\users
if not exist "logs" mkdir logs
if not exist "config" mkdir config

echo ✅ تم إنشاء المجلدات المطلوبة
echo.

echo 🔄 بدء تشغيل الخادم على المنفذ 8081...
echo.
echo 📋 روابط مفيدة:
echo    🏠 الصفحة الرئيسية: http://localhost:8081/pages/home.php
echo    🔧 تثبيت الموقع: http://localhost:8081/install/install.php
echo    👨‍💼 لوحة الإدارة: http://localhost:8081/admin/index.php
echo.
echo 💡 استخدم Ctrl+C لإيقاف الخادم
echo =====================================
echo.

REM تشغيل الخادم
php -S localhost:8081

echo.
echo 🛑 تم إيقاف الخادم
pause
