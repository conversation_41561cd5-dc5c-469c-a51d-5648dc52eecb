</main>
<!-- نهاية المحتوى الرئيسي -->

<!-- الفوتر الاحترافي -->
<footer class="footer">
    <div class="container">
        <div class="row">
            <!-- معلومات الموقع -->
            <div class="col-lg-4 col-md-6 mb-4">
                <h5>
                    <i class="fas fa-store me-2"></i>
                    حراجنا
                </h5>
                <p>
                    موقع حراجنا هو أفضل منصة للإعلانات المبوبة في المملكة العربية السعودية. 
                    نوفر لك تجربة سهلة وآمنة للبيع والشراء في جميع الفئات.
                </p>
                <div class="social-links">
                    <a href="#" title="فيسبوك" aria-label="فيسبوك">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" title="تويتر" aria-label="تويتر">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" title="إنستغرام" aria-label="إنستغرام">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" title="لينكد إن" aria-label="لينكد إن">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a href="#" title="يوتيوب" aria-label="يوتيوب">
                        <i class="fab fa-youtube"></i>
                    </a>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="col-lg-2 col-md-6 mb-4">
                <h5>روابط سريعة</h5>
                <ul class="list-unstyled">
                    <li><a href="home.php">الرئيسية</a></li>
                    <li><a href="categories.php">الفئات</a></li>
                    <li><a href="search.php">البحث المتقدم</a></li>
                    <li><a href="add-ad.php">إضافة إعلان</a></li>
                    <li><a href="contact.php">اتصل بنا</a></li>
                </ul>
            </div>

            <!-- الفئات الشائعة -->
            <div class="col-lg-2 col-md-6 mb-4">
                <h5>الفئات الشائعة</h5>
                <ul class="list-unstyled">
                    <?php
                    if (isset($db)) {
                        $popular_categories = $db->fetchAll("
                            SELECT c.name, c.slug, COUNT(a.id) as ads_count 
                            FROM categories c 
                            LEFT JOIN ads a ON c.id = a.category_id AND a.status = 'active'
                            WHERE c.is_active = 1 
                            GROUP BY c.id 
                            ORDER BY ads_count DESC 
                            LIMIT 5
                        ");
                        
                        foreach ($popular_categories as $category):
                    ?>
                        <li>
                            <a href="category-ads.php?category=<?= $category['slug'] ?>">
                                <?= $category['name'] ?>
                                <small class="text-muted">(<?= $category['ads_count'] ?>)</small>
                            </a>
                        </li>
                    <?php 
                        endforeach;
                    } else {
                        // فئات افتراضية في حالة عدم توفر قاعدة البيانات
                        $default_categories = [
                            ['name' => 'السيارات', 'slug' => 'cars'],
                            ['name' => 'العقارات', 'slug' => 'real-estate'],
                            ['name' => 'الإلكترونيات', 'slug' => 'electronics'],
                            ['name' => 'الوظائف', 'slug' => 'jobs'],
                            ['name' => 'الأثاث', 'slug' => 'furniture']
                        ];
                        
                        foreach ($default_categories as $category):
                    ?>
                        <li>
                            <a href="category-ads.php?category=<?= $category['slug'] ?>">
                                <?= $category['name'] ?>
                            </a>
                        </li>
                    <?php endforeach; } ?>
                </ul>
            </div>

            <!-- خدمات المستخدمين -->
            <div class="col-lg-2 col-md-6 mb-4">
                <h5>خدمات المستخدمين</h5>
                <ul class="list-unstyled">
                    <?php if (isLoggedIn()): ?>
                        <li><a href="profile.php">الملف الشخصي</a></li>
                        <li><a href="my-ads.php">إعلاناتي</a></li>
                        <li><a href="messages.php">الرسائل</a></li>
                        <li><a href="favorites.php">المفضلة</a></li>
                    <?php else: ?>
                        <li><a href="login.php">تسجيل الدخول</a></li>
                        <li><a href="register.php">إنشاء حساب</a></li>
                        <li><a href="forgot-password.php">نسيت كلمة المرور</a></li>
                    <?php endif; ?>
                    <li><a href="help.php">المساعدة</a></li>
                    <li><a href="faq.php">الأسئلة الشائعة</a></li>
                </ul>
            </div>

            <!-- معلومات الاتصال -->
            <div class="col-lg-2 col-md-6 mb-4">
                <h5>تواصل معنا</h5>
                <ul class="list-unstyled">
                    <li>
                        <i class="fas fa-envelope me-2"></i>
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </li>
                    <li>
                        <i class="fas fa-phone me-2"></i>
                        <a href="tel:+966500000000">+966 50 000 0000</a>
                    </li>
                    <li>
                        <i class="fas fa-map-marker-alt me-2"></i>
                        الرياض، المملكة العربية السعودية
                    </li>
                    <li>
                        <i class="fas fa-clock me-2"></i>
                        24/7 خدمة العملاء
                    </li>
                </ul>
            </div>
        </div>

        <!-- خط فاصل -->
        <hr class="my-4">

        <!-- روابط قانونية وحقوق النشر -->
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="copyright">
                    <p class="mb-0">
                        &copy; <?= date('Y') ?> حراجنا. جميع الحقوق محفوظة.
                    </p>
                </div>
            </div>
            <div class="col-md-6">
                <ul class="list-inline text-md-end mb-0">
                    <li class="list-inline-item">
                        <a href="privacy.php">سياسة الخصوصية</a>
                    </li>
                    <li class="list-inline-item">
                        <a href="terms.php">شروط الاستخدام</a>
                    </li>
                    <li class="list-inline-item">
                        <a href="sitemap.php">خريطة الموقع</a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <div class="d-flex justify-content-center align-items-center flex-wrap gap-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-shield-alt text-success me-2"></i>
                        <small>معاملات آمنة</small>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-clock text-info me-2"></i>
                        <small>دعم 24/7</small>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-users text-warning me-2"></i>
                        <small>مجتمع موثوق</small>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-mobile-alt text-primary me-2"></i>
                        <small>متوافق مع الجوال</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- زر العودة للأعلى -->
<button id="backToTop" class="btn btn-primary position-fixed" 
        style="bottom: 20px; left: 20px; z-index: 1000; border-radius: 50%; width: 50px; height: 50px; display: none;"
        title="العودة للأعلى" aria-label="العودة للأعلى">
    <i class="fas fa-arrow-up"></i>
</button>

<!-- Bootstrap JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<!-- JavaScript مخصص -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // زر العودة للأعلى
    const backToTopButton = document.getElementById('backToTop');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 300) {
            backToTopButton.style.display = 'block';
            backToTopButton.classList.add('fade-in');
        } else {
            backToTopButton.style.display = 'none';
            backToTopButton.classList.remove('fade-in');
        }
    });
    
    backToTopButton.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    // تحسين الأداء - تأخير تحميل الصور
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('loading');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
    
    // تحسين تجربة المستخدم - إضافة تأثيرات التحميل
    const cards = document.querySelectorAll('.card, .ad-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('slide-up');
    });
    
    // تحسين الأداء - تحميل الخطوط بشكل غير متزامن
    if ('fonts' in document) {
        document.fonts.ready.then(() => {
            document.body.classList.add('fonts-loaded');
        });
    }
    
    // تحسين إمكانية الوصول
    const focusableElements = document.querySelectorAll('a, button, input, textarea, select');
    focusableElements.forEach(element => {
        element.addEventListener('focus', function() {
            this.classList.add('focus-visible');
        });
        
        element.addEventListener('blur', function() {
            this.classList.remove('focus-visible');
        });
    });
});

// تحسين الأداء - Service Worker للتخزين المؤقت
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('SW registered: ', registration);
            })
            .catch(function(registrationError) {
                console.log('SW registration failed: ', registrationError);
            });
    });
}
</script>

<!-- Schema.org Structured Data -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "حراجنا",
    "url": "<?= (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] ?>",
    "description": "موقع حراجنا للإعلانات المبوبة - أفضل موقع للبيع والشراء في المملكة العربية السعودية",
    "potentialAction": {
        "@type": "SearchAction",
        "target": "<?= (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] ?>/search.php?q={search_term_string}",
        "query-input": "required name=search_term_string"
    }
}
</script>

</body>
</html>
