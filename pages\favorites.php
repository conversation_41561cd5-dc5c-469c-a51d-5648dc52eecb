<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// إنشاء كائن المصادقة
$auth = new Auth($db);

// التحقق من تسجيل الدخول
requireLogin();

// معالجة إزالة من المفضلة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['remove_favorite'])) {
    $ad_id = (int)$_POST['ad_id'];
    $db->query("DELETE FROM favorites WHERE user_id = ? AND ad_id = ?", [$_SESSION['user_id'], $ad_id]);
    $_SESSION['success'] = 'تم إزالة الإعلان من المفضلة';
    header('Location: favorites.php');
    exit();
}

// جلب الإعلانات المفضلة
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$items_per_page = 12;
$offset = ($page - 1) * $items_per_page;

$sql = "
    SELECT a.*, u.username, u.full_name, c.name as category_name,
           (SELECT image_path FROM ad_images WHERE ad_id = a.id AND is_primary = 1 LIMIT 1) as primary_image,
           f.created_at as favorited_at
    FROM favorites f
    JOIN ads a ON f.ad_id = a.id
    JOIN users u ON a.user_id = u.id
    JOIN categories c ON a.category_id = c.id
    WHERE f.user_id = ? AND a.status = 'active'
    ORDER BY f.created_at DESC
    LIMIT $items_per_page OFFSET $offset
";

$favorites = $db->fetchAll($sql, [$_SESSION['user_id']]);

// عدد النتائج الإجمالي
$total_favorites = $db->fetch("
    SELECT COUNT(*) as total 
    FROM favorites f 
    JOIN ads a ON f.ad_id = a.id 
    WHERE f.user_id = ? AND a.status = 'active'
", [$_SESSION['user_id']])['total'];

$total_pages = ceil($total_favorites / $items_per_page);

$page_title = 'المفضلة';
include 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-heart text-danger"></i> المفضلة</h2>
                <div class="text-muted">
                    <?= number_format($total_favorites) ?> إعلان مفضل
                </div>
            </div>

            <?php if (!empty($favorites)): ?>
                <div class="row">
                    <?php foreach ($favorites as $ad): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 shadow-sm">
                                <div class="position-relative">
                                    <?php if ($ad['primary_image']): ?>
                                        <img src="../uploads/<?= $ad['primary_image'] ?>" 
                                             class="card-img-top" 
                                             style="height: 200px; object-fit: cover;" 
                                             alt="<?= $ad['title'] ?>">
                                    <?php else: ?>
                                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                             style="height: 200px;">
                                            <i class="fas fa-image text-muted fa-3x"></i>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- زر إزالة من المفضلة -->
                                    <form method="post" class="position-absolute top-0 end-0 m-2">
                                        <input type="hidden" name="ad_id" value="<?= $ad['id'] ?>">
                                        <button type="submit" name="remove_favorite" 
                                                class="btn btn-danger btn-sm rounded-circle remove-favorite-btn"
                                                title="إزالة من المفضلة">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </form>

                                    <?php if ($ad['is_featured']): ?>
                                        <div class="position-absolute top-0 start-0 m-2">
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-star"></i> مميز
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title">
                                        <a href="ad-details.php?id=<?= $ad['id'] ?>" class="text-decoration-none">
                                            <?= substr($ad['title'], 0, 50) ?><?= strlen($ad['title']) > 50 ? '...' : '' ?>
                                        </a>
                                    </h5>
                                    
                                    <div class="text-success fw-bold fs-5 mb-2">
                                        <?= formatPrice($ad['price']) ?>
                                        <?php if ($ad['price_type'] === 'negotiable'): ?>
                                            <small class="text-muted">(قابل للتفاوض)</small>
                                        <?php elseif ($ad['price_type'] === 'free'): ?>
                                            <small class="text-success">(مجاني)</small>
                                        <?php endif; ?>
                                    </div>

                                    <div class="text-muted small mb-2">
                                        <i class="fas fa-tag"></i> <?= $ad['category_name'] ?>
                                    </div>

                                    <div class="text-muted small mb-2">
                                        <i class="fas fa-map-marker-alt"></i> <?= $ad['city'] ?>, <?= $ad['region'] ?>
                                    </div>

                                    <div class="text-muted small mb-3">
                                        <i class="fas fa-user"></i> <?= $ad['full_name'] ?>
                                    </div>

                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center text-muted small mb-2">
                                            <span>
                                                <i class="fas fa-eye"></i> <?= number_format($ad['views_count']) ?>
                                            </span>
                                            <span>
                                                <i class="fas fa-clock"></i> <?= timeAgo($ad['created_at']) ?>
                                            </span>
                                        </div>

                                        <div class="text-muted small">
                                            <i class="fas fa-heart text-danger"></i> 
                                            أُضيف للمفضلة <?= timeAgo($ad['favorited_at']) ?>
                                        </div>

                                        <div class="d-grid gap-2 mt-3">
                                            <a href="ad-details.php?id=<?= $ad['id'] ?>" class="btn btn-primary btn-sm">
                                                <i class="fas fa-eye"></i> عرض التفاصيل
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- الصفحات -->
                <?php if ($total_pages > 1): ?>
                <nav aria-label="صفحات المفضلة" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page - 1 ?>">السابق</a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?>"><?= $i ?></a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page + 1 ?>">التالي</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
                <?php endif; ?>

            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-heart fa-4x text-muted mb-3"></i>
                    <h4>لا توجد إعلانات مفضلة</h4>
                    <p class="text-muted">لم تقم بإضافة أي إعلانات للمفضلة بعد</p>
                    <a href="home.php" class="btn btn-primary">
                        <i class="fas fa-search"></i> تصفح الإعلانات
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأكيد إزالة من المفضلة
    document.querySelectorAll('.remove-favorite-btn').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            
            if (confirm('هل تريد إزالة هذا الإعلان من المفضلة؟')) {
                this.closest('form').submit();
            }
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
