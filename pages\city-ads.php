<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';

// جلب اسم المدينة
$city = isset($_GET['city']) ? sanitize($_GET['city']) : '';
$category = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$sort = isset($_GET['sort']) ? sanitize($_GET['sort']) : 'newest';

if (empty($city)) {
    header('Location: home.php');
    exit();
}

$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$items_per_page = 12;
$offset = ($page - 1) * $items_per_page;

// بناء شروط البحث
$where_conditions = ["a.status = 'active'", "a.city = ?"];
$params = [$city];

if (!empty($search)) {
    $where_conditions[] = "(a.title LIKE ? OR a.description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($category > 0) {
    $where_conditions[] = "a.category_id = ?";
    $params[] = $category;
}

$where_clause = implode(' AND ', $where_conditions);

// ترتيب النتائج
$order_by = "a.is_featured DESC, a.created_at DESC";
switch ($sort) {
    case 'price_low':
        $order_by = "a.is_featured DESC, a.price ASC";
        break;
    case 'price_high':
        $order_by = "a.is_featured DESC, a.price DESC";
        break;
    case 'oldest':
        $order_by = "a.is_featured DESC, a.created_at ASC";
        break;
    case 'most_viewed':
        $order_by = "a.is_featured DESC, a.views_count DESC";
        break;
}

// جلب الإعلانات
$sql = "
    SELECT a.*, u.full_name, u.username, c.name as category_name,
           (SELECT image_path FROM ad_images WHERE ad_id = a.id AND is_primary = 1 LIMIT 1) as primary_image,
           (SELECT COUNT(*) FROM favorites WHERE ad_id = a.id) as favorites_count
    FROM ads a
    JOIN users u ON a.user_id = u.id
    JOIN categories c ON a.category_id = c.id
    WHERE $where_clause
    ORDER BY $order_by
    LIMIT $items_per_page OFFSET $offset
";

$ads = $db->fetchAll($sql, $params);

// عدد النتائج الإجمالي
$count_sql = "SELECT COUNT(*) as total FROM ads a WHERE $where_clause";
$total_ads = $db->fetch($count_sql, $params)['total'];
$total_pages = ceil($total_ads / $items_per_page);

// جلب الفئات للفلتر
$categories = $db->fetchAll("
    SELECT c.*, COUNT(a.id) as ads_count 
    FROM categories c 
    LEFT JOIN ads a ON c.id = a.category_id AND a.status = 'active' AND a.city = ?
    WHERE c.is_active = 1 AND c.parent_id IS NULL
    GROUP BY c.id
    ORDER BY c.sort_order, c.name
", [$city]);

$page_title = "إعلانات $city";
include 'includes/header.php';
?>

<div class="container mt-4">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="home.php">الرئيسية</a></li>
                    <li class="breadcrumb-item active"><?= $city ?></li>
                </ol>
            </nav>
            
            <div class="d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-map-marker-alt text-primary"></i> إعلانات <?= $city ?></h1>
                <div class="text-muted">
                    <?= number_format($total_ads) ?> إعلان
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <input type="hidden" name="city" value="<?= htmlspecialchars($city) ?>">
                
                <div class="col-md-4">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?= htmlspecialchars($search) ?>" placeholder="البحث...">
                        <label for="search">البحث في الإعلانات</label>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="form-floating">
                        <select class="form-select" id="category" name="category">
                            <option value="">جميع الفئات</option>
                            <?php foreach ($categories as $cat): ?>
                                <option value="<?= $cat['id'] ?>" <?= $category == $cat['id'] ? 'selected' : '' ?>>
                                    <?= $cat['name'] ?> (<?= $cat['ads_count'] ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <label for="category">الفئة</label>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="form-floating">
                        <select class="form-select" id="sort" name="sort">
                            <option value="newest" <?= $sort === 'newest' ? 'selected' : '' ?>>الأحدث</option>
                            <option value="oldest" <?= $sort === 'oldest' ? 'selected' : '' ?>>الأقدم</option>
                            <option value="price_low" <?= $sort === 'price_low' ? 'selected' : '' ?>>السعر: من الأقل للأعلى</option>
                            <option value="price_high" <?= $sort === 'price_high' ? 'selected' : '' ?>>السعر: من الأعلى للأقل</option>
                            <option value="most_viewed" <?= $sort === 'most_viewed' ? 'selected' : '' ?>>الأكثر مشاهدة</option>
                        </select>
                        <label for="sort">ترتيب النتائج</label>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary h-100 w-100">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نتائج البحث -->
    <?php if (!empty($ads)): ?>
        <div class="row">
            <?php foreach ($ads as $ad): ?>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="position-relative">
                            <?php if ($ad['primary_image']): ?>
                                <img src="../uploads/<?= $ad['primary_image'] ?>" 
                                     class="card-img-top" 
                                     style="height: 200px; object-fit: cover;" 
                                     alt="<?= $ad['title'] ?>">
                            <?php else: ?>
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                     style="height: 200px;">
                                    <i class="fas fa-image text-muted fa-3x"></i>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($ad['is_featured']): ?>
                                <div class="position-absolute top-0 start-0 m-2">
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-star"></i> مميز
                                    </span>
                                </div>
                            <?php endif; ?>
                            
                            <!-- زر المفضلة -->
                            <?php if (isset($_SESSION['user_id'])): ?>
                                <div class="position-absolute top-0 end-0 m-2">
                                    <button class="btn btn-light btn-sm rounded-circle favorite-btn" 
                                            data-ad-id="<?= $ad['id'] ?>">
                                        <i class="fas fa-heart"></i>
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title"><?= substr($ad['title'], 0, 50) ?><?= strlen($ad['title']) > 50 ? '...' : '' ?></h6>
                            
                            <div class="text-success fw-bold fs-5 mb-2">
                                <?= formatPrice($ad['price']) ?>
                                <?php if ($ad['price_type'] === 'negotiable'): ?>
                                    <small class="text-muted">(قابل للتفاوض)</small>
                                <?php elseif ($ad['price_type'] === 'free'): ?>
                                    <small class="text-success">(مجاني)</small>
                                <?php endif; ?>
                            </div>

                            <div class="text-muted small mb-2">
                                <i class="fas fa-tag"></i> <?= $ad['category_name'] ?>
                            </div>

                            <div class="text-muted small mb-2">
                                <i class="fas fa-user"></i> <?= $ad['full_name'] ?>
                            </div>

                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center text-muted small mb-2">
                                    <span>
                                        <i class="fas fa-eye"></i> <?= number_format($ad['views_count']) ?>
                                    </span>
                                    <span>
                                        <i class="fas fa-heart"></i> <?= number_format($ad['favorites_count']) ?>
                                    </span>
                                    <span>
                                        <i class="fas fa-clock"></i> <?= timeAgo($ad['created_at']) ?>
                                    </span>
                                </div>

                                <div class="d-grid">
                                    <a href="ad-details.php?id=<?= $ad['id'] ?>" class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye"></i> عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- الصفحات -->
        <?php if ($total_pages > 1): ?>
        <nav aria-label="صفحات النتائج" class="mt-4">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>">السابق</a>
                    </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                        <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"><?= $i ?></a>
                    </li>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>">التالي</a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>

    <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-search fa-4x text-muted mb-3"></i>
            <h4>لا توجد إعلانات</h4>
            <p class="text-muted">لا توجد إعلانات في <?= $city ?> تطابق معايير البحث</p>
            <a href="home.php" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> العودة للرئيسية
            </a>
        </div>
    <?php endif; ?>
</div>

<script>
// إضافة/إزالة من المفضلة
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.favorite-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const adId = this.getAttribute('data-ad-id');
            
            fetch('ajax/toggle-favorite.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ad_id: adId})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const icon = this.querySelector('i');
                    if (data.is_favorite) {
                        icon.style.color = '#dc3545';
                    } else {
                        icon.style.color = '';
                    }
                }
            });
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
