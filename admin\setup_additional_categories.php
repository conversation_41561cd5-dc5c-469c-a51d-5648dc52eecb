<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدمن
requireAdmin();

try {
    // إضافة 6 فئات رئيسية جديدة
    $new_categories = [
        [
            'name' => 'خدمات',
            'description' => 'جميع أنواع الخدمات المهنية والشخصية',
            'icon' => 'fas fa-tools',
            'sort_order' => 7
        ],
        [
            'name' => 'رياضة وترفيه',
            'description' => 'معدات رياضية وألعاب وأنشطة ترفيهية',
            'icon' => 'fas fa-futbol',
            'sort_order' => 8
        ],
        [
            'name' => 'كتب وتعليم',
            'description' => 'كتب ومواد تعليمية ودورات',
            'icon' => 'fas fa-book',
            'sort_order' => 9
        ],
        [
            'name' => 'حيوانات أليفة',
            'description' => 'حيوانات أليفة ومستلزماتها',
            'icon' => 'fas fa-paw',
            'sort_order' => 10
        ],
        [
            'name' => 'صحة وجمال',
            'description' => 'منتجات العناية والجمال والصحة',
            'icon' => 'fas fa-heart',
            'sort_order' => 11
        ],
        [
            'name' => 'أطفال ومواليد',
            'description' => 'مستلزمات الأطفال والمواليد',
            'icon' => 'fas fa-baby',
            'sort_order' => 12
        ]
    ];
    
    $added_count = 0;
    
    foreach ($new_categories as $category) {
        // التحقق من عدم وجود الفئة مسبقاً
        $existing = $db->fetch("SELECT id FROM categories WHERE name = ?", [$category['name']]);
        
        if (!$existing) {
            $db->query(
                "INSERT INTO categories (name, description, icon, sort_order, is_active, created_at) VALUES (?, ?, ?, ?, 1, NOW())",
                [$category['name'], $category['description'], $category['icon'], $category['sort_order']]
            );
            $added_count++;
        }
    }
    
    // إضافة فئات فرعية للفئات الجديدة
    $subcategories = [
        // خدمات
        ['خدمات منزلية', 'خدمات', 'fas fa-home'],
        ['خدمات تقنية', 'خدمات', 'fas fa-laptop'],
        ['خدمات طبية', 'خدمات', 'fas fa-user-md'],
        ['خدمات قانونية', 'خدمات', 'fas fa-gavel'],
        ['خدمات تعليمية', 'خدمات', 'fas fa-chalkboard-teacher'],
        
        // رياضة وترفيه
        ['كرة القدم', 'رياضة وترفيه', 'fas fa-futbol'],
        ['كمال أجسام', 'رياضة وترفيه', 'fas fa-dumbbell'],
        ['سباحة', 'رياضة وترفيه', 'fas fa-swimmer'],
        ['ألعاب فيديو', 'رياضة وترفيه', 'fas fa-gamepad'],
        ['ألعاب أطفال', 'رياضة وترفيه', 'fas fa-puzzle-piece'],
        
        // كتب وتعليم
        ['كتب أكاديمية', 'كتب وتعليم', 'fas fa-graduation-cap'],
        ['كتب أدبية', 'كتب وتعليم', 'fas fa-book-open'],
        ['كتب دينية', 'كتب وتعليم', 'fas fa-mosque'],
        ['دورات تدريبية', 'كتب وتعليم', 'fas fa-certificate'],
        ['مواد تعليمية', 'كتب وتعليم', 'fas fa-pencil-alt'],
        
        // حيوانات أليفة
        ['قطط', 'حيوانات أليفة', 'fas fa-cat'],
        ['كلاب', 'حيوانات أليفة', 'fas fa-dog'],
        ['طيور', 'حيوانات أليفة', 'fas fa-dove'],
        ['أسماك', 'حيوانات أليفة', 'fas fa-fish'],
        ['مستلزمات حيوانات', 'حيوانات أليفة', 'fas fa-bone'],
        
        // صحة وجمال
        ['منتجات عناية', 'صحة وجمال', 'fas fa-spa'],
        ['مكياج', 'صحة وجمال', 'fas fa-palette'],
        ['عطور', 'صحة وجمال', 'fas fa-spray-can'],
        ['أجهزة رياضية', 'صحة وجمال', 'fas fa-weight'],
        ['مكملات غذائية', 'صحة وجمال', 'fas fa-pills'],
        
        // أطفال ومواليد
        ['ملابس أطفال', 'أطفال ومواليد', 'fas fa-tshirt'],
        ['ألعاب تعليمية', 'أطفال ومواليد', 'fas fa-blocks'],
        ['عربات أطفال', 'أطفال ومواليد', 'fas fa-baby-carriage'],
        ['أثاث أطفال', 'أطفال ومواليد', 'fas fa-bed'],
        ['مستلزمات رضاعة', 'أطفال ومواليد', 'fas fa-baby-bottle']
    ];
    
    $subcategories_added = 0;
    
    foreach ($subcategories as $sub) {
        // جلب ID الفئة الرئيسية
        $parent = $db->fetch("SELECT id FROM categories WHERE name = ? AND parent_id IS NULL", [$sub[1]]);
        
        if ($parent) {
            // التحقق من عدم وجود الفئة الفرعية مسبقاً
            $existing_sub = $db->fetch("SELECT id FROM categories WHERE name = ? AND parent_id = ?", [$sub[0], $parent['id']]);
            
            if (!$existing_sub) {
                $db->query(
                    "INSERT INTO categories (name, parent_id, icon, is_active, created_at) VALUES (?, ?, ?, 1, NOW())",
                    [$sub[0], $parent['id'], $sub[2]]
                );
                $subcategories_added++;
            }
        }
    }
    
    echo "تم إضافة {$added_count} فئة رئيسية جديدة و {$subcategories_added} فئة فرعية بنجاح!";
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
