<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h1>🔧 إصلاح جميع مشاكل صفحة الإعلان</h1>";

try {
    echo "<h2>المشكلة 1: إصلاح جدول البلاغات (reports)</h2>";
    
    // فحص جدول reports
    $reports_structure = $db->fetchAll("PRAGMA table_info(reports)");
    $reports_columns = array_column($reports_structure, 'name');
    
    echo "<p><strong>أعمدة جدول reports الحالية:</strong> " . implode(', ', $reports_columns) . "</p>";
    
    $has_ad_id = in_array('ad_id', $reports_columns);
    $has_reported_ad_id = in_array('reported_ad_id', $reports_columns);
    
    if ($has_ad_id && !$has_reported_ad_id) {
        echo "<p style='color: red;'>❌ مشكلة: الجدول يحتوي على ad_id بدلاً من reported_ad_id</p>";
        
        // حفظ البيانات الموجودة
        $existing_reports = $db->fetchAll("SELECT * FROM reports");
        echo "<p>✅ تم حفظ " . count($existing_reports) . " بلاغ موجود</p>";
        
        // إعادة إنشاء الجدول
        $db->query("DROP TABLE IF EXISTS reports");
        $db->query("
            CREATE TABLE reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                reporter_id INTEGER NOT NULL,
                reported_ad_id INTEGER NOT NULL,
                report_type TEXT NOT NULL,
                reason TEXT,
                status TEXT DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (reporter_id) REFERENCES users(id),
                FOREIGN KEY (reported_ad_id) REFERENCES ads(id)
            )
        ");
        
        // استعادة البيانات
        foreach ($existing_reports as $report) {
            $ad_id = $report['ad_id'] ?? null;
            if ($ad_id) {
                $db->query("
                    INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ", [
                    $report['user_id'],
                    $report['reporter_id'] ?? $report['user_id'],
                    $ad_id,
                    $report['report_type'] ?? 'other',
                    $report['reason'] ?? '',
                    $report['status'] ?? 'pending',
                    $report['created_at'] ?? date('Y-m-d H:i:s')
                ]);
            }
        }
        echo "<p>✅ تم إصلاح جدول reports</p>";
    } else {
        echo "<p style='color: green;'>✅ جدول reports صحيح</p>";
    }
    
    echo "<h2>المشكلة 2: إصلاح جدول المفضلة (favorites)</h2>";
    
    // فحص جدول favorites
    try {
        $favorites_structure = $db->fetchAll("PRAGMA table_info(favorites)");
        if (empty($favorites_structure)) {
            echo "<p style='color: orange;'>⚠️ جدول favorites غير موجود، سيتم إنشاؤه</p>";
            
            $db->query("
                CREATE TABLE favorites (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    ad_id INTEGER NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(user_id, ad_id),
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    FOREIGN KEY (ad_id) REFERENCES ads(id)
                )
            ");
            echo "<p>✅ تم إنشاء جدول favorites</p>";
        } else {
            echo "<p style='color: green;'>✅ جدول favorites موجود</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في جدول favorites: " . $e->getMessage() . "</p>";
        
        // إنشاء الجدول من جديد
        $db->query("DROP TABLE IF EXISTS favorites");
        $db->query("
            CREATE TABLE favorites (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                ad_id INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(user_id, ad_id),
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (ad_id) REFERENCES ads(id)
            )
        ");
        echo "<p>✅ تم إعادة إنشاء جدول favorites</p>";
    }
    
    echo "<h2>المشكلة 3: فحص وإصلاح ملف toggle-favorite.php</h2>";
    
    // التحقق من وجود الملف
    if (file_exists('pages/ajax/toggle-favorite.php')) {
        echo "<p>✅ ملف toggle-favorite.php موجود</p>";
        
        // قراءة محتوى الملف
        $file_content = file_get_contents('pages/ajax/toggle-favorite.php');
        
        // فحص المشاكل الشائعة
        if (strpos($file_content, 'NOW()') !== false) {
            echo "<p style='color: orange;'>⚠️ الملف يحتوي على NOW() بدلاً من datetime('now')</p>";
        }
        
        if (strpos($file_content, 'application/json') === false) {
            echo "<p style='color: orange;'>⚠️ الملف قد لا يرسل header JSON صحيح</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ ملف toggle-favorite.php غير موجود</p>";
    }
    
    echo "<h2>اختبار شامل للمشاكل</h2>";
    
    // تسجيل دخول تلقائي للاختبار
    session_start();
    $test_user = $db->fetch("SELECT * FROM users LIMIT 1");
    if ($test_user) {
        $_SESSION['user_id'] = $test_user['id'];
        $_SESSION['username'] = $test_user['username'];
        $_SESSION['logged_in'] = true;
        echo "<p>✅ تم تسجيل الدخول كـ: {$test_user['username']}</p>";
    }
    
    // جلب إعلان للاختبار
    $test_ad = $db->fetch("SELECT * FROM ads LIMIT 1");
    if (!$test_ad) {
        echo "<p style='color: red;'>❌ لا توجد إعلانات للاختبار</p>";
    } else {
        echo "<p>✅ سيتم الاختبار على الإعلان: {$test_ad['title']} (ID: {$test_ad['id']})</p>";
        
        // اختبار 1: البلاغات
        echo "<h3>اختبار البلاغات:</h3>";
        try {
            $test_reason = "اختبار إصلاح - " . date('Y-m-d H:i:s');
            $db->query("INSERT INTO reports (user_id, reporter_id, reported_ad_id, report_type, reason, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))",
                      [$test_user['id'], $test_user['id'], $test_ad['id'], 'test', $test_reason]);
            
            $inserted_report = $db->fetch("SELECT id FROM reports WHERE reason = ?", [$test_reason]);
            if ($inserted_report) {
                echo "<p style='color: green;'>✅ البلاغات تعمل بنجاح</p>";
                $db->query("DELETE FROM reports WHERE id = ?", [$inserted_report['id']]);
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ البلاغات لا تعمل: " . $e->getMessage() . "</p>";
        }
        
        // اختبار 2: المفضلة
        echo "<h3>اختبار المفضلة:</h3>";
        try {
            // حذف أي مفضلة سابقة
            $db->query("DELETE FROM favorites WHERE user_id = ? AND ad_id = ?", [$test_user['id'], $test_ad['id']]);
            
            // إضافة للمفضلة
            $db->query("INSERT INTO favorites (user_id, ad_id, created_at) VALUES (?, ?, datetime('now'))", 
                      [$test_user['id'], $test_ad['id']]);
            
            $inserted_favorite = $db->fetch("SELECT id FROM favorites WHERE user_id = ? AND ad_id = ?", 
                                           [$test_user['id'], $test_ad['id']]);
            if ($inserted_favorite) {
                echo "<p style='color: green;'>✅ المفضلة تعمل بنجاح</p>";
                $db->query("DELETE FROM favorites WHERE id = ?", [$inserted_favorite['id']]);
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ المفضلة لا تعمل: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;'>";
    echo "<h2>🎉 تم إصلاح جميع المشاكل!</h2>";
    echo "<p>البلاغات والمفضلة يجب أن تعمل الآن بشكل صحيح</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ في الإصلاح:</h4>";
    echo "<p><strong>الرسالة:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>🧪 اختبر الآن:</h3>";
echo "<div style='text-align: center;'>";
echo "<a href='pages/ad-details.php?id=4' target='_blank' style='margin: 5px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>اختبار صفحة الإعلان</a>";
echo "<a href='test-favorites-ajax.php' style='margin: 5px; padding: 10px 20px; background: #dc3545; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>اختبار المفضلة AJAX</a>";
echo "</div>";
?>
