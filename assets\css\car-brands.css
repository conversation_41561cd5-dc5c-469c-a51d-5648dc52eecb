/* تصميم قسم السيارات */
.car-brands-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 3rem 0;
    margin: 0;
    position: relative;
    overflow: hidden;
}

.car-brands-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="60" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.car-brands-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    position: relative;
    z-index: 2;
}

.car-brands-title {
    text-align: center;
    margin-bottom: 3rem;
    color: white;
    font-weight: 700;
    font-size: 2.2rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    animation: fadeInDown 1s ease-out;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.car-brands-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
    min-height: 200px; /* ضمان ظهور المساحة حتى لو لم تحمل البيانات */
}

.car-brand-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem 1rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border: 2px solid rgba(255,255,255,0.2);
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

.car-brand-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.car-brand-item:hover::before {
    left: 100%;
}

.car-brand-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    border-color: #667eea;
    color: inherit;
    text-decoration: none;
    background: rgba(255, 255, 255, 1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.car-brand-logo {
    width: 70px;
    height: 70px;
    margin-bottom: 1rem;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(102, 126, 234, 0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: 3px solid rgba(255,255,255,0.8);
    position: relative;
}

.car-brand-logo::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.car-brand-item:hover .car-brand-logo::after {
    opacity: 1;
}

.car-brand-logo img,
.car-brand-logo svg {
    width: 55px;
    height: 55px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.car-brand-item:hover .car-brand-logo {
    transform: scale(1.15) rotate(5deg);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.car-brand-item:hover .car-brand-logo img,
.car-brand-item:hover .car-brand-logo svg {
    transform: scale(1.1);
}

.car-brand-name {
    font-size: 0.9rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: 0.25rem;
}

.car-brand-arabic {
    font-size: 0.8rem;
    color: #6c757d;
    text-align: center;
}

.show-more-btn {
    display: block;
    margin: 3rem auto 0;
    padding: 1rem 2.5rem;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    backdrop-filter: blur(10px);
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    position: relative;
    overflow: hidden;
}

.show-more-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.show-more-btn:hover::before {
    left: 100%;
}

.show-more-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    border-color: rgba(255,255,255,0.5);
}

.hidden-brands {
    display: none;
}

.hidden-brands.show {
    display: contents;
}

/* استجابة للجوال */
@media (max-width: 768px) {
    .car-brands-section {
        padding: 2rem 0;
    }

    .car-brands-title {
        font-size: 1.8rem;
        margin-bottom: 2rem;
    }

    .car-brands-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 1rem;
    }

    .car-brand-item {
        padding: 1rem 0.5rem;
    }

    .car-brand-logo {
        width: 55px;
        height: 55px;
    }

    .car-brand-logo img,
    .car-brand-logo svg {
        width: 45px;
        height: 45px;
    }

    .car-brand-name {
        font-size: 0.8rem;
    }

    .car-brand-arabic {
        font-size: 0.7rem;
    }

    .show-more-btn {
        padding: 0.8rem 2rem;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .car-brands-section {
        padding: 1.5rem 0;
    }

    .car-brands-title {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .car-brands-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 0.75rem;
    }

    .car-brand-item {
        padding: 0.75rem 0.25rem;
    }

    .car-brand-logo {
        width: 45px;
        height: 45px;
        margin-bottom: 0.5rem;
    }

    .car-brand-logo img,
    .car-brand-logo svg {
        width: 35px;
        height: 35px;
    }

    .car-brand-name {
        font-size: 0.7rem;
    }

    .car-brand-arabic {
        font-size: 0.6rem;
    }

    .show-more-btn {
        padding: 0.7rem 1.5rem;
        font-size: 0.9rem;
        margin-top: 2rem;
    }
}
