<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص النظام - حراجنا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .check-item { padding: 10px; margin: 5px 0; border-radius: 8px; }
        .check-pass { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .check-fail { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .check-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0"><i class="fas fa-cogs"></i> فحص نظام حراجنا</h3>
                    </div>
                    <div class="card-body">
                        
                        <!-- معلومات النظام -->
                        <h5><i class="fas fa-info-circle"></i> معلومات النظام</h5>
                        <div class="check-item check-pass">
                            <strong>📅 التاريخ والوقت:</strong> <?= date('Y-m-d H:i:s') ?>
                        </div>
                        <div class="check-item check-pass">
                            <strong>🐘 إصدار PHP:</strong> <?= PHP_VERSION ?>
                        </div>
                        <div class="check-item check-pass">
                            <strong>🌐 الخادم:</strong> <?= $_SERVER['HTTP_HOST'] ?? 'localhost:8081' ?>
                        </div>
                        <div class="check-item check-pass">
                            <strong>📁 المجلد الجذر:</strong> <?= __DIR__ ?>
                        </div>
                        
                        <hr>
                        
                        <!-- فحص إضافات PHP -->
                        <h5><i class="fas fa-puzzle-piece"></i> إضافات PHP المطلوبة</h5>
                        <?php
                        $extensions = [
                            'pdo' => 'PDO - للاتصال بقاعدة البيانات',
                            'pdo_mysql' => 'PDO MySQL - لدعم MySQL',
                            'gd' => 'GD - لمعالجة الصور',
                            'mbstring' => 'Mbstring - لدعم النصوص متعددة البايت',
                            'openssl' => 'OpenSSL - للتشفير',
                            'json' => 'JSON - لمعالجة JSON',
                            'session' => 'Session - لإدارة الجلسات'
                        ];
                        
                        foreach ($extensions as $ext => $desc) {
                            $loaded = extension_loaded($ext);
                            $class = $loaded ? 'check-pass' : 'check-fail';
                            $icon = $loaded ? '✅' : '❌';
                            echo "<div class='check-item $class'>$icon <strong>$ext:</strong> $desc</div>";
                        }
                        ?>
                        
                        <hr>
                        
                        <!-- فحص الملفات -->
                        <h5><i class="fas fa-file-code"></i> الملفات الأساسية</h5>
                        <?php
                        $files = [
                            'index.php' => 'الصفحة الرئيسية',
                            'config/database.php' => 'إعدادات قاعدة البيانات',
                            'config/config.php' => 'الإعدادات العامة',
                            'includes/functions.php' => 'الدوال المساعدة',
                            'includes/auth.php' => 'نظام المصادقة',
                            'pages/home.php' => 'الصفحة الرئيسية للموقع',
                            'pages/login.php' => 'صفحة تسجيل الدخول',
                            'pages/register.php' => 'صفحة التسجيل',
                            'admin/index.php' => 'لوحة تحكم الإدارة',
                            'install/install.php' => 'معالج التثبيت',
                            'install/database.sql' => 'هيكل قاعدة البيانات'
                        ];
                        
                        foreach ($files as $file => $desc) {
                            $exists = file_exists($file);
                            $class = $exists ? 'check-pass' : 'check-fail';
                            $icon = $exists ? '✅' : '❌';
                            echo "<div class='check-item $class'>$icon <strong>$file:</strong> $desc</div>";
                        }
                        ?>
                        
                        <hr>
                        
                        <!-- فحص المجلدات -->
                        <h5><i class="fas fa-folder"></i> المجلدات المطلوبة</h5>
                        <?php
                        $directories = [
                            'uploads' => 'مجلد رفع الملفات',
                            'uploads/ads' => 'صور الإعلانات',
                            'uploads/users' => 'صور المستخدمين',
                            'logs' => 'ملفات السجلات',
                            'config' => 'ملفات التكوين',
                            'assets' => 'الملفات الثابتة',
                            'assets/css' => 'ملفات CSS'
                        ];
                        
                        foreach ($directories as $dir => $desc) {
                            $exists = is_dir($dir);
                            $writable = $exists ? is_writable($dir) : false;
                            
                            if ($exists && $writable) {
                                $class = 'check-pass';
                                $icon = '✅';
                                $status = 'موجود وقابل للكتابة';
                            } elseif ($exists) {
                                $class = 'check-warning';
                                $icon = '⚠️';
                                $status = 'موجود لكن غير قابل للكتابة';
                            } else {
                                $class = 'check-fail';
                                $icon = '❌';
                                $status = 'غير موجود';
                            }
                            
                            echo "<div class='check-item $class'>$icon <strong>$dir:</strong> $desc - $status</div>";
                        }
                        ?>
                        
                        <hr>
                        
                        <!-- الروابط السريعة -->
                        <h5><i class="fas fa-link"></i> الروابط السريعة</h5>
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <a href="/" class="btn btn-primary w-100">
                                    <i class="fas fa-home"></i> الصفحة الرئيسية
                                </a>
                            </div>
                            <div class="col-md-6 mb-2">
                                <a href="/install/install.php" class="btn btn-success w-100">
                                    <i class="fas fa-cog"></i> تثبيت الموقع
                                </a>
                            </div>
                            <div class="col-md-6 mb-2">
                                <a href="/pages/home.php" class="btn btn-info w-100">
                                    <i class="fas fa-store"></i> موقع حراجنا
                                </a>
                            </div>
                            <div class="col-md-6 mb-2">
                                <a href="/admin/index.php" class="btn btn-warning w-100">
                                    <i class="fas fa-user-shield"></i> لوحة الإدارة
                                </a>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <!-- معلومات إضافية -->
                        <h5><i class="fas fa-chart-bar"></i> معلومات إضافية</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="check-item check-pass">
                                    <strong>💾 الذاكرة المتاحة:</strong> <?= ini_get('memory_limit') ?>
                                </div>
                                <div class="check-item check-pass">
                                    <strong>📤 حد رفع الملفات:</strong> <?= ini_get('upload_max_filesize') ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="check-item check-pass">
                                    <strong>⏱️ مهلة التنفيذ:</strong> <?= ini_get('max_execution_time') ?> ثانية
                                </div>
                                <div class="check-item check-pass">
                                    <strong>📊 حد POST:</strong> <?= ini_get('post_max_size') ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-4">
                            <h6><i class="fas fa-lightbulb"></i> نصائح:</h6>
                            <ul class="mb-0">
                                <li>إذا كانت جميع الفحوصات ✅، يمكنك البدء في استخدام الموقع</li>
                                <li>ابدأ بزيارة صفحة التثبيت لإعداد قاعدة البيانات</li>
                                <li>تأكد من تشغيل MySQL قبل التثبيت</li>
                                <li>استخدم بيانات الاختبار: admin / password</li>
                            </ul>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
