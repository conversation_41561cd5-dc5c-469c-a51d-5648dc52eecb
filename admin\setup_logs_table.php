<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدمن
requireAdmin();

try {
    // حذف الجدول إذا كان موجوداً لإعادة إنشائه بالشكل الصحيح
    $db->query("DROP TABLE IF EXISTS activity_logs");

    // إنشاء جدول سجلات النشاط
    $db->query("
        CREATE TABLE activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NULL,
            action_type ENUM('login', 'logout', 'create', 'update', 'delete', 'admin', 'view', 'other') NOT NULL,
            description TEXT NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user (user_id),
            INDEX idx_action (action_type),
            INDEX idx_created (created_at),
            INDEX idx_ip (ip_address)
        )
    ");
    
    // التحقق من وجود سجلات
    $existing_logs = $db->fetch("SELECT COUNT(*) as count FROM activity_logs")['count'];
    
    if ($existing_logs == 0) {
        // إدراج بعض السجلات التجريبية
        $sample_logs = [
            [
                'user_id' => 1,
                'action_type' => 'login',
                'description' => 'تسجيل دخول المدير',
                'ip_address' => '127.0.0.1'
            ],
            [
                'user_id' => 1,
                'action_type' => 'admin',
                'description' => 'دخول لوحة الإدارة',
                'ip_address' => '127.0.0.1'
            ],
            [
                'user_id' => 1,
                'action_type' => 'create',
                'description' => 'إنشاء فئة جديدة',
                'ip_address' => '127.0.0.1'
            ],
            [
                'user_id' => 1,
                'action_type' => 'update',
                'description' => 'تحديث إعدادات الموقع',
                'ip_address' => '127.0.0.1'
            ],
            [
                'user_id' => 1,
                'action_type' => 'delete',
                'description' => 'حذف إعلان مخالف',
                'ip_address' => '127.0.0.1'
            ],
            [
                'user_id' => 1,
                'action_type' => 'view',
                'description' => 'عرض التقارير',
                'ip_address' => '127.0.0.1'
            ],
            [
                'user_id' => 1,
                'action_type' => 'admin',
                'description' => 'مراجعة تقرير مستخدم',
                'ip_address' => '127.0.0.1'
            ],
            [
                'user_id' => 1,
                'action_type' => 'update',
                'description' => 'تحديث حالة إعلان',
                'ip_address' => '127.0.0.1'
            ]
        ];
        
        foreach ($sample_logs as $log) {
            $db->query(
                "INSERT INTO activity_logs (user_id, action_type, description, ip_address, user_agent, created_at) VALUES (?, ?, ?, ?, ?, ?)",
                [
                    $log['user_id'],
                    $log['action_type'],
                    $log['description'],
                    $log['ip_address'],
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'))
                ]
            );
        }
        
        echo "تم إنشاء جدول سجلات النشاط وإدراج البيانات التجريبية بنجاح!";
    } else {
        echo "جدول سجلات النشاط موجود مسبقاً ويحتوي على {$existing_logs} سجل.";
    }
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
