<?php
// إنشاء قاعدة البيانات المحلية
try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $pdo = new PDO('mysql:host=localhost;charset=utf8mb4', 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS harajuna CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ تم إنشاء قاعدة البيانات 'harajuna' بنجاح<br>";
    
    // الاتصال بقاعدة البيانات الجديدة
    $pdo->exec("USE harajuna");
    
    // قراءة ملف SQL
    $sql = file_get_contents('install/database.sql');
    
    if ($sql) {
        // تقسيم الاستعلامات
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                try {
                    $pdo->exec($statement);
                } catch (PDOException $e) {
                    echo "⚠️ تحذير في الاستعلام: " . $e->getMessage() . "<br>";
                }
            }
        }
        
        echo "✅ تم تنفيذ جميع استعلامات قاعدة البيانات<br>";
    } else {
        echo "❌ لم يتم العثور على ملف database.sql<br>";
    }
    
    echo "<hr>";
    echo "<h3>🎉 تم إعداد قاعدة البيانات بنجاح!</h3>";
    echo "<p>يمكنك الآن استخدام الموقع:</p>";
    echo "<ul>";
    echo "<li><a href='pages/home.php'>الصفحة الرئيسية</a></li>";
    echo "<li><a href='pages/login.php'>تسجيل الدخول</a></li>";
    echo "<li><a href='pages/register.php'>إنشاء حساب</a></li>";
    echo "<li><a href='admin/index.php'>لوحة الإدارة</a></li>";
    echo "</ul>";
    
    echo "<h4>بيانات الإدمن الافتراضي:</h4>";
    echo "<ul>";
    echo "<li><strong>اسم المستخدم:</strong> admin</li>";
    echo "<li><strong>كلمة المرور:</strong> password</li>";
    echo "<li><strong>البريد الإلكتروني:</strong> <EMAIL></li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "❌ خطأ في إنشاء قاعدة البيانات: " . $e->getMessage() . "<br>";
    echo "<p>تأكد من أن MySQL يعمل وأن المستخدم 'root' لديه صلاحيات إنشاء قواعد البيانات.</p>";
}
?>
