<?php
// إنشاء جدول الإعلانات العامة
try {
    $pdo = new PDO('mysql:host=localhost;dbname=harajuna;charset=utf8mb4', 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // إنشاء جدول الإعلانات العامة
    $sql = "
    CREATE TABLE IF NOT EXISTS announcements (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(200) NOT NULL,
        content TEXT NOT NULL,
        type ENUM('info', 'warning', 'success', 'danger') DEFAULT 'info',
        is_active BOOLEAN DEFAULT TRUE,
        show_on_homepage BOOLEAN DEFAULT TRUE,
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_is_active (is_active),
        INDEX idx_show_on_homepage (show_on_homepage),
        INDEX idx_created_at (created_at),
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول الإعلانات العامة بنجاح<br>";
    
    // إضافة إعلان تجريبي
    $pdo->exec("
        INSERT INTO announcements (title, content, type, created_by) 
        VALUES ('مرحباً بكم في حراجنا', 'نرحب بجميع المستخدمين في منصة حراجنا للإعلانات المبوبة', 'info', 1)
    ");
    echo "✅ تم إضافة إعلان تجريبي<br>";
    
    echo "<hr>";
    echo "<h3>🎉 تم إعداد نظام الإعلانات العامة!</h3>";
    echo "<p>يمكنك الآن:</p>";
    echo "<ul>";
    echo "<li><a href='admin/announcements.php'>إدارة الإعلانات العامة</a></li>";
    echo "<li><a href='pages/home.php'>مشاهدة الإعلانات في الصفحة الرئيسية</a></li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
}
?>
