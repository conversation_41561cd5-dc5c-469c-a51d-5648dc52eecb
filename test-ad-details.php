<?php
session_start();

echo "<h2>🧪 اختبار صفحة تفاصيل الإعلان</h2>";

try {
    // تعيين مسار الجذر
    $root_path = __DIR__;

    require_once $root_path . '/config/database.php';
    require_once $root_path . '/config/config.php';
    require_once $root_path . '/includes/functions.php';
    require_once $root_path . '/includes/auth.php';

    echo "✅ تم تحميل جميع الملفات المطلوبة<br>";

    // اختبار الاتصال بقاعدة البيانات
    if (isset($db)) {
        echo "✅ الاتصال بقاعدة البيانات يعمل<br>";
    } else {
        echo "❌ مشكلة في الاتصال بقاعدة البيانات<br>";
        exit();
    }

    // اختبار وجود إعلان
    $ad_id = 1;
    $ad = $db->fetch("
        SELECT a.*, u.username, u.full_name, u.phone as user_phone, u.city as user_city, u.email as user_email,
               c.name as category_name, c.slug as category_slug,
               (SELECT COUNT(*) FROM favorites WHERE ad_id = a.id) as favorites_count
        FROM ads a
        JOIN users u ON a.user_id = u.id
        JOIN categories c ON a.category_id = c.id
        WHERE a.id = ? AND a.status = 'active'
    ", [$ad_id]);

    if ($ad) {
        echo "✅ الإعلان موجود: " . $ad['title'] . "<br>";
        echo "✅ البائع: " . $ad['full_name'] . "<br>";
        echo "✅ الفئة: " . $ad['category_name'] . "<br>";
    } else {
        echo "❌ الإعلان غير موجود أو غير نشط<br>";
        
        // إنشاء إعلان تجريبي
        echo "<h3>إنشاء إعلان تجريبي...</h3>";
        
        // التحقق من وجود مستخدم
        $user = $db->fetch("SELECT id FROM users LIMIT 1");
        if (!$user) {
            // إنشاء مستخدم تجريبي
            $db->query("INSERT INTO users (username, email, password, full_name, phone, city, user_type, is_active, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())", 
                      ['testuser', '<EMAIL>', password_hash('123456', PASSWORD_DEFAULT), 'مستخدم تجريبي', '0501234567', 'الرياض', 'user', 1]);
            $user_id = $db->lastInsertId();
            echo "✅ تم إنشاء مستخدم تجريبي<br>";
        } else {
            $user_id = $user['id'];
        }
        
        // التحقق من وجود فئة
        $category = $db->fetch("SELECT id FROM categories LIMIT 1");
        if (!$category) {
            // إنشاء فئة تجريبية
            $db->query("INSERT INTO categories (name, slug, description, is_active, created_at) VALUES (?, ?, ?, ?, NOW())", 
                      ['فئة تجريبية', 'test-category', 'فئة للاختبار', 1]);
            $category_id = $category['id'];
            echo "✅ تم إنشاء فئة تجريبية<br>";
        } else {
            $category_id = $category['id'];
        }
        
        // إنشاء إعلان تجريبي
        $db->query("INSERT INTO ads (user_id, category_id, title, description, price, price_type, condition_type, city, region, contact_phone, contact_email, status, expires_at, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL 30 DAY), NOW())", 
                  [$user_id, $category_id, 'إعلان تجريبي للاختبار', 'هذا إعلان تجريبي لاختبار الموقع', 1000, 'fixed', 'new', 'الرياض', 'الرياض', '0501234567', '<EMAIL>', 'active']);
        
        echo "✅ تم إنشاء إعلان تجريبي<br>";
        
        $ad = $db->fetch("
            SELECT a.*, u.username, u.full_name, u.phone as user_phone, u.city as user_city, u.email as user_email,
                   c.name as category_name, c.slug as category_slug,
                   (SELECT COUNT(*) FROM favorites WHERE ad_id = a.id) as favorites_count
            FROM ads a
            JOIN users u ON a.user_id = u.id
            JOIN categories c ON a.category_id = c.id
            WHERE a.id = ? AND a.status = 'active'
        ", [$db->lastInsertId()]);
        
        if ($ad) {
            echo "✅ الإعلان التجريبي جاهز: " . $ad['title'] . "<br>";
            $ad_id = $ad['id'];
        }
    }

    // اختبار الجداول المطلوبة
    echo "<h3>📊 اختبار الجداول:</h3>";
    
    $tables = ['reports', 'messages', 'favorites'];
    foreach ($tables as $table) {
        try {
            $count = $db->fetch("SELECT COUNT(*) as count FROM $table")['count'];
            echo "✅ جدول $table: $count سجل<br>";
        } catch (Exception $e) {
            echo "❌ جدول $table: خطأ - " . $e->getMessage() . "<br>";
        }
    }

    // اختبار المفضلة
    echo "<h3>💖 اختبار المفضلة:</h3>";
    if (isset($_SESSION['user_id'])) {
        $favorite = $db->fetch("SELECT id FROM favorites WHERE user_id = ? AND ad_id = ?", [$_SESSION['user_id'], $ad_id]);
        echo $favorite ? "✅ الإعلان في المفضلة<br>" : "ℹ️ الإعلان ليس في المفضلة<br>";
    } else {
        echo "ℹ️ يجب تسجيل الدخول لاختبار المفضلة<br>";
    }

    echo "<hr>";
    echo "<h3>🔗 روابط الاختبار:</h3>";
    echo "<ul>";
    echo "<li><a href='pages/ad-details.php?id=$ad_id' target='_blank'>صفحة تفاصيل الإعلان</a></li>";
    echo "<li><a href='pages/login.php' target='_blank'>تسجيل الدخول</a></li>";
    echo "<li><a href='pages/register.php' target='_blank'>إنشاء حساب</a></li>";
    echo "<li><a href='pages/messages.php' target='_blank'>الرسائل</a></li>";
    echo "</ul>";

    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724;'>🎉 الاختبار مكتمل!</h3>";
    echo "<p style='color: #155724;'>يمكنك الآن اختبار صفحة تفاصيل الإعلان والميزات المختلفة.</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ: " . $e->getMessage() . "</div>";
    echo "<div style='color: red;'>📍 الملف: " . $e->getFile() . "</div>";
    echo "<div style='color: red;'>📍 السطر: " . $e->getLine() . "</div>";
}

// إضافة CSS للتنسيق
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
ul { margin: 10px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
