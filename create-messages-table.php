<?php
// إنشاء جدول الرسائل
try {
    $pdo = new PDO('mysql:host=localhost;dbname=harajuna;charset=utf8mb4', 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // إنشاء جدول الرسائل
    $sql = "
    CREATE TABLE IF NOT EXISTS messages (
        id INT PRIMARY KEY AUTO_INCREMENT,
        sender_id INT NOT NULL,
        receiver_id INT NOT NULL,
        ad_id INT NULL,
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_sender_id (sender_id),
        INDEX idx_receiver_id (receiver_id),
        INDEX idx_ad_id (ad_id),
        INDEX idx_created_at (created_at),
        FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
        <PERSON>OREI<PERSON><PERSON> KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE SET NULL
    )";
    
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول الرسائل بنجاح<br>";
    
    echo "<hr>";
    echo "<h3>🎉 تم إعداد جدول الرسائل!</h3>";
    echo "<p>يمكنك الآن استخدام نظام الرسائل:</p>";
    echo "<ul>";
    echo "<li><a href='pages/messages.php'>صفحة الرسائل</a></li>";
    echo "<li><a href='pages/ad-details.php?id=1'>اختبار إرسال رسالة</a></li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
}
?>
