<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// إنشاء كائن المصادقة
$auth = new Auth($db);

// التحقق من صلاحيات الإدمن
requireAdmin();

$error = '';
$success = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        $user_id = (int)$_POST['user_id'];
        
        switch ($action) {
            case 'activate':
                $db->query("UPDATE users SET is_active = 1 WHERE id = ?", [$user_id]);
                $success = 'تم تفعيل المستخدم بنجاح';
                break;
                
            case 'deactivate':
                $db->query("UPDATE users SET is_active = 0 WHERE id = ?", [$user_id]);
                $success = 'تم إلغاء تفعيل المستخدم';
                break;
                
            case 'make_admin':
                $db->query("UPDATE users SET user_type = 'admin' WHERE id = ?", [$user_id]);
                $success = 'تم منح صلاحيات الإدمن للمستخدم';
                break;
                
            case 'remove_admin':
                $db->query("UPDATE users SET user_type = 'user' WHERE id = ?", [$user_id]);
                $success = 'تم إزالة صلاحيات الإدمن من المستخدم';
                break;
                
            case 'delete':
                // حذف الإعلانات أولاً
                $db->query("DELETE FROM ads WHERE user_id = ?", [$user_id]);
                // حذف المستخدم
                $db->query("DELETE FROM users WHERE id = ?", [$user_id]);
                $success = 'تم حذف المستخدم وجميع إعلاناته';
                break;
        }
        
        header('Location: users.php');
        exit();
    }
}

// فلترة المستخدمين
$filter = isset($_GET['filter']) ? sanitize($_GET['filter']) : 'all';
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$items_per_page = 20;
$offset = ($page - 1) * $items_per_page;

$where_conditions = [];
$params = [];

if ($filter !== 'all') {
    switch ($filter) {
        case 'active':
            $where_conditions[] = "is_active = 1";
            break;
        case 'inactive':
            $where_conditions[] = "is_active = 0";
            break;
        case 'admin':
            $where_conditions[] = "user_type = 'admin'";
            break;
    }
}

if (!empty($search)) {
    $where_conditions[] = "(full_name LIKE ? OR username LIKE ? OR email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب المستخدمين
$sql = "
    SELECT u.*, 
           (SELECT COUNT(*) FROM ads WHERE user_id = u.id) as ads_count,
           (SELECT COUNT(*) FROM ads WHERE user_id = u.id AND status = 'active') as active_ads_count
    FROM users u
    $where_clause
    ORDER BY u.created_at DESC
    LIMIT $items_per_page OFFSET $offset
";

$users = $db->fetchAll($sql, $params);

// عدد النتائج الإجمالي
$count_sql = "SELECT COUNT(*) as total FROM users u $where_clause";
$total_users = $db->fetch($count_sql, $params)['total'];
$total_pages = ceil($total_users / $items_per_page);

// إحصائيات سريعة
$stats = [
    'total' => $db->fetch("SELECT COUNT(*) as count FROM users")['count'],
    'active' => $db->fetch("SELECT COUNT(*) as count FROM users WHERE is_active = 1")['count'],
    'inactive' => $db->fetch("SELECT COUNT(*) as count FROM users WHERE is_active = 0")['count'],
    'admin' => $db->fetch("SELECT COUNT(*) as count FROM users WHERE user_type = 'admin'")['count'],
];

$page_title = 'إدارة المستخدمين';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة المستخدمين</h1>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?= $error ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?= $success ?>
                </div>
            <?php endif; ?>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4><?= number_format($stats['total']) ?></h4>
                            <p class="mb-0">إجمالي المستخدمين</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4><?= number_format($stats['active']) ?></h4>
                            <p class="mb-0">نشط</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h4><?= number_format($stats['inactive']) ?></h4>
                            <p class="mb-0">غير نشط</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h4><?= number_format($stats['admin']) ?></h4>
                            <p class="mb-0">إدمن</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?= htmlspecialchars($search) ?>" placeholder="البحث...">
                                <label for="search">البحث في المستخدمين</label>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="form-floating">
                                <select class="form-select" id="filter" name="filter">
                                    <option value="all" <?= $filter === 'all' ? 'selected' : '' ?>>جميع المستخدمين</option>
                                    <option value="active" <?= $filter === 'active' ? 'selected' : '' ?>>نشط</option>
                                    <option value="inactive" <?= $filter === 'inactive' ? 'selected' : '' ?>>غير نشط</option>
                                    <option value="admin" <?= $filter === 'admin' ? 'selected' : '' ?>>إدمن</option>
                                </select>
                                <label for="filter">فلتر</label>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary h-100 w-100">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول المستخدمين -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">قائمة المستخدمين (<?= number_format($total_users) ?> مستخدم)</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($users)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>المستخدم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>النوع</th>
                                        <th>الحالة</th>
                                        <th>الإعلانات</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold"><?= $user['full_name'] ?></div>
                                                <small class="text-muted">@<?= $user['username'] ?></small>
                                                <?php if ($user['phone']): ?>
                                                    <br><small class="text-muted"><?= $user['phone'] ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= $user['email'] ?></td>
                                            <td>
                                                <span class="badge bg-<?= $user['user_type'] === 'admin' ? 'danger' : 'primary' ?>">
                                                    <?= $user['user_type'] === 'admin' ? 'إدمن' : 'مستخدم' ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $user['is_active'] ? 'success' : 'secondary' ?>">
                                                    <?= $user['is_active'] ? 'نشط' : 'غير نشط' ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= $user['ads_count'] ?></span>
                                                <?php if ($user['active_ads_count'] > 0): ?>
                                                    <br><small class="text-success"><?= $user['active_ads_count'] ?> نشط</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div><?= date('Y-m-d', strtotime($user['created_at'])) ?></div>
                                                <small class="text-muted"><?= date('H:i', strtotime($user['created_at'])) ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary view-user-btn" 
                                                            data-user='<?= json_encode($user) ?>'>
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    
                                                    <?php if ($user['is_active']): ?>
                                                        <button class="btn btn-outline-warning action-btn" 
                                                                data-action="deactivate" 
                                                                data-id="<?= $user['id'] ?>"
                                                                data-name="<?= $user['full_name'] ?>">
                                                            <i class="fas fa-ban"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <button class="btn btn-outline-success action-btn" 
                                                                data-action="activate" 
                                                                data-id="<?= $user['id'] ?>"
                                                                data-name="<?= $user['full_name'] ?>">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($user['user_type'] !== 'admin'): ?>
                                                        <button class="btn btn-outline-info action-btn" 
                                                                data-action="make_admin" 
                                                                data-id="<?= $user['id'] ?>"
                                                                data-name="<?= $user['full_name'] ?>">
                                                            <i class="fas fa-user-shield"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <button class="btn btn-outline-secondary action-btn" 
                                                                data-action="remove_admin" 
                                                                data-id="<?= $user['id'] ?>"
                                                                data-name="<?= $user['full_name'] ?>">
                                                            <i class="fas fa-user"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    
                                                    <button class="btn btn-outline-danger action-btn" 
                                                            data-action="delete" 
                                                            data-id="<?= $user['id'] ?>"
                                                            data-name="<?= $user['full_name'] ?>">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- الصفحات -->
                        <?php if ($total_pages > 1): ?>
                        <nav aria-label="صفحات المستخدمين" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>">السابق</a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                        <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"><?= $i ?></a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>">التالي</a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>

                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5>لا يوجد مستخدمين</h5>
                            <p class="text-muted">لا يوجد مستخدمين يطابقون معايير البحث</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // إجراءات المستخدمين
    document.querySelectorAll('.action-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const action = this.getAttribute('data-action');
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            
            let message = '';
            switch (action) {
                case 'activate':
                    message = `هل تريد تفعيل المستخدم "${name}"؟`;
                    break;
                case 'deactivate':
                    message = `هل تريد إلغاء تفعيل المستخدم "${name}"؟`;
                    break;
                case 'make_admin':
                    message = `هل تريد منح صلاحيات الإدمن للمستخدم "${name}"؟`;
                    break;
                case 'remove_admin':
                    message = `هل تريد إزالة صلاحيات الإدمن من المستخدم "${name}"؟`;
                    break;
                case 'delete':
                    message = `هل تريد حذف المستخدم "${name}" وجميع إعلاناته؟ هذا الإجراء لا يمكن التراجع عنه!`;
                    break;
            }
            
            if (confirm(message)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="${action}">
                    <input type="hidden" name="user_id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
