<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// إنشاء كائن المصادقة
$auth = new Auth($db);

// إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل
if (isLoggedIn()) {
    header('Location: home.php');
    exit();
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = [
        'username' => sanitize($_POST['username'] ?? ''),
        'email' => sanitize($_POST['email'] ?? ''),
        'password' => $_POST['password'] ?? '',
        'confirm_password' => $_POST['confirm_password'] ?? '',
        'full_name' => sanitize($_POST['full_name'] ?? ''),
        'phone' => sanitize($_POST['phone'] ?? ''),
        'city' => sanitize($_POST['city'] ?? ''),
        'region' => sanitize($_POST['region'] ?? ''),
    ];
    
    // التحقق من صحة البيانات
    if (empty($data['username']) || empty($data['email']) || empty($data['password']) || empty($data['full_name'])) {
        $error = 'الحقول المطلوبة يجب ملؤها';
    } elseif ($data['password'] !== $data['confirm_password']) {
        $error = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
    } elseif (strlen($data['password']) < 6) {
        $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } elseif (!validateEmail($data['email'])) {
        $error = 'البريد الإلكتروني غير صحيح';
    } elseif (!empty($data['phone']) && !validatePhone($data['phone'])) {
        $error = 'رقم الهاتف غير صحيح';
    } else {
        $result = $auth->register($data);
        
        if ($result['success']) {
            $success = $result['message'];
            // مسح البيانات بعد التسجيل الناجح
            $data = [];
        } else {
            $error = $result['message'];
        }
    }
}

$page_title = 'إنشاء حساب جديد';
include 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-lg border-0 rounded-lg mt-5">
                <div class="card-header">
                    <h3 class="text-center font-weight-light my-4">
                        <i class="fas fa-user-plus"></i> إنشاء حساب جديد
                    </h3>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i> <?= $error ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> <?= $success ?>
                            <div class="mt-2">
                                <a href="login.php" class="btn btn-sm btn-success">تسجيل الدخول الآن</a>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" id="registerForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="username" name="username" 
                                           placeholder="اسم المستخدم" 
                                           value="<?= htmlspecialchars($data['username'] ?? '') ?>" required>
                                    <label for="username">اسم المستخدم *</label>
                                    <div class="form-text">يجب أن يكون 3 أحرف على الأقل</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="email" name="email" 
                                           placeholder="البريد الإلكتروني" 
                                           value="<?= htmlspecialchars($data['email'] ?? '') ?>" required>
                                    <label for="email">البريد الإلكتروني *</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                   placeholder="الاسم الكامل" 
                                   value="<?= htmlspecialchars($data['full_name'] ?? '') ?>" required>
                            <label for="full_name">الاسم الكامل *</label>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="password" class="form-control" id="password" name="password" 
                                           placeholder="كلمة المرور" required>
                                    <label for="password">كلمة المرور *</label>
                                    <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                           placeholder="تأكيد كلمة المرور" required>
                                    <label for="confirm_password">تأكيد كلمة المرور *</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   placeholder="رقم الهاتف" 
                                   value="<?= htmlspecialchars($data['phone'] ?? '') ?>">
                            <label for="phone">رقم الهاتف (اختياري)</label>
                            <div class="form-text">مثال: 0501234567</div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="region" name="region">
                                        <option value="">اختر المنطقة</option>
                                        <?php
                                        global $regions;
                                        foreach ($regions as $region => $cities):
                                        ?>
                                            <option value="<?= $region ?>" <?= ($data['region'] ?? '') === $region ? 'selected' : '' ?>>
                                                <?= $region ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <label for="region">المنطقة</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="city" name="city">
                                        <option value="">اختر المدينة</option>
                                    </select>
                                    <label for="city">المدينة</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="terms" required>
                            <label class="form-check-label" for="terms">
                                أوافق على <a href="terms.php" target="_blank">شروط الاستخدام</a> و <a href="privacy.php" target="_blank">سياسة الخصوصية</a>
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus"></i> إنشاء الحساب
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <div class="small">
                        لديك حساب بالفعل؟ <a href="login.php">تسجيل الدخول</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// بيانات المناطق والمدن
const regions = <?= json_encode($regions) ?>;

document.addEventListener('DOMContentLoaded', function() {
    const regionSelect = document.getElementById('region');
    const citySelect = document.getElementById('city');
    const form = document.getElementById('registerForm');
    
    // تحديث المدن عند تغيير المنطقة
    regionSelect.addEventListener('change', function() {
        const selectedRegion = this.value;
        citySelect.innerHTML = '<option value="">اختر المدينة</option>';
        
        if (selectedRegion && regions[selectedRegion]) {
            regions[selectedRegion].forEach(function(city) {
                const option = document.createElement('option');
                option.value = city;
                option.textContent = city;
                citySelect.appendChild(option);
            });
        }
    });
    
    // تحديد المدينة المحفوظة
    const savedCity = '<?= $data['city'] ?? '' ?>';
    if (savedCity) {
        regionSelect.dispatchEvent(new Event('change'));
        setTimeout(() => {
            citySelect.value = savedCity;
        }, 100);
    }
    
    // التحقق من صحة النموذج
    form.addEventListener('submit', function(e) {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        const username = document.getElementById('username').value.trim();
        const email = document.getElementById('email').value.trim();
        const fullName = document.getElementById('full_name').value.trim();
        const phone = document.getElementById('phone').value.trim();
        
        // التحقق من كلمة المرور
        if (password !== confirmPassword) {
            e.preventDefault();
            showNotification('كلمة المرور وتأكيد كلمة المرور غير متطابقتين', 'error');
            return;
        }
        
        if (password.length < 6) {
            e.preventDefault();
            showNotification('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
            return;
        }
        
        // التحقق من اسم المستخدم
        if (username.length < 3) {
            e.preventDefault();
            showNotification('اسم المستخدم يجب أن يكون 3 أحرف على الأقل', 'error');
            return;
        }
        
        // التحقق من البريد الإلكتروني
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            showNotification('البريد الإلكتروني غير صحيح', 'error');
            return;
        }
        
        // التحقق من رقم الهاتف
        if (phone && !/^(05|5)[0-9]{8}$/.test(phone)) {
            e.preventDefault();
            showNotification('رقم الهاتف غير صحيح', 'error');
            return;
        }
        
        // إظهار مؤشر التحميل
        const submitButton = form.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إنشاء الحساب...';
    });
    
    // التحقق من قوة كلمة المرور
    const passwordInput = document.getElementById('password');
    const strengthIndicator = document.createElement('div');
    strengthIndicator.className = 'password-strength mt-1';
    passwordInput.parentElement.appendChild(strengthIndicator);
    
    passwordInput.addEventListener('input', function() {
        const password = this.value;
        let strength = 0;
        let feedback = [];
        
        if (password.length >= 6) strength++;
        else feedback.push('6 أحرف على الأقل');
        
        if (/[a-z]/.test(password)) strength++;
        else feedback.push('حرف صغير');
        
        if (/[A-Z]/.test(password)) strength++;
        else feedback.push('حرف كبير');
        
        if (/[0-9]/.test(password)) strength++;
        else feedback.push('رقم');
        
        if (/[^a-zA-Z0-9]/.test(password)) strength++;
        else feedback.push('رمز خاص');
        
        let strengthText = '';
        let strengthClass = '';
        
        if (strength < 2) {
            strengthText = 'ضعيفة';
            strengthClass = 'text-danger';
        } else if (strength < 4) {
            strengthText = 'متوسطة';
            strengthClass = 'text-warning';
        } else {
            strengthText = 'قوية';
            strengthClass = 'text-success';
        }
        
        strengthIndicator.innerHTML = `
            <small class="${strengthClass}">
                قوة كلمة المرور: ${strengthText}
                ${feedback.length > 0 ? '<br>يُنصح بإضافة: ' + feedback.join(', ') : ''}
            </small>
        `;
    });
    
    // التحقق من تطابق كلمة المرور
    const confirmPasswordInput = document.getElementById('confirm_password');
    confirmPasswordInput.addEventListener('input', function() {
        const password = passwordInput.value;
        const confirmPassword = this.value;
        
        if (confirmPassword && password !== confirmPassword) {
            this.setCustomValidity('كلمة المرور غير متطابقة');
            this.classList.add('is-invalid');
        } else {
            this.setCustomValidity('');
            this.classList.remove('is-invalid');
        }
    });
    
    // التحقق من توفر اسم المستخدم
    const usernameInput = document.getElementById('username');
    let usernameTimeout;
    
    usernameInput.addEventListener('input', function() {
        clearTimeout(usernameTimeout);
        const username = this.value.trim();
        
        if (username.length >= 3) {
            usernameTimeout = setTimeout(() => {
                fetch('ajax/check-username.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({username: username})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.exists) {
                        usernameInput.setCustomValidity('اسم المستخدم مستخدم بالفعل');
                        usernameInput.classList.add('is-invalid');
                    } else {
                        usernameInput.setCustomValidity('');
                        usernameInput.classList.remove('is-invalid');
                        usernameInput.classList.add('is-valid');
                    }
                });
            }, 500);
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
