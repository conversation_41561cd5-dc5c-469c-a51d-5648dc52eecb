<?php
session_start();

echo "<h1>🔧 اختبار نهائي شامل - نظام الرسائل</h1>";

try {
    $pdo = new PDO('mysql:host=localhost;dbname=harajuna;charset=utf8mb4', 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<h2>✅ قاعدة البيانات متصلة</h2>";
    
    // التحقق من جدول الرسائل
    try {
        $messages_count = $pdo->query("SELECT COUNT(*) as count FROM messages")->fetch()['count'];
        echo "✅ جدول الرسائل يعمل - عدد الرسائل: $messages_count<br>";
    } catch (Exception $e) {
        echo "❌ مشكلة في جدول الرسائل - إنشاء الجدول...<br>";
        
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS messages (
                id INT PRIMARY KEY AUTO_INCREMENT,
                sender_id INT NOT NULL,
                receiver_id INT NOT NULL,
                ad_id INT NULL,
                message TEXT NOT NULL,
                is_read BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_sender_id (sender_id),
                INDEX idx_receiver_id (receiver_id),
                INDEX idx_ad_id (ad_id),
                INDEX idx_created_at (created_at)
            )
        ");
        echo "✅ تم إنشاء جدول الرسائل<br>";
    }
    
    // التحقق من المستخدمين
    $users = $pdo->query("SELECT id, full_name, username FROM users LIMIT 5")->fetchAll();
    echo "✅ عدد المستخدمين: " . count($users) . "<br>";
    
    if (count($users) < 2) {
        echo "🔧 إنشاء مستخدمين تجريبيين...<br>";
        
        // إنشاء مستخدمين تجريبيين
        $pdo->exec("INSERT INTO users (username, email, password, full_name, phone, city, user_type, is_active, created_at) VALUES 
                   ('testuser1', '<EMAIL>', '" . password_hash('123456', PASSWORD_DEFAULT) . "', 'أحمد محمد', '0501111111', 'الرياض', 'user', 1, NOW()),
                   ('testuser2', '<EMAIL>', '" . password_hash('123456', PASSWORD_DEFAULT) . "', 'فاطمة علي', '0502222222', 'جدة', 'user', 1, NOW())");
        
        echo "✅ تم إنشاء مستخدمين تجريبيين<br>";
        $users = $pdo->query("SELECT id, full_name, username FROM users LIMIT 5")->fetchAll();
    }
    
    // إنشاء رسائل تجريبية
    if (count($users) >= 2) {
        $existing_messages = $pdo->query("SELECT COUNT(*) as count FROM messages")->fetch()['count'];
        
        if ($existing_messages < 5) {
            echo "🔧 إنشاء رسائل تجريبية...<br>";
            
            $user1_id = $users[0]['id'];
            $user2_id = $users[1]['id'];
            
            $sample_messages = [
                [$user1_id, $user2_id, 'مرحباً، كيف حالك؟'],
                [$user2_id, $user1_id, 'مرحباً بك، أنا بخير والحمد لله'],
                [$user1_id, $user2_id, 'هل يمكنني الاستفسار عن الإعلان؟'],
                [$user2_id, $user1_id, 'بالطبع، تفضل بالسؤال'],
                [$user1_id, $user2_id, 'شكراً لك على الرد السريع']
            ];
            
            foreach ($sample_messages as $msg) {
                $pdo->prepare("INSERT INTO messages (sender_id, receiver_id, message, created_at) VALUES (?, ?, ?, NOW())")
                    ->execute($msg);
            }
            
            echo "✅ تم إنشاء رسائل تجريبية<br>";
        }
    }
    
    // اختبار الاستعلامات
    echo "<h3>📊 اختبار الاستعلامات:</h3>";
    
    // اختبار جلب المحادثات
    if (count($users) >= 2) {
        $user_id = $users[0]['id'];
        
        try {
            $conversations = $pdo->prepare("
                SELECT DISTINCT 
                    CASE 
                        WHEN sender_id = ? THEN receiver_id 
                        ELSE sender_id 
                    END as other_user_id
                FROM messages 
                WHERE sender_id = ? OR receiver_id = ?
            ");
            $conversations->execute([$user_id, $user_id, $user_id]);
            $conv_count = $conversations->rowCount();
            
            echo "✅ استعلام المحادثات يعمل - عدد المحادثات: $conv_count<br>";
        } catch (Exception $e) {
            echo "❌ خطأ في استعلام المحادثات: " . $e->getMessage() . "<br>";
        }
        
        // اختبار جلب الرسائل
        if (count($users) >= 2) {
            try {
                $messages = $pdo->prepare("
                    SELECT *, 
                           CASE WHEN sender_id = ? THEN 'sent' ELSE 'received' END as message_type
                    FROM messages 
                    WHERE (sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)
                    ORDER BY created_at ASC
                ");
                $user2_id = $users[1]['id'];
                $messages->execute([$user_id, $user_id, $user2_id, $user2_id, $user_id]);
                $msg_count = $messages->rowCount();
                
                echo "✅ استعلام الرسائل يعمل - عدد الرسائل: $msg_count<br>";
            } catch (Exception $e) {
                echo "❌ خطأ في استعلام الرسائل: " . $e->getMessage() . "<br>";
            }
        }
    }
    
    // معلومات الجلسة
    echo "<h3>👤 معلومات الجلسة:</h3>";
    if (isset($_SESSION['user_id'])) {
        $current_user = $pdo->prepare("SELECT full_name, username FROM users WHERE id = ?");
        $current_user->execute([$_SESSION['user_id']]);
        $user_info = $current_user->fetch();
        
        if ($user_info) {
            echo "✅ مسجل الدخول: " . $user_info['full_name'] . " (@" . $user_info['username'] . ")<br>";
        }
    } else {
        echo "⚠️ غير مسجل الدخول<br>";
        echo "📝 بيانات تسجيل الدخول التجريبية:<br>";
        echo "- اسم المستخدم: testuser1 أو testuser2<br>";
        echo "- كلمة المرور: 123456<br>";
    }
    
    // اختبار POST request
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_send'])) {
        echo "<h3>🧪 اختبار إرسال رسالة:</h3>";
        
        if (isset($_SESSION['user_id'])) {
            $receiver_id = (int)$_POST['receiver_id'];
            $message = $_POST['message'];
            
            try {
                $stmt = $pdo->prepare("INSERT INTO messages (sender_id, receiver_id, message, created_at) VALUES (?, ?, ?, NOW())");
                $stmt->execute([$_SESSION['user_id'], $receiver_id, $message]);
                echo "✅ تم إرسال الرسالة بنجاح!<br>";
            } catch (Exception $e) {
                echo "❌ خطأ في إرسال الرسالة: " . $e->getMessage() . "<br>";
            }
        } else {
            echo "❌ يجب تسجيل الدخول أولاً<br>";
        }
    }
    
    echo "<hr>";
    echo "<h3>🔗 روابط الاختبار:</h3>";
    echo "<div class='row'>";
    
    // روابط الصفحات
    $test_links = [
        'صفحة الرسائل الرئيسية' => 'pages/messages.php',
        'صفحة الرسائل المبسطة' => 'pages/messages-simple.php',
        'تسجيل الدخول' => 'pages/login.php',
        'إنشاء حساب' => 'pages/register.php',
        'اختبار تفاصيل الإعلان' => 'pages/ad-details.php?id=1'
    ];
    
    foreach ($test_links as $name => $url) {
        echo "<div class='col-md-6 mb-2'>";
        echo "<a href='$url' target='_blank' class='btn btn-primary w-100'>$name</a>";
        echo "</div>";
    }
    
    // روابط المحادثات المباشرة
    if (count($users) >= 2) {
        echo "<div class='col-12'><hr><h5>محادثات مباشرة:</h5></div>";
        for ($i = 0; $i < min(3, count($users)); $i++) {
            echo "<div class='col-md-4 mb-2'>";
            echo "<a href='pages/messages.php?user_id=" . $users[$i]['id'] . "' target='_blank' class='btn btn-outline-info w-100'>";
            echo "محادثة مع " . $users[$i]['full_name'];
            echo "</a>";
            echo "</div>";
        }
    }
    
    echo "</div>";
    
    // نموذج اختبار إرسال رسالة
    if (isset($_SESSION['user_id']) && count($users) >= 2) {
        echo "<hr>";
        echo "<h3>📝 اختبار إرسال رسالة:</h3>";
        echo "<form method='POST' class='row g-3'>";
        echo "<div class='col-md-4'>";
        echo "<label class='form-label'>المستقبل:</label>";
        echo "<select name='receiver_id' class='form-select' required>";
        foreach ($users as $user) {
            if ($user['id'] != $_SESSION['user_id']) {
                echo "<option value='" . $user['id'] . "'>" . $user['full_name'] . "</option>";
            }
        }
        echo "</select>";
        echo "</div>";
        echo "<div class='col-md-6'>";
        echo "<label class='form-label'>الرسالة:</label>";
        echo "<input type='text' name='message' class='form-control' value='رسالة اختبار من صفحة الاختبار' required>";
        echo "</div>";
        echo "<div class='col-md-2'>";
        echo "<label class='form-label'>&nbsp;</label>";
        echo "<button type='submit' name='test_send' class='btn btn-success w-100'>إرسال</button>";
        echo "</div>";
        echo "</form>";
    }
    
    // إحصائيات نهائية
    echo "<hr>";
    echo "<h3>📈 الإحصائيات النهائية:</h3>";
    
    $stats = [
        'إجمالي الرسائل' => $pdo->query("SELECT COUNT(*) as count FROM messages")->fetch()['count'],
        'الرسائل غير المقروءة' => $pdo->query("SELECT COUNT(*) as count FROM messages WHERE is_read = 0")->fetch()['count'],
        'المحادثات الفريدة' => $pdo->query("SELECT COUNT(DISTINCT CONCAT(LEAST(sender_id, receiver_id), '-', GREATEST(sender_id, receiver_id))) as count FROM messages")->fetch()['count'],
        'المستخدمين النشطين' => $pdo->query("SELECT COUNT(DISTINCT sender_id) as count FROM messages")->fetch()['count']
    ];
    
    echo "<div class='row'>";
    foreach ($stats as $name => $value) {
        echo "<div class='col-md-3 mb-2'>";
        echo "<div class='card text-center'>";
        echo "<div class='card-body'>";
        echo "<h4 class='text-primary'>$value</h4>";
        echo "<p class='mb-0'>$name</p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<div class='alert alert-success mt-4'>";
    echo "<h4>🎉 اختبار نظام الرسائل مكتمل!</h4>";
    echo "<ul class='mb-0'>";
    echo "<li>✅ جدول الرسائل يعمل بشكل صحيح</li>";
    echo "<li>✅ استعلامات المحادثات تعمل</li>";
    echo "<li>✅ إرسال الرسائل يعمل</li>";
    echo "<li>✅ عرض الرسائل يعمل</li>";
    echo "<li>✅ جميع الصفحات متاحة</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ خطأ في الاختبار:</h4>";
    echo "<p><strong>الرسالة:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

// إضافة Bootstrap للتنسيق
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; margin-bottom: 1rem; }
.card { margin-bottom: 1rem; }
.btn { margin: 0.25rem; }
</style>";
?>
