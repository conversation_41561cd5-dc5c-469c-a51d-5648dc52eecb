<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(dirname(__DIR__));

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول']);
    exit();
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit();
}

// قراءة البيانات
$input = json_decode(file_get_contents('php://input'), true);
if ($input) {
    $ad_id = isset($input['ad_id']) ? (int)$input['ad_id'] : 0;
} else {
    // إذا لم تكن البيانات JSON، جرب POST عادي
    $ad_id = isset($_POST['ad_id']) ? (int)$_POST['ad_id'] : 0;
}

if ($ad_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف الإعلان غير صحيح']);
    exit();
}

try {
    // التحقق من وجود الإعلان
    $ad = $db->fetch("SELECT id FROM ads WHERE id = ? AND status = 'active'", [$ad_id]);
    
    if (!$ad) {
        echo json_encode(['success' => false, 'message' => 'الإعلان غير موجود']);
        exit();
    }
    
    // التحقق من وجود المفضلة
    $favorite = $db->fetch("SELECT id FROM favorites WHERE user_id = ? AND ad_id = ?", [$_SESSION['user_id'], $ad_id]);
    
    if ($favorite) {
        // إزالة من المفضلة
        $db->query("DELETE FROM favorites WHERE user_id = ? AND ad_id = ?", [$_SESSION['user_id'], $ad_id]);
        echo json_encode(['success' => true, 'is_favorite' => false, 'added' => false, 'message' => 'تم إزالة الإعلان من المفضلة']);
    } else {
        // إضافة للمفضلة
        $db->query("INSERT INTO favorites (user_id, ad_id, created_at) VALUES (?, ?, datetime('now'))", [$_SESSION['user_id'], $ad_id]);
        echo json_encode(['success' => true, 'is_favorite' => true, 'added' => true, 'message' => 'تم إضافة الإعلان للمفضلة']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء العملية']);
}
?>
