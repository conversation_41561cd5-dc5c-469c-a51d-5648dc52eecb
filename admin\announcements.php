<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدمن
requireAdmin();

$error = '';
$success = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        switch ($action) {
            case 'add_announcement':
                $title = sanitize($_POST['title']);
                $content = sanitize($_POST['content']);
                $type = sanitize($_POST['type']);
                $show_on_homepage = isset($_POST['show_on_homepage']) ? 1 : 0;
                
                if (!empty($title) && !empty($content)) {
                    $sql = "INSERT INTO announcements (title, content, type, show_on_homepage, created_by, created_at) 
                            VALUES (?, ?, ?, ?, ?, NOW())";
                    $db->query($sql, [$title, $content, $type, $show_on_homepage, $_SESSION['user_id']]);
                    $success = 'تم إضافة الإعلان بنجاح';
                } else {
                    $error = 'العنوان والمحتوى مطلوبان';
                }
                break;
                
            case 'edit_announcement':
                $id = (int)$_POST['id'];
                $title = sanitize($_POST['title']);
                $content = sanitize($_POST['content']);
                $type = sanitize($_POST['type']);
                $is_active = isset($_POST['is_active']) ? 1 : 0;
                $show_on_homepage = isset($_POST['show_on_homepage']) ? 1 : 0;
                
                if (!empty($title) && !empty($content)) {
                    $sql = "UPDATE announcements SET title = ?, content = ?, type = ?, is_active = ?, 
                            show_on_homepage = ?, updated_at = NOW() WHERE id = ?";
                    $db->query($sql, [$title, $content, $type, $is_active, $show_on_homepage, $id]);
                    $success = 'تم تحديث الإعلان بنجاح';
                } else {
                    $error = 'العنوان والمحتوى مطلوبان';
                }
                break;
                
            case 'toggle_status':
                $id = (int)$_POST['id'];
                $db->query("UPDATE announcements SET is_active = NOT is_active WHERE id = ?", [$id]);
                $success = 'تم تغيير حالة الإعلان';
                break;
                
            case 'delete_announcement':
                $id = (int)$_POST['id'];
                $db->query("DELETE FROM announcements WHERE id = ?", [$id]);
                $success = 'تم حذف الإعلان';
                break;
        }
        
        header('Location: announcements.php');
        exit();
    }
}

// جلب الإعلانات
$announcements = $db->fetchAll("
    SELECT a.*, u.full_name as created_by_name
    FROM announcements a
    JOIN users u ON a.created_by = u.id
    ORDER BY a.created_at DESC
");

$page_title = 'إدارة الإعلانات العامة';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة الإعلانات العامة</h1>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAnnouncementModal">
                    <i class="fas fa-plus"></i> إضافة إعلان عام
                </button>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?= $error ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?= $success ?>
                </div>
            <?php endif; ?>

            <!-- جدول الإعلانات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">قائمة الإعلانات العامة</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($announcements)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>العنوان</th>
                                        <th>المحتوى</th>
                                        <th>النوع</th>
                                        <th>الحالة</th>
                                        <th>الصفحة الرئيسية</th>
                                        <th>المنشئ</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($announcements as $announcement): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold"><?= $announcement['title'] ?></div>
                                            </td>
                                            <td>
                                                <div style="max-width: 200px;">
                                                    <?= substr($announcement['content'], 0, 100) ?>...
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= 
                                                    $announcement['type'] === 'info' ? 'info' : 
                                                    ($announcement['type'] === 'warning' ? 'warning' : 
                                                    ($announcement['type'] === 'success' ? 'success' : 'danger')) 
                                                ?>">
                                                    <?php
                                                    $type_labels = [
                                                        'info' => 'معلومات',
                                                        'warning' => 'تحذير',
                                                        'success' => 'نجاح',
                                                        'danger' => 'خطر'
                                                    ];
                                                    echo $type_labels[$announcement['type']] ?? $announcement['type'];
                                                    ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $announcement['is_active'] ? 'success' : 'secondary' ?>">
                                                    <?= $announcement['is_active'] ? 'نشط' : 'غير نشط' ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $announcement['show_on_homepage'] ? 'primary' : 'light text-dark' ?>">
                                                    <?= $announcement['show_on_homepage'] ? 'يظهر' : 'لا يظهر' ?>
                                                </span>
                                            </td>
                                            <td><?= $announcement['created_by_name'] ?></td>
                                            <td>
                                                <div><?= date('Y-m-d', strtotime($announcement['created_at'])) ?></div>
                                                <small class="text-muted"><?= date('H:i', strtotime($announcement['created_at'])) ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary edit-announcement-btn" 
                                                            data-announcement='<?= json_encode($announcement) ?>'>
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-<?= $announcement['is_active'] ? 'warning' : 'success' ?> toggle-status-btn" 
                                                            data-id="<?= $announcement['id'] ?>">
                                                        <i class="fas fa-<?= $announcement['is_active'] ? 'eye-slash' : 'eye' ?>"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger delete-announcement-btn" 
                                                            data-id="<?= $announcement['id'] ?>" 
                                                            data-title="<?= $announcement['title'] ?>">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                            <h5>لا توجد إعلانات عامة</h5>
                            <p class="text-muted">لم يتم إنشاء أي إعلانات عامة بعد</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Modal إضافة إعلان -->
<div class="modal fade" id="addAnnouncementModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة إعلان عام جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="add_announcement">
                <div class="modal-body">
                    <div class="form-floating mb-3">
                        <input type="text" class="form-control" id="title" name="title" required>
                        <label for="title">عنوان الإعلان *</label>
                    </div>
                    
                    <div class="form-floating mb-3">
                        <textarea class="form-control" id="content" name="content" style="height: 120px" required></textarea>
                        <label for="content">محتوى الإعلان *</label>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="type" name="type">
                                    <option value="info">معلومات</option>
                                    <option value="warning">تحذير</option>
                                    <option value="success">نجاح</option>
                                    <option value="danger">خطر</option>
                                </select>
                                <label for="type">نوع الإعلان</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mt-3">
                                <input class="form-check-input" type="checkbox" id="show_on_homepage" name="show_on_homepage" checked>
                                <label class="form-check-label" for="show_on_homepage">
                                    عرض في الصفحة الرئيسية
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة الإعلان</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تعديل إعلان -->
<div class="modal fade" id="editAnnouncementModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الإعلان</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editAnnouncementForm">
                <input type="hidden" name="action" value="edit_announcement">
                <input type="hidden" name="id" id="edit_id">
                <div class="modal-body">
                    <div class="form-floating mb-3">
                        <input type="text" class="form-control" id="edit_title" name="title" required>
                        <label for="edit_title">عنوان الإعلان *</label>
                    </div>
                    
                    <div class="form-floating mb-3">
                        <textarea class="form-control" id="edit_content" name="content" style="height: 120px" required></textarea>
                        <label for="edit_content">محتوى الإعلان *</label>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="edit_type" name="type">
                                    <option value="info">معلومات</option>
                                    <option value="warning">تحذير</option>
                                    <option value="success">نجاح</option>
                                    <option value="danger">خطر</option>
                                </select>
                                <label for="edit_type">نوع الإعلان</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mt-3">
                                <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                                <label class="form-check-label" for="edit_is_active">
                                    نشط
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mt-3">
                                <input class="form-check-input" type="checkbox" id="edit_show_on_homepage" name="show_on_homepage">
                                <label class="form-check-label" for="edit_show_on_homepage">
                                    عرض في الصفحة الرئيسية
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تعديل إعلان
    document.querySelectorAll('.edit-announcement-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const announcement = JSON.parse(this.getAttribute('data-announcement'));
            
            document.getElementById('edit_id').value = announcement.id;
            document.getElementById('edit_title').value = announcement.title;
            document.getElementById('edit_content').value = announcement.content;
            document.getElementById('edit_type').value = announcement.type;
            document.getElementById('edit_is_active').checked = announcement.is_active == 1;
            document.getElementById('edit_show_on_homepage').checked = announcement.show_on_homepage == 1;
            
            const modal = new bootstrap.Modal(document.getElementById('editAnnouncementModal'));
            modal.show();
        });
    });
    
    // تغيير حالة الإعلان
    document.querySelectorAll('.toggle-status-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="toggle_status">
                <input type="hidden" name="id" value="${id}">
            `;
            document.body.appendChild(form);
            form.submit();
        });
    });
    
    // حذف إعلان
    document.querySelectorAll('.delete-announcement-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const title = this.getAttribute('data-title');
            
            if (confirm(`هل تريد حذف الإعلان "${title}"؟`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_announcement">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
